
<!DOCTYPE html>
<html>
    <head>
        <title>云手机查看</title>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
        <style>
            html,
            body {
                margin: 0;
                padding: 0;
                height: 100%;
            }
            .container {
                /*width: 100%;*/
                /*!*max-width: 100vw;*!*/
                /*height: 100%;*/
                /*background: black;*/
                position: absolute;
                display: block;
                height: 100vh;
                width: 100vw;
                margin: 0;
                padding: 0;
                top: 0;
                left: 0;
            }
            .sdk-switch {
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 1000;
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
            }
            .sdk-switch button {
                margin: 0 5px;
                padding: 5px 10px;
                border: none;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
            }
            .sdk-switch button.active {
                background: #07C160;
                color: white;
            }
            .sdk-switch button:not(.active) {
                background: #ccc;
                color: #333;
            }
        </style>
        <link rel="stylesheet" href="sdk/weui.min.2.5.16.css" />
        <script type="text/javascript" src="sdk/JHSDK.min.3.21.6.js"></script>
        <script src="./ld/LDSDK.min.js"></script>
        <script src="sdk/vconsole.min.3.15.1.js"></script>
        <script src="sdk/axios.min.1.9.0.js"></script>
        <script type="text/javascript" src="sdk/weui.min.1.2.21.js"></script>
        <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>

        <script>
            new VConsole();
        </script>
    </head>
    <body onunload="closePage()">
        <div id="fullscreen-container" class="fullscreen-container">
            <!-- SDK切换控制 -->
            <div class="sdk-switch">
                <span>SDK模式:</span>
                <button id="jhSdkBtn">JH SDK</button>
                <button id="ldSdkBtn" class="active">LD SDK</button>
            </div>

            <!-- 用来挂在云手机的节点 -->
            <div id="container" class="container"></div>

            <!-- 添加控制按钮 -->
            <div class="fixed bottom-4 left-0 right-0 flex justify-center gap-4" style="display: none">
                <button id="wxCloseWindow" class="bg-[#07C160] text-white px-4 py-2 rounded-lg shadow-md">
                    关闭微信页面
                </button>
                <button id="wxGetLocation" class="bg-[#07C160] text-white px-4 py-2 rounded-lg shadow-md">
                    获取地理位置
                </button>
            </div>
        </div>
        <script src="js/config.20250814_1753.js"></script>
        <script src="js/utils.20250814_1753.js"></script>
        <script src="js/wx-sdk-handler.20250814_1753.js"></script>
        <script src="js/action.20250814_1753.js"></script>
        <script src="js/ws-handler.20250814_1753.js"></script>
        <script src="js/index.20250814_1753.js"></script>
    </body>
</html>
