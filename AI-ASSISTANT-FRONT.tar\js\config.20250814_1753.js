
window.API_URL_BASE_VDI = "https://testing-vdi.byodonline.com";
window.API_URL_BASE_VG = "https://testing-vg.byodonline.com";
window.URL_PATH = "vdi-ctest/pa";

window.LOCAL_STORAGE_CACHE_KEY_UID = 'AI_ASSISTANT_UID';
window.LOCAL_STORAGE_CACHE_KEY_TOKEN = 'AI_ASSISTANT_TOKEN';
window.SESSION_STORAGE_CACHE_KEY_SENDER_ID = 'AI_ASSISTANT_SENDER_ID';

window.WX_MP_APPID = "wxae80b67f97373ec8";

window.WEBSOCKET_API_URL = "wss://testing-vg.zdgzc.com/decs-cvgs/wsforaiactl";

window.WEBSOCKET_VDI_URL = "wss://testing-vg.zdgzc.com/decs-cvgs/wsforaiactl";
// LD SDK 配置
window.LD_SDK_CONFIG = {
    // 是否使用测试地址
    USE_TEST_URL: true,
    // 测试地址
    TEST_URL: 'wss://testing-vgcph.zdgzc.com/decs-cvgs/wsforcph-test?ip=***********&port=10242',
    // libav文件路径
    LIBAV_URL: './ld/libav-*******-0a1c8f7-zzh.js'
};