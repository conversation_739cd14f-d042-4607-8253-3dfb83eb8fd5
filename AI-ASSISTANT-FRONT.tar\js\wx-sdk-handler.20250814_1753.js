
// 获取微信配置参数
async function getWxConfig() {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || ''; // 从缓存获取
    const postData = {
        v: '1.0.0',
        arg: {
            url: window.location.href,//bugfix: pageUrl弃用
        },
    };
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };
    const configUrl = `${window.API_URL_BASE_VDI}/decs-cvgs/api/aia/get-js-ticket`;

    try {
        const response = await fetch(configUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(postData),
            timeout: 10000,
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data?.errorCode === -45813) {
            const queryString = window.location.search.substring(1);
            redirect2Login('index',queryString);
            return null;
        }

        if (data?.errorCode !== 0) {
            weui.alert(data?.errorMessage || '获取微信配置失败');
            return null;
        }

        return data.data;
    } catch (error) {
        console.error('获取微信配置失败:', error);
        weui.alert(error.message || '未知错误');
        return null;
    }
}

// 初始化微信配置
async function initWxConfig(jsApiList) {
    const loading = weui.loading('系统初始化中,请稍候...');
    try {
        const config = await getWxConfig();
        if (!config) {
            throw new Error('获取配置失败');
        }

        wx.config({
            debug: false,
            appId: config.appId,
            timestamp: config.timestamp,
            nonceStr: config.nonceStr,
            signature: config.signature,
            jsApiList: jsApiList,
        });

    } catch (error) {
        console.error('初始化微信配置失败:', error);
        weui.toast(error.message || '初始化配置失败，请重试', 2000);
    } finally {
        loading.hide();
    }
}
