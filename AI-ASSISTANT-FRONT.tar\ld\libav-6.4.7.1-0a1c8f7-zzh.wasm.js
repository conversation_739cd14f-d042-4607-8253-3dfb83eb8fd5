/*!
 * libav.js 
 *
 * This software is compiled from several sources, the licenses for which are
 * included herein.
 *
 * ---
 *
 * ffmpeg:
 *
 *  Copyright (c) 2000-2024 F<PERSON><PERSON> et al
 *
 *                   GNU LESSER GENERAL PUBLIC LICENSE
 *                        Version 2.1, February 1999
 *
 *  Copyright (C) 1991, 1999 Free Software Foundation, Inc.
 *  51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 *  Everyone is permitted to copy and distribute verbatim copies
 *  of this license document, but changing it is not allowed.
 *
 * [This is the first released version of the Lesser GPL.  It also counts
 *  as the successor of the GNU Library Public License, version 2, hence
 *  the version number 2.1.]
 *
 *                             Preamble
 *
 *   The licenses for most software are designed to take away your
 * freedom to share and change it.  By contrast, the GNU General Public
 * Licenses are intended to guarantee your freedom to share and change
 * free software--to make sure the software is free for all its users.
 *
 *   This license, the Lesser General Public License, applies to some
 * specially designated software packages--typically libraries--of the
 * Free Software Foundation and other authors who decide to use it.  You
 * can use it too, but we suggest you first think carefully about whether
 * this license or the ordinary General Public License is the better
 * strategy to use in any particular case, based on the explanations below.
 *
 *   When we speak of free software, we are referring to freedom of use,
 * not price.  Our General Public Licenses are designed to make sure that
 * you have the freedom to distribute copies of free software (and charge
 * for this service if you wish); that you receive source code or can get
 * it if you want it; that you can change the software and use pieces of
 * it in new free programs; and that you are informed that you can do
 * these things.
 *
 *   To protect your rights, we need to make restrictions that forbid
 * distributors to deny you these rights or to ask you to surrender these
 * rights.  These restrictions translate to certain responsibilities for
 * you if you distribute copies of the library or if you modify it.
 *
 *   For example, if you distribute copies of the library, whether gratis
 * or for a fee, you must give the recipients all the rights that we gave
 * you.  You must make sure that they, too, receive or can get the source
 * code.  If you link other code with the library, you must provide
 * complete object files to the recipients, so that they can relink them
 * with the library after making changes to the library and recompiling
 * it.  And you must show them these terms so they know their rights.
 *
 *   We protect your rights with a two-step method: (1) we copyright the
 * library, and (2) we offer you this license, which gives you legal
 * permission to copy, distribute and/or modify the library.
 *
 *   To protect each distributor, we want to make it very clear that
 * there is no warranty for the free library.  Also, if the library is
 * modified by someone else and passed on, the recipients should know
 * that what they have is not the original version, so that the original
 * author's reputation will not be affected by problems that might be
 * introduced by others.
 *
 *   Finally, software patents pose a constant threat to the existence of
 * any free program.  We wish to make sure that a company cannot
 * effectively restrict the users of a free program by obtaining a
 * restrictive license from a patent holder.  Therefore, we insist that
 * any patent license obtained for a version of the library must be
 * consistent with the full freedom of use specified in this license.
 *
 *   Most GNU software, including some libraries, is covered by the
 * ordinary GNU General Public License.  This license, the GNU Lesser
 * General Public License, applies to certain designated libraries, and
 * is quite different from the ordinary General Public License.  We use
 * this license for certain libraries in order to permit linking those
 * libraries into non-free programs.
 *
 *   When a program is linked with a library, whether statically or using
 * a shared library, the combination of the two is legally speaking a
 * combined work, a derivative of the original library.  The ordinary
 * General Public License therefore permits such linking only if the
 * entire combination fits its criteria of freedom.  The Lesser General
 * Public License permits more lax criteria for linking other code with
 * the library.
 *
 *   We call this license the "Lesser" General Public License because it
 * does Less to protect the user's freedom than the ordinary General
 * Public License.  It also provides other free software developers Less
 * of an advantage over competing non-free programs.  These disadvantages
 * are the reason we use the ordinary General Public License for many
 * libraries.  However, the Lesser license provides advantages in certain
 * special circumstances.
 *
 *   For example, on rare occasions, there may be a special need to
 * encourage the widest possible use of a certain library, so that it becomes
 * a de-facto standard.  To achieve this, non-free programs must be
 * allowed to use the library.  A more frequent case is that a free
 * library does the same job as widely used non-free libraries.  In this
 * case, there is little to gain by limiting the free library to free
 * software only, so we use the Lesser General Public License.
 *
 *   In other cases, permission to use a particular library in non-free
 * programs enables a greater number of people to use a large body of
 * free software.  For example, permission to use the GNU C Library in
 * non-free programs enables many more people to use the whole GNU
 * operating system, as well as its variant, the GNU/Linux operating
 * system.
 *
 *   Although the Lesser General Public License is Less protective of the
 * users' freedom, it does ensure that the user of a program that is
 * linked with the Library has the freedom and the wherewithal to run
 * that program using a modified version of the Library.
 *
 *   The precise terms and conditions for copying, distribution and
 * modification follow.  Pay close attention to the difference between a
 * "work based on the library" and a "work that uses the library".  The
 * former contains code derived from the library, whereas the latter must
 * be combined with the library in order to run.
 *
 *                   GNU LESSER GENERAL PUBLIC LICENSE
 *    TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION
 *
 *   0. This License Agreement applies to any software library or other
 * program which contains a notice placed by the copyright holder or
 * other authorized party saying it may be distributed under the terms of
 * this Lesser General Public License (also called "this License").
 * Each licensee is addressed as "you".
 *
 *   A "library" means a collection of software functions and/or data
 * prepared so as to be conveniently linked with application programs
 * (which use some of those functions and data) to form executables.
 *
 *   The "Library", below, refers to any such software library or work
 * which has been distributed under these terms.  A "work based on the
 * Library" means either the Library or any derivative work under
 * copyright law: that is to say, a work containing the Library or a
 * portion of it, either verbatim or with modifications and/or translated
 * straightforwardly into another language.  (Hereinafter, translation is
 * included without limitation in the term "modification".)
 *
 *   "Source code" for a work means the preferred form of the work for
 * making modifications to it.  For a library, complete source code means
 * all the source code for all modules it contains, plus any associated
 * interface definition files, plus the scripts used to control compilation
 * and installation of the library.
 *
 *   Activities other than copying, distribution and modification are not
 * covered by this License; they are outside its scope.  The act of
 * running a program using the Library is not restricted, and output from
 * such a program is covered only if its contents constitute a work based
 * on the Library (independent of the use of the Library in a tool for
 * writing it).  Whether that is true depends on what the Library does
 * and what the program that uses the Library does.
 *
 *   1. You may copy and distribute verbatim copies of the Library's
 * complete source code as you receive it, in any medium, provided that
 * you conspicuously and appropriately publish on each copy an
 * appropriate copyright notice and disclaimer of warranty; keep intact
 * all the notices that refer to this License and to the absence of any
 * warranty; and distribute a copy of this License along with the
 * Library.
 *
 *   You may charge a fee for the physical act of transferring a copy,
 * and you may at your option offer warranty protection in exchange for a
 * fee.
 *
 *   2. You may modify your copy or copies of the Library or any portion
 * of it, thus forming a work based on the Library, and copy and
 * distribute such modifications or work under the terms of Section 1
 * above, provided that you also meet all of these conditions:
 *
 *     a) The modified work must itself be a software library.
 *
 *     b) You must cause the files modified to carry prominent notices
 *     stating that you changed the files and the date of any change.
 *
 *     c) You must cause the whole of the work to be licensed at no
 *     charge to all third parties under the terms of this License.
 *
 *     d) If a facility in the modified Library refers to a function or a
 *     table of data to be supplied by an application program that uses
 *     the facility, other than as an argument passed when the facility
 *     is invoked, then you must make a good faith effort to ensure that,
 *     in the event an application does not supply such function or
 *     table, the facility still operates, and performs whatever part of
 *     its purpose remains meaningful.
 *
 *     (For example, a function in a library to compute square roots has
 *     a purpose that is entirely well-defined independent of the
 *     application.  Therefore, Subsection 2d requires that any
 *     application-supplied function or table used by this function must
 *     be optional: if the application does not supply it, the square
 *     root function must still compute square roots.)
 *
 * These requirements apply to the modified work as a whole.  If
 * identifiable sections of that work are not derived from the Library,
 * and can be reasonably considered independent and separate works in
 * themselves, then this License, and its terms, do not apply to those
 * sections when you distribute them as separate works.  But when you
 * distribute the same sections as part of a whole which is a work based
 * on the Library, the distribution of the whole must be on the terms of
 * this License, whose permissions for other licensees extend to the
 * entire whole, and thus to each and every part regardless of who wrote
 * it.
 *
 * Thus, it is not the intent of this section to claim rights or contest
 * your rights to work written entirely by you; rather, the intent is to
 * exercise the right to control the distribution of derivative or
 * collective works based on the Library.
 *
 * In addition, mere aggregation of another work not based on the Library
 * with the Library (or with a work based on the Library) on a volume of
 * a storage or distribution medium does not bring the other work under
 * the scope of this License.
 *
 *   3. You may opt to apply the terms of the ordinary GNU General Public
 * License instead of this License to a given copy of the Library.  To do
 * this, you must alter all the notices that refer to this License, so
 * that they refer to the ordinary GNU General Public License, version 2,
 * instead of to this License.  (If a newer version than version 2 of the
 * ordinary GNU General Public License has appeared, then you can specify
 * that version instead if you wish.)  Do not make any other change in
 * these notices.
 *
 *   Once this change is made in a given copy, it is irreversible for
 * that copy, so the ordinary GNU General Public License applies to all
 * subsequent copies and derivative works made from that copy.
 *
 *   This option is useful when you wish to copy part of the code of
 * the Library into a program that is not a library.
 *
 *   4. You may copy and distribute the Library (or a portion or
 * derivative of it, under Section 2) in object code or executable form
 * under the terms of Sections 1 and 2 above provided that you accompany
 * it with the complete corresponding machine-readable source code, which
 * must be distributed under the terms of Sections 1 and 2 above on a
 * medium customarily used for software interchange.
 *
 *   If distribution of object code is made by offering access to copy
 * from a designated place, then offering equivalent access to copy the
 * source code from the same place satisfies the requirement to
 * distribute the source code, even though third parties are not
 * compelled to copy the source along with the object code.
 *
 *   5. A program that contains no derivative of any portion of the
 * Library, but is designed to work with the Library by being compiled or
 * linked with it, is called a "work that uses the Library".  Such a
 * work, in isolation, is not a derivative work of the Library, and
 * therefore falls outside the scope of this License.
 *
 *   However, linking a "work that uses the Library" with the Library
 * creates an executable that is a derivative of the Library (because it
 * contains portions of the Library), rather than a "work that uses the
 * library".  The executable is therefore covered by this License.
 * Section 6 states terms for distribution of such executables.
 *
 *   When a "work that uses the Library" uses material from a header file
 * that is part of the Library, the object code for the work may be a
 * derivative work of the Library even though the source code is not.
 * Whether this is true is especially significant if the work can be
 * linked without the Library, or if the work is itself a library.  The
 * threshold for this to be true is not precisely defined by law.
 *
 *   If such an object file uses only numerical parameters, data
 * structure layouts and accessors, and small macros and small inline
 * functions (ten lines or less in length), then the use of the object
 * file is unrestricted, regardless of whether it is legally a derivative
 * work.  (Executables containing this object code plus portions of the
 * Library will still fall under Section 6.)
 *
 *   Otherwise, if the work is a derivative of the Library, you may
 * distribute the object code for the work under the terms of Section 6.
 * Any executables containing that work also fall under Section 6,
 * whether or not they are linked directly with the Library itself.
 *
 *   6. As an exception to the Sections above, you may also combine or
 * link a "work that uses the Library" with the Library to produce a
 * work containing portions of the Library, and distribute that work
 * under terms of your choice, provided that the terms permit
 * modification of the work for the customer's own use and reverse
 * engineering for debugging such modifications.
 *
 *   You must give prominent notice with each copy of the work that the
 * Library is used in it and that the Library and its use are covered by
 * this License.  You must supply a copy of this License.  If the work
 * during execution displays copyright notices, you must include the
 * copyright notice for the Library among them, as well as a reference
 * directing the user to the copy of this License.  Also, you must do one
 * of these things:
 *
 *     a) Accompany the work with the complete corresponding
 *     machine-readable source code for the Library including whatever
 *     changes were used in the work (which must be distributed under
 *     Sections 1 and 2 above); and, if the work is an executable linked
 *     with the Library, with the complete machine-readable "work that
 *     uses the Library", as object code and/or source code, so that the
 *     user can modify the Library and then relink to produce a modified
 *     executable containing the modified Library.  (It is understood
 *     that the user who changes the contents of definitions files in the
 *     Library will not necessarily be able to recompile the application
 *     to use the modified definitions.)
 *
 *     b) Use a suitable shared library mechanism for linking with the
 *     Library.  A suitable mechanism is one that (1) uses at run time a
 *     copy of the library already present on the user's computer system,
 *     rather than copying library functions into the executable, and (2)
 *     will operate properly with a modified version of the library, if
 *     the user installs one, as long as the modified version is
 *     interface-compatible with the version that the work was made with.
 *
 *     c) Accompany the work with a written offer, valid for at
 *     least three years, to give the same user the materials
 *     specified in Subsection 6a, above, for a charge no more
 *     than the cost of performing this distribution.
 *
 *     d) If distribution of the work is made by offering access to copy
 *     from a designated place, offer equivalent access to copy the above
 *     specified materials from the same place.
 *
 *     e) Verify that the user has already received a copy of these
 *     materials or that you have already sent this user a copy.
 *
 *   For an executable, the required form of the "work that uses the
 * Library" must include any data and utility programs needed for
 * reproducing the executable from it.  However, as a special exception,
 * the materials to be distributed need not include anything that is
 * normally distributed (in either source or binary form) with the major
 * components (compiler, kernel, and so on) of the operating system on
 * which the executable runs, unless that component itself accompanies
 * the executable.
 *
 *   It may happen that this requirement contradicts the license
 * restrictions of other proprietary libraries that do not normally
 * accompany the operating system.  Such a contradiction means you cannot
 * use both them and the Library together in an executable that you
 * distribute.
 *
 *   7. You may place library facilities that are a work based on the
 * Library side-by-side in a single library together with other library
 * facilities not covered by this License, and distribute such a combined
 * library, provided that the separate distribution of the work based on
 * the Library and of the other library facilities is otherwise
 * permitted, and provided that you do these two things:
 *
 *     a) Accompany the combined library with a copy of the same work
 *     based on the Library, uncombined with any other library
 *     facilities.  This must be distributed under the terms of the
 *     Sections above.
 *
 *     b) Give prominent notice with the combined library of the fact
 *     that part of it is a work based on the Library, and explaining
 *     where to find the accompanying uncombined form of the same work.
 *
 *   8. You may not copy, modify, sublicense, link with, or distribute
 * the Library except as expressly provided under this License.  Any
 * attempt otherwise to copy, modify, sublicense, link with, or
 * distribute the Library is void, and will automatically terminate your
 * rights under this License.  However, parties who have received copies,
 * or rights, from you under this License will not have their licenses
 * terminated so long as such parties remain in full compliance.
 *
 *   9. You are not required to accept this License, since you have not
 * signed it.  However, nothing else grants you permission to modify or
 * distribute the Library or its derivative works.  These actions are
 * prohibited by law if you do not accept this License.  Therefore, by
 * modifying or distributing the Library (or any work based on the
 * Library), you indicate your acceptance of this License to do so, and
 * all its terms and conditions for copying, distributing or modifying
 * the Library or works based on it.
 *
 *   10. Each time you redistribute the Library (or any work based on the
 * Library), the recipient automatically receives a license from the
 * original licensor to copy, distribute, link with or modify the Library
 * subject to these terms and conditions.  You may not impose any further
 * restrictions on the recipients' exercise of the rights granted herein.
 * You are not responsible for enforcing compliance by third parties with
 * this License.
 *
 *   11. If, as a consequence of a court judgment or allegation of patent
 * infringement or for any other reason (not limited to patent issues),
 * conditions are imposed on you (whether by court order, agreement or
 * otherwise) that contradict the conditions of this License, they do not
 * excuse you from the conditions of this License.  If you cannot
 * distribute so as to satisfy simultaneously your obligations under this
 * License and any other pertinent obligations, then as a consequence you
 * may not distribute the Library at all.  For example, if a patent
 * license would not permit royalty-free redistribution of the Library by
 * all those who receive copies directly or indirectly through you, then
 * the only way you could satisfy both it and this License would be to
 * refrain entirely from distribution of the Library.
 *
 * If any portion of this section is held invalid or unenforceable under any
 * particular circumstance, the balance of the section is intended to apply,
 * and the section as a whole is intended to apply in other circumstances.
 *
 * It is not the purpose of this section to induce you to infringe any
 * patents or other property right claims or to contest validity of any
 * such claims; this section has the sole purpose of protecting the
 * integrity of the free software distribution system which is
 * implemented by public license practices.  Many people have made
 * generous contributions to the wide range of software distributed
 * through that system in reliance on consistent application of that
 * system; it is up to the author/donor to decide if he or she is willing
 * to distribute software through any other system and a licensee cannot
 * impose that choice.
 *
 * This section is intended to make thoroughly clear what is believed to
 * be a consequence of the rest of this License.
 *
 *   12. If the distribution and/or use of the Library is restricted in
 * certain countries either by patents or by copyrighted interfaces, the
 * original copyright holder who places the Library under this License may add
 * an explicit geographical distribution limitation excluding those countries,
 * so that distribution is permitted only in or among countries not thus
 * excluded.  In such case, this License incorporates the limitation as if
 * written in the body of this License.
 *
 *   13. The Free Software Foundation may publish revised and/or new
 * versions of the Lesser General Public License from time to time.
 * Such new versions will be similar in spirit to the present version,
 * but may differ in detail to address new problems or concerns.
 *
 * Each version is given a distinguishing version number.  If the Library
 * specifies a version number of this License which applies to it and
 * "any later version", you have the option of following the terms and
 * conditions either of that version or of any later version published by
 * the Free Software Foundation.  If the Library does not specify a
 * license version number, you may choose any version ever published by
 * the Free Software Foundation.
 *
 *   14. If you wish to incorporate parts of the Library into other free
 * programs whose distribution conditions are incompatible with these,
 * write to the author to ask for permission.  For software which is
 * copyrighted by the Free Software Foundation, write to the Free
 * Software Foundation; we sometimes make exceptions for this.  Our
 * decision will be guided by the two goals of preserving the free status
 * of all derivatives of our free software and of promoting the sharing
 * and reuse of software generally.
 *
 *                             NO WARRANTY
 *
 *   15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO
 * WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
 * EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
 * OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
 * LIBRARY IS WITH YOU.  SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME
 * THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.
 *
 *   16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
 * WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
 * AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU
 * FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR
 * CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
 * LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING
 * RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A
 * FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF
 * SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
 * DAMAGES.
 *
 *                      END OF TERMS AND CONDITIONS
 *
 *            How to Apply These Terms to Your New Libraries
 *
 *   If you develop a new library, and you want it to be of the greatest
 * possible use to the public, we recommend making it free software that
 * everyone can redistribute and change.  You can do so by permitting
 * redistribution under these terms (or, alternatively, under the terms of the
 * ordinary General Public License).
 *
 *   To apply these terms, attach the following notices to the library.  It is
 * safest to attach them to the start of each source file to most effectively
 * convey the exclusion of warranty; and each file should have at least the
 * "copyright" line and a pointer to where the full notice is found.
 *
 *     <one line to give the library's name and a brief idea of what it does.>
 *     Copyright (C) <year>  <name of author>
 *
 *     This library is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU Lesser General Public
 *     License as published by the Free Software Foundation; either
 *     version 2.1 of the License, or (at your option) any later version.
 *
 *     This library is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 *     Lesser General Public License for more details.
 *
 *     You should have received a copy of the GNU Lesser General Public
 *     License along with this library; if not, write to the Free Software
 *     Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 *
 * Also add information on how to contact you by electronic and paper mail.
 *
 * You should also get your employer (if you work as a programmer) or your
 * school, if any, to sign a "copyright disclaimer" for the library, if
 * necessary.  Here is a sample; alter the names:
 *
 *   Yoyodyne, Inc., hereby disclaims all copyright interest in the
 *   library `Frob' (a library for tweaking knobs) written by James Random Hacker.
 *
 *   <signature of Ty Coon>, 1 April 1990
 *   Ty Coon, President of Vice
 *
 * That's all there is to it!
 *
 *
 * ---
 *
 * emscripten and musl:
 *
 * Copyright (c) 2010-2024 Emscripten authors, see AUTHORS file.
 * Copyright © 2005-2024 Rich Felker, et al.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * emcc (Emscripten gcc/clang-like replacement + linker emulating GNU ld) 3.1.70 (b53978ee3f540dc74761eba127aa7f1b8761a125)
 *
 */

var LibAVFactory = (() => {
    var _scriptName = typeof document != 'undefined' ? document.currentScript?.src : undefined;
    if (typeof __filename != 'undefined') _scriptName = _scriptName || __filename;
    return (
        function (moduleArg = {}) {
            var moduleRtn;

            var Module = moduleArg; var readyPromiseResolve, readyPromiseReject; var readyPromise = new Promise((resolve, reject) => { readyPromiseResolve = resolve; readyPromiseReject = reject }); var ENVIRONMENT_IS_WEB = typeof window == "object"; var ENVIRONMENT_IS_WORKER = typeof importScripts == "function"; var ENVIRONMENT_IS_NODE = typeof process == "object" && typeof process.versions == "object" && typeof process.versions.node == "string" && process.type != "renderer"; if (ENVIRONMENT_IS_NODE) { } if (typeof _scriptName === "undefined") { if (typeof LibAV === "object" && LibAV && LibAV.base) _scriptName = LibAV.base + "/libav-*******-0a1c8f7-zzh.wasm.js"; else if (typeof self === "object" && self && self.location) _scriptName = self.location.href } Module.locateFile = function (path, prefix) { if (path.lastIndexOf(".wasm") === path.length - 5 && path.indexOf("libav-") !== -1) { if (Module.wasmurl) return Module.wasmurl; if (Module.variant) return prefix + "libav-*******-0a1c8f7-" + Module.variant + ".wasm.wasm" } return prefix + path }; var moduleOverrides = Object.assign({}, Module); var arguments_ = []; var thisProgram = "./this.program"; var quit_ = (status, toThrow) => { throw toThrow }; var scriptDirectory = ""; function locateFile(path) { if (Module["locateFile"]) { return Module["locateFile"](path, scriptDirectory) } return scriptDirectory + path } var readAsync, readBinary; if (ENVIRONMENT_IS_NODE) { var fs = require("fs"); var nodePath = require("path"); scriptDirectory = __dirname + "/"; readBinary = filename => { filename = isFileURI(filename) ? new URL(filename) : nodePath.normalize(filename); var ret = fs.readFileSync(filename); return ret }; readAsync = (filename, binary = true) => { filename = isFileURI(filename) ? new URL(filename) : nodePath.normalize(filename); return new Promise((resolve, reject) => { fs.readFile(filename, binary ? undefined : "utf8", (err, data) => { if (err) reject(err); else resolve(binary ? data.buffer : data) }) }) }; if (!Module["thisProgram"] && process.argv.length > 1) { thisProgram = process.argv[1].replace(/\\/g, "/") } arguments_ = process.argv.slice(2); quit_ = (status, toThrow) => { process.exitCode = status; throw toThrow } } else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) { if (ENVIRONMENT_IS_WORKER) { scriptDirectory = self.location.href } else if (typeof document != "undefined" && document.currentScript) { scriptDirectory = document.currentScript.src } if (_scriptName) { scriptDirectory = _scriptName } if (scriptDirectory.startsWith("blob:")) { scriptDirectory = "" } else { scriptDirectory = scriptDirectory.substr(0, scriptDirectory.replace(/[?#].*/, "").lastIndexOf("/") + 1) } { if (ENVIRONMENT_IS_WORKER) { readBinary = url => { var xhr = new XMLHttpRequest; xhr.open("GET", url, false); xhr.responseType = "arraybuffer"; xhr.send(null); return new Uint8Array(xhr.response) } } readAsync = url => { if (isFileURI(url)) { return new Promise((resolve, reject) => { var xhr = new XMLHttpRequest; xhr.open("GET", url, true); xhr.responseType = "arraybuffer"; xhr.onload = () => { if (xhr.status == 200 || xhr.status == 0 && xhr.response) { resolve(xhr.response); return } reject(xhr.status) }; xhr.onerror = reject; xhr.send(null) }) } return fetch(url, { credentials: "same-origin" }).then(response => { if (response.ok) { return response.arrayBuffer() } return Promise.reject(new Error(response.status + " : " + response.url)) }) } } } else { } var out = Module["print"] || console.log.bind(console); var err = Module["printErr"] || console.error.bind(console); Object.assign(Module, moduleOverrides); moduleOverrides = null; if (Module["arguments"]) arguments_ = Module["arguments"]; if (Module["thisProgram"]) thisProgram = Module["thisProgram"]; var wasmBinary = Module["wasmBinary"]; var wasmMemory; var ABORT = false; var EXITSTATUS; var HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64; function updateMemoryViews() { var b = wasmMemory.buffer; Module["HEAP8"] = HEAP8 = new Int8Array(b); Module["HEAP16"] = HEAP16 = new Int16Array(b); Module["HEAPU8"] = HEAPU8 = new Uint8Array(b); Module["HEAPU16"] = HEAPU16 = new Uint16Array(b); Module["HEAP32"] = HEAP32 = new Int32Array(b); Module["HEAPU32"] = HEAPU32 = new Uint32Array(b); Module["HEAPF32"] = HEAPF32 = new Float32Array(b); Module["HEAPF64"] = HEAPF64 = new Float64Array(b) } var __ATPRERUN__ = []; var __ATINIT__ = []; var __ATPOSTRUN__ = []; var runtimeInitialized = false; function preRun() { var preRuns = Module["preRun"]; if (preRuns) { if (typeof preRuns == "function") preRuns = [preRuns]; preRuns.forEach(addOnPreRun) } callRuntimeCallbacks(__ATPRERUN__) } function initRuntime() { runtimeInitialized = true; if (!Module["noFSInit"] && !FS.initialized) FS.init(); FS.ignorePermissions = false; TTY.init(); callRuntimeCallbacks(__ATINIT__) } function postRun() { var postRuns = Module["postRun"]; if (postRuns) { if (typeof postRuns == "function") postRuns = [postRuns]; postRuns.forEach(addOnPostRun) } callRuntimeCallbacks(__ATPOSTRUN__) } function addOnPreRun(cb) { __ATPRERUN__.unshift(cb) } function addOnInit(cb) { __ATINIT__.unshift(cb) } function addOnPostRun(cb) { __ATPOSTRUN__.unshift(cb) } var runDependencies = 0; var runDependencyWatcher = null; var dependenciesFulfilled = null; function getUniqueRunDependency(id) { return id } function addRunDependency(id) { runDependencies++; Module["monitorRunDependencies"]?.(runDependencies) } function removeRunDependency(id) { runDependencies--; Module["monitorRunDependencies"]?.(runDependencies); if (runDependencies == 0) { if (runDependencyWatcher !== null) { clearInterval(runDependencyWatcher); runDependencyWatcher = null } if (dependenciesFulfilled) { var callback = dependenciesFulfilled; dependenciesFulfilled = null; callback() } } } function abort(what) { Module["onAbort"]?.(what); what = "Aborted(" + what + ")"; err(what); ABORT = true; what += ". Build with -sASSERTIONS for more info."; var e = new WebAssembly.RuntimeError(what); readyPromiseReject(e); throw e } var dataURIPrefix = "data:application/octet-stream;base64,"; var isDataURI = filename => filename.startsWith(dataURIPrefix); var isFileURI = filename => filename.startsWith("file://"); function findWasmBinary() { var f = "libav-*******-0a1c8f7-zzh.wasm.wasm"; if (!isDataURI(f)) { return locateFile(f) } return f } var wasmBinaryFile; function getBinarySync(file) { if (file == wasmBinaryFile && wasmBinary) { return new Uint8Array(wasmBinary) } if (readBinary) { return readBinary(file) } throw "both async and sync fetching of the wasm failed" } function getBinaryPromise(binaryFile) { if (!wasmBinary) { return readAsync(binaryFile).then(response => new Uint8Array(response), () => getBinarySync(binaryFile)) } return Promise.resolve().then(() => getBinarySync(binaryFile)) } function instantiateArrayBuffer(binaryFile, imports, receiver) { return getBinaryPromise(binaryFile).then(binary => WebAssembly.instantiate(binary, imports)).then(receiver, reason => { err(`failed to asynchronously prepare wasm: ${reason}`); abort(reason) }) } function instantiateAsync(binary, binaryFile, imports, callback) { if (!binary && typeof WebAssembly.instantiateStreaming == "function" && !isDataURI(binaryFile) && !isFileURI(binaryFile) && !ENVIRONMENT_IS_NODE && typeof fetch == "function") { return fetch(binaryFile, { credentials: "same-origin" }).then(response => { var result = WebAssembly.instantiateStreaming(response, imports); return result.then(callback, function (reason) { err(`wasm streaming compile failed: ${reason}`); err("falling back to ArrayBuffer instantiation"); return instantiateArrayBuffer(binaryFile, imports, callback) }) }) } return instantiateArrayBuffer(binaryFile, imports, callback) } function getWasmImports() { return { a: wasmImports } } function createWasm() { var info = getWasmImports(); function receiveInstance(instance, module) { wasmExports = instance.exports; wasmExports = Asyncify.instrumentWasmExports(wasmExports); wasmMemory = wasmExports["B"]; updateMemoryViews(); addOnInit(wasmExports["C"]); removeRunDependency("wasm-instantiate"); return wasmExports } addRunDependency("wasm-instantiate"); function receiveInstantiationResult(result) { receiveInstance(result["instance"]) } if (Module["instantiateWasm"]) { try { return Module["instantiateWasm"](info, receiveInstance) } catch (e) { err(`Module.instantiateWasm callback failed with error: ${e}`); readyPromiseReject(e) } } wasmBinaryFile ??= findWasmBinary(); instantiateAsync(wasmBinary, wasmBinaryFile, info, receiveInstantiationResult).catch(readyPromiseReject); return {} } var tempDouble; var tempI64; function writeoutEmscriptenOOM() { throw new Error("Out of memory") } function libavjs_wait_reader(fd) { return Asyncify.handleAsync(function () { return new Promise(function (res) { var name = Module.fdName(fd); var waiters = Module.ff_reader_dev_waiters[name]; if (!waiters) waiters = Module.ff_reader_dev_waiters[name] = []; waiters.push(res) }) }) } function ExitStatus(status) { this.name = "ExitStatus"; this.message = `Program terminated with exit(${status})`; this.status = status } var callRuntimeCallbacks = callbacks => { callbacks.forEach(f => f(Module)) }; var noExitRuntime = Module["noExitRuntime"] || true; var stackRestore = val => __emscripten_stack_restore(val); var stackSave = () => _emscripten_stack_get_current(); var PATH = { isAbs: path => path.charAt(0) === "/", splitPath: filename => { var splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/; return splitPathRe.exec(filename).slice(1) }, normalizeArray: (parts, allowAboveRoot) => { var up = 0; for (var i = parts.length - 1; i >= 0; i--) { var last = parts[i]; if (last === ".") { parts.splice(i, 1) } else if (last === "..") { parts.splice(i, 1); up++ } else if (up) { parts.splice(i, 1); up-- } } if (allowAboveRoot) { for (; up; up--) { parts.unshift("..") } } return parts }, normalize: path => { var isAbsolute = PATH.isAbs(path), trailingSlash = path.substr(-1) === "/"; path = PATH.normalizeArray(path.split("/").filter(p => !!p), !isAbsolute).join("/"); if (!path && !isAbsolute) { path = "." } if (path && trailingSlash) { path += "/" } return (isAbsolute ? "/" : "") + path }, dirname: path => { var result = PATH.splitPath(path), root = result[0], dir = result[1]; if (!root && !dir) { return "." } if (dir) { dir = dir.substr(0, dir.length - 1) } return root + dir }, basename: path => { if (path === "/") return "/"; path = PATH.normalize(path); path = path.replace(/\/$/, ""); var lastSlash = path.lastIndexOf("/"); if (lastSlash === -1) return path; return path.substr(lastSlash + 1) }, join: (...paths) => PATH.normalize(paths.join("/")), join2: (l, r) => PATH.normalize(l + "/" + r) }; var initRandomFill = () => { if (typeof crypto == "object" && typeof crypto["getRandomValues"] == "function") { return view => crypto.getRandomValues(view) } else if (ENVIRONMENT_IS_NODE) { try { var crypto_module = require("crypto"); var randomFillSync = crypto_module["randomFillSync"]; if (randomFillSync) { return view => crypto_module["randomFillSync"](view) } var randomBytes = crypto_module["randomBytes"]; return view => (view.set(randomBytes(view.byteLength)), view) } catch (e) { } } abort("initRandomDevice") }; var randomFill = view => (randomFill = initRandomFill())(view); var PATH_FS = { resolve: (...args) => { var resolvedPath = "", resolvedAbsolute = false; for (var i = args.length - 1; i >= -1 && !resolvedAbsolute; i--) { var path = i >= 0 ? args[i] : FS.cwd(); if (typeof path != "string") { throw new TypeError("Arguments to path.resolve must be strings") } else if (!path) { return "" } resolvedPath = path + "/" + resolvedPath; resolvedAbsolute = PATH.isAbs(path) } resolvedPath = PATH.normalizeArray(resolvedPath.split("/").filter(p => !!p), !resolvedAbsolute).join("/"); return (resolvedAbsolute ? "/" : "") + resolvedPath || "." }, relative: (from, to) => { from = PATH_FS.resolve(from).substr(1); to = PATH_FS.resolve(to).substr(1); function trim(arr) { var start = 0; for (; start < arr.length; start++) { if (arr[start] !== "") break } var end = arr.length - 1; for (; end >= 0; end--) { if (arr[end] !== "") break } if (start > end) return []; return arr.slice(start, end - start + 1) } var fromParts = trim(from.split("/")); var toParts = trim(to.split("/")); var length = Math.min(fromParts.length, toParts.length); var samePartsLength = length; for (var i = 0; i < length; i++) { if (fromParts[i] !== toParts[i]) { samePartsLength = i; break } } var outputParts = []; for (var i = samePartsLength; i < fromParts.length; i++) { outputParts.push("..") } outputParts = outputParts.concat(toParts.slice(samePartsLength)); return outputParts.join("/") } }; var UTF8Decoder = typeof TextDecoder != "undefined" ? new TextDecoder : undefined; var UTF8ArrayToString = (heapOrArray, idx = 0, maxBytesToRead = NaN) => { var endIdx = idx + maxBytesToRead; var endPtr = idx; while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr; if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) { return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr)) } var str = ""; while (idx < endPtr) { var u0 = heapOrArray[idx++]; if (!(u0 & 128)) { str += String.fromCharCode(u0); continue } var u1 = heapOrArray[idx++] & 63; if ((u0 & 224) == 192) { str += String.fromCharCode((u0 & 31) << 6 | u1); continue } var u2 = heapOrArray[idx++] & 63; if ((u0 & 240) == 224) { u0 = (u0 & 15) << 12 | u1 << 6 | u2 } else { u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | heapOrArray[idx++] & 63 } if (u0 < 65536) { str += String.fromCharCode(u0) } else { var ch = u0 - 65536; str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023) } } return str }; var FS_stdin_getChar_buffer = []; var lengthBytesUTF8 = str => { var len = 0; for (var i = 0; i < str.length; ++i) { var c = str.charCodeAt(i); if (c <= 127) { len++ } else if (c <= 2047) { len += 2 } else if (c >= 55296 && c <= 57343) { len += 4; ++i } else { len += 3 } } return len }; var stringToUTF8Array = (str, heap, outIdx, maxBytesToWrite) => { if (!(maxBytesToWrite > 0)) return 0; var startIdx = outIdx; var endIdx = outIdx + maxBytesToWrite - 1; for (var i = 0; i < str.length; ++i) { var u = str.charCodeAt(i); if (u >= 55296 && u <= 57343) { var u1 = str.charCodeAt(++i); u = 65536 + ((u & 1023) << 10) | u1 & 1023 } if (u <= 127) { if (outIdx >= endIdx) break; heap[outIdx++] = u } else if (u <= 2047) { if (outIdx + 1 >= endIdx) break; heap[outIdx++] = 192 | u >> 6; heap[outIdx++] = 128 | u & 63 } else if (u <= 65535) { if (outIdx + 2 >= endIdx) break; heap[outIdx++] = 224 | u >> 12; heap[outIdx++] = 128 | u >> 6 & 63; heap[outIdx++] = 128 | u & 63 } else { if (outIdx + 3 >= endIdx) break; heap[outIdx++] = 240 | u >> 18; heap[outIdx++] = 128 | u >> 12 & 63; heap[outIdx++] = 128 | u >> 6 & 63; heap[outIdx++] = 128 | u & 63 } } heap[outIdx] = 0; return outIdx - startIdx }; function intArrayFromString(stringy, dontAddNull, length) { var len = length > 0 ? length : lengthBytesUTF8(stringy) + 1; var u8array = new Array(len); var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length); if (dontAddNull) u8array.length = numBytesWritten; return u8array } var FS_stdin_getChar = () => { if (!FS_stdin_getChar_buffer.length) { var result = null; if (ENVIRONMENT_IS_NODE) { var BUFSIZE = 256; var buf = Buffer.alloc(BUFSIZE); var bytesRead = 0; var fd = process.stdin.fd; try { bytesRead = fs.readSync(fd, buf, 0, BUFSIZE) } catch (e) { if (e.toString().includes("EOF")) bytesRead = 0; else throw e } if (bytesRead > 0) { result = buf.slice(0, bytesRead).toString("utf-8") } } else if (typeof window != "undefined" && typeof window.prompt == "function") { result = window.prompt("Input: "); if (result !== null) { result += "\n" } } else { } if (!result) { return null } FS_stdin_getChar_buffer = intArrayFromString(result, true) } return FS_stdin_getChar_buffer.shift() }; var TTY = { ttys: [], init() { }, shutdown() { }, register(dev, ops) { TTY.ttys[dev] = { input: [], output: [], ops }; FS.registerDevice(dev, TTY.stream_ops) }, stream_ops: { open(stream) { var tty = TTY.ttys[stream.node.rdev]; if (!tty) { throw new FS.ErrnoError(43) } stream.tty = tty; stream.seekable = false }, close(stream) { stream.tty.ops.fsync(stream.tty) }, fsync(stream) { stream.tty.ops.fsync(stream.tty) }, read(stream, buffer, offset, length, pos) { if (!stream.tty || !stream.tty.ops.get_char) { throw new FS.ErrnoError(60) } var bytesRead = 0; for (var i = 0; i < length; i++) { var result; try { result = stream.tty.ops.get_char(stream.tty) } catch (e) { throw new FS.ErrnoError(29) } if (result === undefined && bytesRead === 0) { throw new FS.ErrnoError(6) } if (result === null || result === undefined) break; bytesRead++; buffer[offset + i] = result } if (bytesRead) { stream.node.timestamp = Date.now() } return bytesRead }, write(stream, buffer, offset, length, pos) { if (!stream.tty || !stream.tty.ops.put_char) { throw new FS.ErrnoError(60) } try { for (var i = 0; i < length; i++) { stream.tty.ops.put_char(stream.tty, buffer[offset + i]) } } catch (e) { throw new FS.ErrnoError(29) } if (length) { stream.node.timestamp = Date.now() } return i } }, default_tty_ops: { get_char(tty) { return FS_stdin_getChar() }, put_char(tty, val) { if (val === null || val === 10) { out(UTF8ArrayToString(tty.output)); tty.output = [] } else { if (val != 0) tty.output.push(val) } }, fsync(tty) { if (tty.output && tty.output.length > 0) { out(UTF8ArrayToString(tty.output)); tty.output = [] } }, ioctl_tcgets(tty) { return { c_iflag: 25856, c_oflag: 5, c_cflag: 191, c_lflag: 35387, c_cc: [3, 28, 127, 21, 4, 0, 1, 0, 17, 19, 26, 0, 18, 15, 23, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] } }, ioctl_tcsets(tty, optional_actions, data) { return 0 }, ioctl_tiocgwinsz(tty) { return [24, 80] } }, default_tty1_ops: { put_char(tty, val) { if (val === null || val === 10) { tty.output = [] } else { if (val != 0) tty.output.push(val) } }, fsync(tty) { if (tty.output && tty.output.length > 0) { tty.output = [] } } } }; var alignMemory = (size, alignment) => Math.ceil(size / alignment) * alignment; var mmapAlloc = size => { abort() }; var MEMFS = { ops_table: null, mount(mount) { return MEMFS.createNode(null, "/", 16384 | 511, 0) }, createNode(parent, name, mode, dev) { if (FS.isBlkdev(mode) || FS.isFIFO(mode)) { throw new FS.ErrnoError(63) } MEMFS.ops_table ||= { dir: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, lookup: MEMFS.node_ops.lookup, mknod: MEMFS.node_ops.mknod, rename: MEMFS.node_ops.rename, unlink: MEMFS.node_ops.unlink, rmdir: MEMFS.node_ops.rmdir, readdir: MEMFS.node_ops.readdir, symlink: MEMFS.node_ops.symlink }, stream: { llseek: MEMFS.stream_ops.llseek } }, file: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: { llseek: MEMFS.stream_ops.llseek, read: MEMFS.stream_ops.read, write: MEMFS.stream_ops.write, allocate: MEMFS.stream_ops.allocate, mmap: MEMFS.stream_ops.mmap, msync: MEMFS.stream_ops.msync } }, link: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, readlink: MEMFS.node_ops.readlink }, stream: {} }, chrdev: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: FS.chrdev_stream_ops } }; var node = FS.createNode(parent, name, mode, dev); if (FS.isDir(node.mode)) { node.node_ops = MEMFS.ops_table.dir.node; node.stream_ops = MEMFS.ops_table.dir.stream; node.contents = {} } else if (FS.isFile(node.mode)) { node.node_ops = MEMFS.ops_table.file.node; node.stream_ops = MEMFS.ops_table.file.stream; node.usedBytes = 0; node.contents = null } else if (FS.isLink(node.mode)) { node.node_ops = MEMFS.ops_table.link.node; node.stream_ops = MEMFS.ops_table.link.stream } else if (FS.isChrdev(node.mode)) { node.node_ops = MEMFS.ops_table.chrdev.node; node.stream_ops = MEMFS.ops_table.chrdev.stream } node.timestamp = Date.now(); if (parent) { parent.contents[name] = node; parent.timestamp = node.timestamp } return node }, getFileDataAsTypedArray(node) { if (!node.contents) return new Uint8Array(0); if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes); return new Uint8Array(node.contents) }, expandFileStorage(node, newCapacity) { var prevCapacity = node.contents ? node.contents.length : 0; if (prevCapacity >= newCapacity) return; var CAPACITY_DOUBLING_MAX = 1024 * 1024; newCapacity = Math.max(newCapacity, prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2 : 1.125) >>> 0); if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256); var oldContents = node.contents; node.contents = new Uint8Array(newCapacity); if (node.usedBytes > 0) node.contents.set(oldContents.subarray(0, node.usedBytes), 0) }, resizeFileStorage(node, newSize) { if (node.usedBytes == newSize) return; if (newSize == 0) { node.contents = null; node.usedBytes = 0 } else { var oldContents = node.contents; node.contents = new Uint8Array(newSize); if (oldContents) { node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes))) } node.usedBytes = newSize } }, node_ops: { getattr(node) { var attr = {}; attr.dev = FS.isChrdev(node.mode) ? node.id : 1; attr.ino = node.id; attr.mode = node.mode; attr.nlink = 1; attr.uid = 0; attr.gid = 0; attr.rdev = node.rdev; if (FS.isDir(node.mode)) { attr.size = 4096 } else if (FS.isFile(node.mode)) { attr.size = node.usedBytes } else if (FS.isLink(node.mode)) { attr.size = node.link.length } else { attr.size = 0 } attr.atime = new Date(node.timestamp); attr.mtime = new Date(node.timestamp); attr.ctime = new Date(node.timestamp); attr.blksize = 4096; attr.blocks = Math.ceil(attr.size / attr.blksize); return attr }, setattr(node, attr) { if (attr.mode !== undefined) { node.mode = attr.mode } if (attr.timestamp !== undefined) { node.timestamp = attr.timestamp } if (attr.size !== undefined) { MEMFS.resizeFileStorage(node, attr.size) } }, lookup(parent, name) { throw FS.genericErrors[44] }, mknod(parent, name, mode, dev) { return MEMFS.createNode(parent, name, mode, dev) }, rename(old_node, new_dir, new_name) { if (FS.isDir(old_node.mode)) { var new_node; try { new_node = FS.lookupNode(new_dir, new_name) } catch (e) { } if (new_node) { for (var i in new_node.contents) { throw new FS.ErrnoError(55) } } } delete old_node.parent.contents[old_node.name]; old_node.parent.timestamp = Date.now(); old_node.name = new_name; new_dir.contents[new_name] = old_node; new_dir.timestamp = old_node.parent.timestamp }, unlink(parent, name) { delete parent.contents[name]; parent.timestamp = Date.now() }, rmdir(parent, name) { var node = FS.lookupNode(parent, name); for (var i in node.contents) { throw new FS.ErrnoError(55) } delete parent.contents[name]; parent.timestamp = Date.now() }, readdir(node) { var entries = [".", ".."]; for (var key of Object.keys(node.contents)) { entries.push(key) } return entries }, symlink(parent, newname, oldpath) { var node = MEMFS.createNode(parent, newname, 511 | 40960, 0); node.link = oldpath; return node }, readlink(node) { if (!FS.isLink(node.mode)) { throw new FS.ErrnoError(28) } return node.link } }, stream_ops: { read(stream, buffer, offset, length, position) { var contents = stream.node.contents; if (position >= stream.node.usedBytes) return 0; var size = Math.min(stream.node.usedBytes - position, length); if (size > 8 && contents.subarray) { buffer.set(contents.subarray(position, position + size), offset) } else { for (var i = 0; i < size; i++)buffer[offset + i] = contents[position + i] } return size }, write(stream, buffer, offset, length, position, canOwn) { if (buffer.buffer === HEAP8.buffer) { canOwn = false } if (!length) return 0; var node = stream.node; node.timestamp = Date.now(); if (buffer.subarray && (!node.contents || node.contents.subarray)) { if (canOwn) { node.contents = buffer.subarray(offset, offset + length); node.usedBytes = length; return length } else if (node.usedBytes === 0 && position === 0) { node.contents = buffer.slice(offset, offset + length); node.usedBytes = length; return length } else if (position + length <= node.usedBytes) { node.contents.set(buffer.subarray(offset, offset + length), position); return length } } MEMFS.expandFileStorage(node, position + length); if (node.contents.subarray && buffer.subarray) { node.contents.set(buffer.subarray(offset, offset + length), position) } else { for (var i = 0; i < length; i++) { node.contents[position + i] = buffer[offset + i] } } node.usedBytes = Math.max(node.usedBytes, position + length); return length }, llseek(stream, offset, whence) { var position = offset; if (whence === 1) { position += stream.position } else if (whence === 2) { if (FS.isFile(stream.node.mode)) { position += stream.node.usedBytes } } if (position < 0) { throw new FS.ErrnoError(28) } return position }, allocate(stream, offset, length) { MEMFS.expandFileStorage(stream.node, offset + length); stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length) }, mmap(stream, length, position, prot, flags) { if (!FS.isFile(stream.node.mode)) { throw new FS.ErrnoError(43) } var ptr; var allocated; var contents = stream.node.contents; if (!(flags & 2) && contents && contents.buffer === HEAP8.buffer) { allocated = false; ptr = contents.byteOffset } else { allocated = true; ptr = mmapAlloc(length); if (!ptr) { throw new FS.ErrnoError(48) } if (contents) { if (position > 0 || position + length < contents.length) { if (contents.subarray) { contents = contents.subarray(position, position + length) } else { contents = Array.prototype.slice.call(contents, position, position + length) } } HEAP8.set(contents, ptr) } } return { ptr, allocated } }, msync(stream, buffer, offset, length, mmapFlags) { MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false); return 0 } } }; var asyncLoad = (url, onload, onerror, noRunDep) => { var dep = !noRunDep ? getUniqueRunDependency(`al ${url}`) : ""; readAsync(url).then(arrayBuffer => { onload(new Uint8Array(arrayBuffer)); if (dep) removeRunDependency(dep) }, err => { if (onerror) { onerror() } else { throw `Loading data file "${url}" failed.` } }); if (dep) addRunDependency(dep) }; var FS_createDataFile = (parent, name, fileData, canRead, canWrite, canOwn) => { FS.createDataFile(parent, name, fileData, canRead, canWrite, canOwn) }; var preloadPlugins = Module["preloadPlugins"] || []; var FS_handledByPreloadPlugin = (byteArray, fullname, finish, onerror) => { if (typeof Browser != "undefined") Browser.init(); var handled = false; preloadPlugins.forEach(plugin => { if (handled) return; if (plugin["canHandle"](fullname)) { plugin["handle"](byteArray, fullname, finish, onerror); handled = true } }); return handled }; var FS_createPreloadedFile = (parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) => { var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent; var dep = getUniqueRunDependency(`cp ${fullname}`); function processData(byteArray) { function finish(byteArray) { preFinish?.(); if (!dontCreateFile) { FS_createDataFile(parent, name, byteArray, canRead, canWrite, canOwn) } onload?.(); removeRunDependency(dep) } if (FS_handledByPreloadPlugin(byteArray, fullname, finish, () => { onerror?.(); removeRunDependency(dep) })) { return } finish(byteArray) } addRunDependency(dep); if (typeof url == "string") { asyncLoad(url, processData, onerror) } else { processData(url) } }; var FS_modeStringToFlags = str => { var flagModes = { r: 0, "r+": 2, w: 512 | 64 | 1, "w+": 512 | 64 | 2, a: 1024 | 64 | 1, "a+": 1024 | 64 | 2 }; var flags = flagModes[str]; if (typeof flags == "undefined") { throw new Error(`Unknown file open mode: ${str}`) } return flags }; var FS_getMode = (canRead, canWrite) => { var mode = 0; if (canRead) mode |= 292 | 73; if (canWrite) mode |= 146; return mode }; var FS = { root: null, mounts: [], devices: {}, streams: [], nextInode: 1, nameTable: null, currentPath: "/", initialized: false, ignorePermissions: true, ErrnoError: class { constructor(errno) { this.name = "ErrnoError"; this.errno = errno } }, genericErrors: {}, filesystems: null, syncFSRequests: 0, readFiles: {}, FSStream: class { constructor() { this.shared = {} } get object() { return this.node } set object(val) { this.node = val } get isRead() { return (this.flags & 2097155) !== 1 } get isWrite() { return (this.flags & 2097155) !== 0 } get isAppend() { return this.flags & 1024 } get flags() { return this.shared.flags } set flags(val) { this.shared.flags = val } get position() { return this.shared.position } set position(val) { this.shared.position = val } }, FSNode: class { constructor(parent, name, mode, rdev) { if (!parent) { parent = this } this.parent = parent; this.mount = parent.mount; this.mounted = null; this.id = FS.nextInode++; this.name = name; this.mode = mode; this.node_ops = {}; this.stream_ops = {}; this.rdev = rdev; this.readMode = 292 | 73; this.writeMode = 146 } get read() { return (this.mode & this.readMode) === this.readMode } set read(val) { val ? this.mode |= this.readMode : this.mode &= ~this.readMode } get write() { return (this.mode & this.writeMode) === this.writeMode } set write(val) { val ? this.mode |= this.writeMode : this.mode &= ~this.writeMode } get isFolder() { return FS.isDir(this.mode) } get isDevice() { return FS.isChrdev(this.mode) } }, lookupPath(path, opts = {}) { path = PATH_FS.resolve(path); if (!path) return { path: "", node: null }; var defaults = { follow_mount: true, recurse_count: 0 }; opts = Object.assign(defaults, opts); if (opts.recurse_count > 8) { throw new FS.ErrnoError(32) } var parts = path.split("/").filter(p => !!p); var current = FS.root; var current_path = "/"; for (var i = 0; i < parts.length; i++) { var islast = i === parts.length - 1; if (islast && opts.parent) { break } current = FS.lookupNode(current, parts[i]); current_path = PATH.join2(current_path, parts[i]); if (FS.isMountpoint(current)) { if (!islast || islast && opts.follow_mount) { current = current.mounted.root } } if (!islast || opts.follow) { var count = 0; while (FS.isLink(current.mode)) { var link = FS.readlink(current_path); current_path = PATH_FS.resolve(PATH.dirname(current_path), link); var lookup = FS.lookupPath(current_path, { recurse_count: opts.recurse_count + 1 }); current = lookup.node; if (count++ > 40) { throw new FS.ErrnoError(32) } } } } return { path: current_path, node: current } }, getPath(node) { var path; while (true) { if (FS.isRoot(node)) { var mount = node.mount.mountpoint; if (!path) return mount; return mount[mount.length - 1] !== "/" ? `${mount}/${path}` : mount + path } path = path ? `${node.name}/${path}` : node.name; node = node.parent } }, hashName(parentid, name) { var hash = 0; for (var i = 0; i < name.length; i++) { hash = (hash << 5) - hash + name.charCodeAt(i) | 0 } return (parentid + hash >>> 0) % FS.nameTable.length }, hashAddNode(node) { var hash = FS.hashName(node.parent.id, node.name); node.name_next = FS.nameTable[hash]; FS.nameTable[hash] = node }, hashRemoveNode(node) { var hash = FS.hashName(node.parent.id, node.name); if (FS.nameTable[hash] === node) { FS.nameTable[hash] = node.name_next } else { var current = FS.nameTable[hash]; while (current) { if (current.name_next === node) { current.name_next = node.name_next; break } current = current.name_next } } }, lookupNode(parent, name) { var errCode = FS.mayLookup(parent); if (errCode) { throw new FS.ErrnoError(errCode) } var hash = FS.hashName(parent.id, name); for (var node = FS.nameTable[hash]; node; node = node.name_next) { var nodeName = node.name; if (node.parent.id === parent.id && nodeName === name) { return node } } return FS.lookup(parent, name) }, createNode(parent, name, mode, rdev) { var node = new FS.FSNode(parent, name, mode, rdev); FS.hashAddNode(node); return node }, destroyNode(node) { FS.hashRemoveNode(node) }, isRoot(node) { return node === node.parent }, isMountpoint(node) { return !!node.mounted }, isFile(mode) { return (mode & 61440) === 32768 }, isDir(mode) { return (mode & 61440) === 16384 }, isLink(mode) { return (mode & 61440) === 40960 }, isChrdev(mode) { return (mode & 61440) === 8192 }, isBlkdev(mode) { return (mode & 61440) === 24576 }, isFIFO(mode) { return (mode & 61440) === 4096 }, isSocket(mode) { return (mode & 49152) === 49152 }, flagsToPermissionString(flag) { var perms = ["r", "w", "rw"][flag & 3]; if (flag & 512) { perms += "w" } return perms }, nodePermissions(node, perms) { if (FS.ignorePermissions) { return 0 } if (perms.includes("r") && !(node.mode & 292)) { return 2 } else if (perms.includes("w") && !(node.mode & 146)) { return 2 } else if (perms.includes("x") && !(node.mode & 73)) { return 2 } return 0 }, mayLookup(dir) { if (!FS.isDir(dir.mode)) return 54; var errCode = FS.nodePermissions(dir, "x"); if (errCode) return errCode; if (!dir.node_ops.lookup) return 2; return 0 }, mayCreate(dir, name) { try { var node = FS.lookupNode(dir, name); return 20 } catch (e) { } return FS.nodePermissions(dir, "wx") }, mayDelete(dir, name, isdir) { var node; try { node = FS.lookupNode(dir, name) } catch (e) { return e.errno } var errCode = FS.nodePermissions(dir, "wx"); if (errCode) { return errCode } if (isdir) { if (!FS.isDir(node.mode)) { return 54 } if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) { return 10 } } else { if (FS.isDir(node.mode)) { return 31 } } return 0 }, mayOpen(node, flags) { if (!node) { return 44 } if (FS.isLink(node.mode)) { return 32 } else if (FS.isDir(node.mode)) { if (FS.flagsToPermissionString(flags) !== "r" || flags & 512) { return 31 } } return FS.nodePermissions(node, FS.flagsToPermissionString(flags)) }, MAX_OPEN_FDS: 4096, nextfd() { for (var fd = 0; fd <= FS.MAX_OPEN_FDS; fd++) { if (!FS.streams[fd]) { return fd } } throw new FS.ErrnoError(33) }, getStreamChecked(fd) { var stream = FS.getStream(fd); if (!stream) { throw new FS.ErrnoError(8) } return stream }, getStream: fd => FS.streams[fd], createStream(stream, fd = -1) { stream = Object.assign(new FS.FSStream, stream); if (fd == -1) { fd = FS.nextfd() } stream.fd = fd; FS.streams[fd] = stream; return stream }, closeStream(fd) { FS.streams[fd] = null }, dupStream(origStream, fd = -1) { var stream = FS.createStream(origStream, fd); stream.stream_ops?.dup?.(stream); return stream }, chrdev_stream_ops: { open(stream) { var device = FS.getDevice(stream.node.rdev); stream.stream_ops = device.stream_ops; stream.stream_ops.open?.(stream) }, llseek() { throw new FS.ErrnoError(70) } }, major: dev => dev >> 8, minor: dev => dev & 255, makedev: (ma, mi) => ma << 8 | mi, registerDevice(dev, ops) { FS.devices[dev] = { stream_ops: ops } }, getDevice: dev => FS.devices[dev], getMounts(mount) { var mounts = []; var check = [mount]; while (check.length) { var m = check.pop(); mounts.push(m); check.push(...m.mounts) } return mounts }, syncfs(populate, callback) { if (typeof populate == "function") { callback = populate; populate = false } FS.syncFSRequests++; if (FS.syncFSRequests > 1) { err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`) } var mounts = FS.getMounts(FS.root.mount); var completed = 0; function doCallback(errCode) { FS.syncFSRequests--; return callback(errCode) } function done(errCode) { if (errCode) { if (!done.errored) { done.errored = true; return doCallback(errCode) } return } if (++completed >= mounts.length) { doCallback(null) } } mounts.forEach(mount => { if (!mount.type.syncfs) { return done(null) } mount.type.syncfs(mount, populate, done) }) }, mount(type, opts, mountpoint) { var root = mountpoint === "/"; var pseudo = !mountpoint; var node; if (root && FS.root) { throw new FS.ErrnoError(10) } else if (!root && !pseudo) { var lookup = FS.lookupPath(mountpoint, { follow_mount: false }); mountpoint = lookup.path; node = lookup.node; if (FS.isMountpoint(node)) { throw new FS.ErrnoError(10) } if (!FS.isDir(node.mode)) { throw new FS.ErrnoError(54) } } var mount = { type, opts, mountpoint, mounts: [] }; var mountRoot = type.mount(mount); mountRoot.mount = mount; mount.root = mountRoot; if (root) { FS.root = mountRoot } else if (node) { node.mounted = mount; if (node.mount) { node.mount.mounts.push(mount) } } return mountRoot }, unmount(mountpoint) { var lookup = FS.lookupPath(mountpoint, { follow_mount: false }); if (!FS.isMountpoint(lookup.node)) { throw new FS.ErrnoError(28) } var node = lookup.node; var mount = node.mounted; var mounts = FS.getMounts(mount); Object.keys(FS.nameTable).forEach(hash => { var current = FS.nameTable[hash]; while (current) { var next = current.name_next; if (mounts.includes(current.mount)) { FS.destroyNode(current) } current = next } }); node.mounted = null; var idx = node.mount.mounts.indexOf(mount); node.mount.mounts.splice(idx, 1) }, lookup(parent, name) { return parent.node_ops.lookup(parent, name) }, mknod(path, mode, dev) { var lookup = FS.lookupPath(path, { parent: true }); var parent = lookup.node; var name = PATH.basename(path); if (!name || name === "." || name === "..") { throw new FS.ErrnoError(28) } var errCode = FS.mayCreate(parent, name); if (errCode) { throw new FS.ErrnoError(errCode) } if (!parent.node_ops.mknod) { throw new FS.ErrnoError(63) } return parent.node_ops.mknod(parent, name, mode, dev) }, create(path, mode) { mode = mode !== undefined ? mode : 438; mode &= 4095; mode |= 32768; return FS.mknod(path, mode, 0) }, mkdir(path, mode) { mode = mode !== undefined ? mode : 511; mode &= 511 | 512; mode |= 16384; return FS.mknod(path, mode, 0) }, mkdirTree(path, mode) { var dirs = path.split("/"); var d = ""; for (var i = 0; i < dirs.length; ++i) { if (!dirs[i]) continue; d += "/" + dirs[i]; try { FS.mkdir(d, mode) } catch (e) { if (e.errno != 20) throw e } } }, mkdev(path, mode, dev) { if (typeof dev == "undefined") { dev = mode; mode = 438 } mode |= 8192; return FS.mknod(path, mode, dev) }, symlink(oldpath, newpath) { if (!PATH_FS.resolve(oldpath)) { throw new FS.ErrnoError(44) } var lookup = FS.lookupPath(newpath, { parent: true }); var parent = lookup.node; if (!parent) { throw new FS.ErrnoError(44) } var newname = PATH.basename(newpath); var errCode = FS.mayCreate(parent, newname); if (errCode) { throw new FS.ErrnoError(errCode) } if (!parent.node_ops.symlink) { throw new FS.ErrnoError(63) } return parent.node_ops.symlink(parent, newname, oldpath) }, rename(old_path, new_path) { var old_dirname = PATH.dirname(old_path); var new_dirname = PATH.dirname(new_path); var old_name = PATH.basename(old_path); var new_name = PATH.basename(new_path); var lookup, old_dir, new_dir; lookup = FS.lookupPath(old_path, { parent: true }); old_dir = lookup.node; lookup = FS.lookupPath(new_path, { parent: true }); new_dir = lookup.node; if (!old_dir || !new_dir) throw new FS.ErrnoError(44); if (old_dir.mount !== new_dir.mount) { throw new FS.ErrnoError(75) } var old_node = FS.lookupNode(old_dir, old_name); var relative = PATH_FS.relative(old_path, new_dirname); if (relative.charAt(0) !== ".") { throw new FS.ErrnoError(28) } relative = PATH_FS.relative(new_path, old_dirname); if (relative.charAt(0) !== ".") { throw new FS.ErrnoError(55) } var new_node; try { new_node = FS.lookupNode(new_dir, new_name) } catch (e) { } if (old_node === new_node) { return } var isdir = FS.isDir(old_node.mode); var errCode = FS.mayDelete(old_dir, old_name, isdir); if (errCode) { throw new FS.ErrnoError(errCode) } errCode = new_node ? FS.mayDelete(new_dir, new_name, isdir) : FS.mayCreate(new_dir, new_name); if (errCode) { throw new FS.ErrnoError(errCode) } if (!old_dir.node_ops.rename) { throw new FS.ErrnoError(63) } if (FS.isMountpoint(old_node) || new_node && FS.isMountpoint(new_node)) { throw new FS.ErrnoError(10) } if (new_dir !== old_dir) { errCode = FS.nodePermissions(old_dir, "w"); if (errCode) { throw new FS.ErrnoError(errCode) } } FS.hashRemoveNode(old_node); try { old_dir.node_ops.rename(old_node, new_dir, new_name); old_node.parent = new_dir } catch (e) { throw e } finally { FS.hashAddNode(old_node) } }, rmdir(path) { var lookup = FS.lookupPath(path, { parent: true }); var parent = lookup.node; var name = PATH.basename(path); var node = FS.lookupNode(parent, name); var errCode = FS.mayDelete(parent, name, true); if (errCode) { throw new FS.ErrnoError(errCode) } if (!parent.node_ops.rmdir) { throw new FS.ErrnoError(63) } if (FS.isMountpoint(node)) { throw new FS.ErrnoError(10) } parent.node_ops.rmdir(parent, name); FS.destroyNode(node) }, readdir(path) { var lookup = FS.lookupPath(path, { follow: true }); var node = lookup.node; if (!node.node_ops.readdir) { throw new FS.ErrnoError(54) } return node.node_ops.readdir(node) }, unlink(path) { var lookup = FS.lookupPath(path, { parent: true }); var parent = lookup.node; if (!parent) { throw new FS.ErrnoError(44) } var name = PATH.basename(path); var node = FS.lookupNode(parent, name); var errCode = FS.mayDelete(parent, name, false); if (errCode) { throw new FS.ErrnoError(errCode) } if (!parent.node_ops.unlink) { throw new FS.ErrnoError(63) } if (FS.isMountpoint(node)) { throw new FS.ErrnoError(10) } parent.node_ops.unlink(parent, name); FS.destroyNode(node) }, readlink(path) { var lookup = FS.lookupPath(path); var link = lookup.node; if (!link) { throw new FS.ErrnoError(44) } if (!link.node_ops.readlink) { throw new FS.ErrnoError(28) } return PATH_FS.resolve(FS.getPath(link.parent), link.node_ops.readlink(link)) }, stat(path, dontFollow) { var lookup = FS.lookupPath(path, { follow: !dontFollow }); var node = lookup.node; if (!node) { throw new FS.ErrnoError(44) } if (!node.node_ops.getattr) { throw new FS.ErrnoError(63) } return node.node_ops.getattr(node) }, lstat(path) { return FS.stat(path, true) }, chmod(path, mode, dontFollow) { var node; if (typeof path == "string") { var lookup = FS.lookupPath(path, { follow: !dontFollow }); node = lookup.node } else { node = path } if (!node.node_ops.setattr) { throw new FS.ErrnoError(63) } node.node_ops.setattr(node, { mode: mode & 4095 | node.mode & ~4095, timestamp: Date.now() }) }, lchmod(path, mode) { FS.chmod(path, mode, true) }, fchmod(fd, mode) { var stream = FS.getStreamChecked(fd); FS.chmod(stream.node, mode) }, chown(path, uid, gid, dontFollow) { var node; if (typeof path == "string") { var lookup = FS.lookupPath(path, { follow: !dontFollow }); node = lookup.node } else { node = path } if (!node.node_ops.setattr) { throw new FS.ErrnoError(63) } node.node_ops.setattr(node, { timestamp: Date.now() }) }, lchown(path, uid, gid) { FS.chown(path, uid, gid, true) }, fchown(fd, uid, gid) { var stream = FS.getStreamChecked(fd); FS.chown(stream.node, uid, gid) }, truncate(path, len) { if (len < 0) { throw new FS.ErrnoError(28) } var node; if (typeof path == "string") { var lookup = FS.lookupPath(path, { follow: true }); node = lookup.node } else { node = path } if (!node.node_ops.setattr) { throw new FS.ErrnoError(63) } if (FS.isDir(node.mode)) { throw new FS.ErrnoError(31) } if (!FS.isFile(node.mode)) { throw new FS.ErrnoError(28) } var errCode = FS.nodePermissions(node, "w"); if (errCode) { throw new FS.ErrnoError(errCode) } node.node_ops.setattr(node, { size: len, timestamp: Date.now() }) }, ftruncate(fd, len) { var stream = FS.getStreamChecked(fd); if ((stream.flags & 2097155) === 0) { throw new FS.ErrnoError(28) } FS.truncate(stream.node, len) }, utime(path, atime, mtime) { var lookup = FS.lookupPath(path, { follow: true }); var node = lookup.node; node.node_ops.setattr(node, { timestamp: Math.max(atime, mtime) }) }, open(path, flags, mode) { if (path === "") { throw new FS.ErrnoError(44) } flags = typeof flags == "string" ? FS_modeStringToFlags(flags) : flags; if (flags & 64) { mode = typeof mode == "undefined" ? 438 : mode; mode = mode & 4095 | 32768 } else { mode = 0 } var node; if (typeof path == "object") { node = path } else { path = PATH.normalize(path); try { var lookup = FS.lookupPath(path, { follow: !(flags & 131072) }); node = lookup.node } catch (e) { } } var created = false; if (flags & 64) { if (node) { if (flags & 128) { throw new FS.ErrnoError(20) } } else { node = FS.mknod(path, mode, 0); created = true } } if (!node) { throw new FS.ErrnoError(44) } if (FS.isChrdev(node.mode)) { flags &= ~512 } if (flags & 65536 && !FS.isDir(node.mode)) { throw new FS.ErrnoError(54) } if (!created) { var errCode = FS.mayOpen(node, flags); if (errCode) { throw new FS.ErrnoError(errCode) } } if (flags & 512 && !created) { FS.truncate(node, 0) } flags &= ~(128 | 512 | 131072); var stream = FS.createStream({ node, path: FS.getPath(node), flags, seekable: true, position: 0, stream_ops: node.stream_ops, ungotten: [], error: false }); if (stream.stream_ops.open) { stream.stream_ops.open(stream) } if (Module["logReadFiles"] && !(flags & 1)) { if (!(path in FS.readFiles)) { FS.readFiles[path] = 1 } } return stream }, close(stream) { if (FS.isClosed(stream)) { throw new FS.ErrnoError(8) } if (stream.getdents) stream.getdents = null; try { if (stream.stream_ops.close) { stream.stream_ops.close(stream) } } catch (e) { throw e } finally { FS.closeStream(stream.fd) } stream.fd = null }, isClosed(stream) { return stream.fd === null }, llseek(stream, offset, whence) { if (FS.isClosed(stream)) { throw new FS.ErrnoError(8) } if (!stream.seekable || !stream.stream_ops.llseek) { throw new FS.ErrnoError(70) } if (whence != 0 && whence != 1 && whence != 2) { throw new FS.ErrnoError(28) } stream.position = stream.stream_ops.llseek(stream, offset, whence); stream.ungotten = []; return stream.position }, read(stream, buffer, offset, length, position) { if (length < 0 || position < 0) { throw new FS.ErrnoError(28) } if (FS.isClosed(stream)) { throw new FS.ErrnoError(8) } if ((stream.flags & 2097155) === 1) { throw new FS.ErrnoError(8) } if (FS.isDir(stream.node.mode)) { throw new FS.ErrnoError(31) } if (!stream.stream_ops.read) { throw new FS.ErrnoError(28) } var seeking = typeof position != "undefined"; if (!seeking) { position = stream.position } else if (!stream.seekable) { throw new FS.ErrnoError(70) } var bytesRead = stream.stream_ops.read(stream, buffer, offset, length, position); if (!seeking) stream.position += bytesRead; return bytesRead }, write(stream, buffer, offset, length, position, canOwn) { if (length < 0 || position < 0) { throw new FS.ErrnoError(28) } if (FS.isClosed(stream)) { throw new FS.ErrnoError(8) } if ((stream.flags & 2097155) === 0) { throw new FS.ErrnoError(8) } if (FS.isDir(stream.node.mode)) { throw new FS.ErrnoError(31) } if (!stream.stream_ops.write) { throw new FS.ErrnoError(28) } if (stream.seekable && stream.flags & 1024) { FS.llseek(stream, 0, 2) } var seeking = typeof position != "undefined"; if (!seeking) { position = stream.position } else if (!stream.seekable) { throw new FS.ErrnoError(70) } var bytesWritten = stream.stream_ops.write(stream, buffer, offset, length, position, canOwn); if (!seeking) stream.position += bytesWritten; return bytesWritten }, allocate(stream, offset, length) { if (FS.isClosed(stream)) { throw new FS.ErrnoError(8) } if (offset < 0 || length <= 0) { throw new FS.ErrnoError(28) } if ((stream.flags & 2097155) === 0) { throw new FS.ErrnoError(8) } if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) { throw new FS.ErrnoError(43) } if (!stream.stream_ops.allocate) { throw new FS.ErrnoError(138) } stream.stream_ops.allocate(stream, offset, length) }, mmap(stream, length, position, prot, flags) { if ((prot & 2) !== 0 && (flags & 2) === 0 && (stream.flags & 2097155) !== 2) { throw new FS.ErrnoError(2) } if ((stream.flags & 2097155) === 1) { throw new FS.ErrnoError(2) } if (!stream.stream_ops.mmap) { throw new FS.ErrnoError(43) } if (!length) { throw new FS.ErrnoError(28) } return stream.stream_ops.mmap(stream, length, position, prot, flags) }, msync(stream, buffer, offset, length, mmapFlags) { if (!stream.stream_ops.msync) { return 0 } return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags) }, ioctl(stream, cmd, arg) { if (!stream.stream_ops.ioctl) { throw new FS.ErrnoError(59) } return stream.stream_ops.ioctl(stream, cmd, arg) }, readFile(path, opts = {}) { opts.flags = opts.flags || 0; opts.encoding = opts.encoding || "binary"; if (opts.encoding !== "utf8" && opts.encoding !== "binary") { throw new Error(`Invalid encoding type "${opts.encoding}"`) } var ret; var stream = FS.open(path, opts.flags); var stat = FS.stat(path); var length = stat.size; var buf = new Uint8Array(length); FS.read(stream, buf, 0, length, 0); if (opts.encoding === "utf8") { ret = UTF8ArrayToString(buf) } else if (opts.encoding === "binary") { ret = buf } FS.close(stream); return ret }, writeFile(path, data, opts = {}) { opts.flags = opts.flags || 577; var stream = FS.open(path, opts.flags, opts.mode); if (typeof data == "string") { var buf = new Uint8Array(lengthBytesUTF8(data) + 1); var actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length); FS.write(stream, buf, 0, actualNumBytes, undefined, opts.canOwn) } else if (ArrayBuffer.isView(data)) { FS.write(stream, data, 0, data.byteLength, undefined, opts.canOwn) } else { throw new Error("Unsupported data type") } FS.close(stream) }, cwd: () => FS.currentPath, chdir(path) { var lookup = FS.lookupPath(path, { follow: true }); if (lookup.node === null) { throw new FS.ErrnoError(44) } if (!FS.isDir(lookup.node.mode)) { throw new FS.ErrnoError(54) } var errCode = FS.nodePermissions(lookup.node, "x"); if (errCode) { throw new FS.ErrnoError(errCode) } FS.currentPath = lookup.path }, createDefaultDirectories() { FS.mkdir("/tmp"); FS.mkdir("/home"); FS.mkdir("/home/<USER>") }, createDefaultDevices() { FS.mkdir("/dev"); FS.registerDevice(FS.makedev(1, 3), { read: () => 0, write: (stream, buffer, offset, length, pos) => length }); FS.mkdev("/dev/null", FS.makedev(1, 3)); TTY.register(FS.makedev(5, 0), TTY.default_tty_ops); TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops); FS.mkdev("/dev/tty", FS.makedev(5, 0)); FS.mkdev("/dev/tty1", FS.makedev(6, 0)); var randomBuffer = new Uint8Array(1024), randomLeft = 0; var randomByte = () => { if (randomLeft === 0) { randomLeft = randomFill(randomBuffer).byteLength } return randomBuffer[--randomLeft] }; FS.createDevice("/dev", "random", randomByte); FS.createDevice("/dev", "urandom", randomByte); FS.mkdir("/dev/shm"); FS.mkdir("/dev/shm/tmp") }, createSpecialDirectories() { FS.mkdir("/proc"); var proc_self = FS.mkdir("/proc/self"); FS.mkdir("/proc/self/fd"); FS.mount({ mount() { var node = FS.createNode(proc_self, "fd", 16384 | 511, 73); node.node_ops = { lookup(parent, name) { var fd = +name; var stream = FS.getStreamChecked(fd); var ret = { parent: null, mount: { mountpoint: "fake" }, node_ops: { readlink: () => stream.path } }; ret.parent = ret; return ret } }; return node } }, {}, "/proc/self/fd") }, createStandardStreams(input, output, error) { if (input) { FS.createDevice("/dev", "stdin", input) } else { FS.symlink("/dev/tty", "/dev/stdin") } if (output) { FS.createDevice("/dev", "stdout", null, output) } else { FS.symlink("/dev/tty", "/dev/stdout") } if (error) { FS.createDevice("/dev", "stderr", null, error) } else { FS.symlink("/dev/tty1", "/dev/stderr") } var stdin = FS.open("/dev/stdin", 0); var stdout = FS.open("/dev/stdout", 1); var stderr = FS.open("/dev/stderr", 1) }, staticInit() { [44].forEach(code => { FS.genericErrors[code] = new FS.ErrnoError(code); FS.genericErrors[code].stack = "<generic error, no stack>" }); FS.nameTable = new Array(4096); FS.mount(MEMFS, {}, "/"); FS.createDefaultDirectories(); FS.createDefaultDevices(); FS.createSpecialDirectories(); FS.filesystems = { MEMFS } }, init(input, output, error) { FS.initialized = true; input ??= Module["stdin"]; output ??= Module["stdout"]; error ??= Module["stderr"]; FS.createStandardStreams(input, output, error) }, quit() { FS.initialized = false; for (var i = 0; i < FS.streams.length; i++) { var stream = FS.streams[i]; if (!stream) { continue } FS.close(stream) } }, findObject(path, dontResolveLastLink) { var ret = FS.analyzePath(path, dontResolveLastLink); if (!ret.exists) { return null } return ret.object }, analyzePath(path, dontResolveLastLink) { try { var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink }); path = lookup.path } catch (e) { } var ret = { isRoot: false, exists: false, error: 0, name: null, path: null, object: null, parentExists: false, parentPath: null, parentObject: null }; try { var lookup = FS.lookupPath(path, { parent: true }); ret.parentExists = true; ret.parentPath = lookup.path; ret.parentObject = lookup.node; ret.name = PATH.basename(path); lookup = FS.lookupPath(path, { follow: !dontResolveLastLink }); ret.exists = true; ret.path = lookup.path; ret.object = lookup.node; ret.name = lookup.node.name; ret.isRoot = lookup.path === "/" } catch (e) { ret.error = e.errno } return ret }, createPath(parent, path, canRead, canWrite) { parent = typeof parent == "string" ? parent : FS.getPath(parent); var parts = path.split("/").reverse(); while (parts.length) { var part = parts.pop(); if (!part) continue; var current = PATH.join2(parent, part); try { FS.mkdir(current) } catch (e) { } parent = current } return current }, createFile(parent, name, properties, canRead, canWrite) { var path = PATH.join2(typeof parent == "string" ? parent : FS.getPath(parent), name); var mode = FS_getMode(canRead, canWrite); return FS.create(path, mode) }, createDataFile(parent, name, data, canRead, canWrite, canOwn) { var path = name; if (parent) { parent = typeof parent == "string" ? parent : FS.getPath(parent); path = name ? PATH.join2(parent, name) : parent } var mode = FS_getMode(canRead, canWrite); var node = FS.create(path, mode); if (data) { if (typeof data == "string") { var arr = new Array(data.length); for (var i = 0, len = data.length; i < len; ++i)arr[i] = data.charCodeAt(i); data = arr } FS.chmod(node, mode | 146); var stream = FS.open(node, 577); FS.write(stream, data, 0, data.length, 0, canOwn); FS.close(stream); FS.chmod(node, mode) } }, createDevice(parent, name, input, output) { var path = PATH.join2(typeof parent == "string" ? parent : FS.getPath(parent), name); var mode = FS_getMode(!!input, !!output); FS.createDevice.major ??= 64; var dev = FS.makedev(FS.createDevice.major++, 0); FS.registerDevice(dev, { open(stream) { stream.seekable = false }, close(stream) { if (output?.buffer?.length) { output(10) } }, read(stream, buffer, offset, length, pos) { var bytesRead = 0; for (var i = 0; i < length; i++) { var result; try { result = input() } catch (e) { throw new FS.ErrnoError(29) } if (result === undefined && bytesRead === 0) { throw new FS.ErrnoError(6) } if (result === null || result === undefined) break; bytesRead++; buffer[offset + i] = result } if (bytesRead) { stream.node.timestamp = Date.now() } return bytesRead }, write(stream, buffer, offset, length, pos) { for (var i = 0; i < length; i++) { try { output(buffer[offset + i]) } catch (e) { throw new FS.ErrnoError(29) } } if (length) { stream.node.timestamp = Date.now() } return i } }); return FS.mkdev(path, mode, dev) }, forceLoadFile(obj) { if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true; if (typeof XMLHttpRequest != "undefined") { throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.") } else { try { obj.contents = readBinary(obj.url); obj.usedBytes = obj.contents.length } catch (e) { throw new FS.ErrnoError(29) } } }, createLazyFile(parent, name, url, canRead, canWrite) { class LazyUint8Array { constructor() { this.lengthKnown = false; this.chunks = [] } get(idx) { if (idx > this.length - 1 || idx < 0) { return undefined } var chunkOffset = idx % this.chunkSize; var chunkNum = idx / this.chunkSize | 0; return this.getter(chunkNum)[chunkOffset] } setDataGetter(getter) { this.getter = getter } cacheLength() { var xhr = new XMLHttpRequest; xhr.open("HEAD", url, false); xhr.send(null); if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status); var datalength = Number(xhr.getResponseHeader("Content-length")); var header; var hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes"; var usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip"; var chunkSize = 1024 * 1024; if (!hasByteServing) chunkSize = datalength; var doXHR = (from, to) => { if (from > to) throw new Error("invalid range (" + from + ", " + to + ") or no bytes requested!"); if (to > datalength - 1) throw new Error("only " + datalength + " bytes available! programmer error!"); var xhr = new XMLHttpRequest; xhr.open("GET", url, false); if (datalength !== chunkSize) xhr.setRequestHeader("Range", "bytes=" + from + "-" + to); xhr.responseType = "arraybuffer"; if (xhr.overrideMimeType) { xhr.overrideMimeType("text/plain; charset=x-user-defined") } xhr.send(null); if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status); if (xhr.response !== undefined) { return new Uint8Array(xhr.response || []) } return intArrayFromString(xhr.responseText || "", true) }; var lazyArray = this; lazyArray.setDataGetter(chunkNum => { var start = chunkNum * chunkSize; var end = (chunkNum + 1) * chunkSize - 1; end = Math.min(end, datalength - 1); if (typeof lazyArray.chunks[chunkNum] == "undefined") { lazyArray.chunks[chunkNum] = doXHR(start, end) } if (typeof lazyArray.chunks[chunkNum] == "undefined") throw new Error("doXHR failed!"); return lazyArray.chunks[chunkNum] }); if (usesGzip || !datalength) { chunkSize = datalength = 1; datalength = this.getter(0).length; chunkSize = datalength; out("LazyFiles on gzip forces download of the whole file when length is accessed") } this._length = datalength; this._chunkSize = chunkSize; this.lengthKnown = true } get length() { if (!this.lengthKnown) { this.cacheLength() } return this._length } get chunkSize() { if (!this.lengthKnown) { this.cacheLength() } return this._chunkSize } } if (typeof XMLHttpRequest != "undefined") { if (!ENVIRONMENT_IS_WORKER) throw "Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc"; var lazyArray = new LazyUint8Array; var properties = { isDevice: false, contents: lazyArray } } else { var properties = { isDevice: false, url } } var node = FS.createFile(parent, name, properties, canRead, canWrite); if (properties.contents) { node.contents = properties.contents } else if (properties.url) { node.contents = null; node.url = properties.url } Object.defineProperties(node, { usedBytes: { get: function () { return this.contents.length } } }); var stream_ops = {}; var keys = Object.keys(node.stream_ops); keys.forEach(key => { var fn = node.stream_ops[key]; stream_ops[key] = (...args) => { FS.forceLoadFile(node); return fn(...args) } }); function writeChunks(stream, buffer, offset, length, position) { var contents = stream.node.contents; if (position >= contents.length) return 0; var size = Math.min(contents.length - position, length); if (contents.slice) { for (var i = 0; i < size; i++) { buffer[offset + i] = contents[position + i] } } else { for (var i = 0; i < size; i++) { buffer[offset + i] = contents.get(position + i) } } return size } stream_ops.read = (stream, buffer, offset, length, position) => { FS.forceLoadFile(node); return writeChunks(stream, buffer, offset, length, position) }; stream_ops.mmap = (stream, length, position, prot, flags) => { FS.forceLoadFile(node); var ptr = mmapAlloc(length); if (!ptr) { throw new FS.ErrnoError(48) } writeChunks(stream, HEAP8, ptr, length, position); return { ptr, allocated: true } }; node.stream_ops = stream_ops; return node } }; var UTF8ToString = (ptr, maxBytesToRead) => ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : ""; var SYSCALLS = { DEFAULT_POLLMASK: 5, calculateAt(dirfd, path, allowEmpty) { if (PATH.isAbs(path)) { return path } var dir; if (dirfd === -100) { dir = FS.cwd() } else { var dirstream = SYSCALLS.getStreamFromFD(dirfd); dir = dirstream.path } if (path.length == 0) { if (!allowEmpty) { throw new FS.ErrnoError(44) } return dir } return PATH.join2(dir, path) }, doStat(func, path, buf) { var stat = func(path); HEAP32[buf >> 2] = stat.dev; HEAP32[buf + 4 >> 2] = stat.mode; HEAPU32[buf + 8 >> 2] = stat.nlink; HEAP32[buf + 12 >> 2] = stat.uid; HEAP32[buf + 16 >> 2] = stat.gid; HEAP32[buf + 20 >> 2] = stat.rdev; tempI64 = [stat.size >>> 0, (tempDouble = stat.size, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 24 >> 2] = tempI64[0], HEAP32[buf + 28 >> 2] = tempI64[1]; HEAP32[buf + 32 >> 2] = 4096; HEAP32[buf + 36 >> 2] = stat.blocks; var atime = stat.atime.getTime(); var mtime = stat.mtime.getTime(); var ctime = stat.ctime.getTime(); tempI64 = [Math.floor(atime / 1e3) >>> 0, (tempDouble = Math.floor(atime / 1e3), +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 40 >> 2] = tempI64[0], HEAP32[buf + 44 >> 2] = tempI64[1]; HEAPU32[buf + 48 >> 2] = atime % 1e3 * 1e3 * 1e3; tempI64 = [Math.floor(mtime / 1e3) >>> 0, (tempDouble = Math.floor(mtime / 1e3), +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 56 >> 2] = tempI64[0], HEAP32[buf + 60 >> 2] = tempI64[1]; HEAPU32[buf + 64 >> 2] = mtime % 1e3 * 1e3 * 1e3; tempI64 = [Math.floor(ctime / 1e3) >>> 0, (tempDouble = Math.floor(ctime / 1e3), +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 72 >> 2] = tempI64[0], HEAP32[buf + 76 >> 2] = tempI64[1]; HEAPU32[buf + 80 >> 2] = ctime % 1e3 * 1e3 * 1e3; tempI64 = [stat.ino >>> 0, (tempDouble = stat.ino, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 88 >> 2] = tempI64[0], HEAP32[buf + 92 >> 2] = tempI64[1]; return 0 }, doMsync(addr, stream, len, flags, offset) { if (!FS.isFile(stream.node.mode)) { throw new FS.ErrnoError(43) } if (flags & 2) { return 0 } var buffer = HEAPU8.slice(addr, addr + len); FS.msync(stream, buffer, offset, len, flags) }, getStreamFromFD(fd) { var stream = FS.getStreamChecked(fd); return stream }, varargs: undefined, getStr(ptr) { var ret = UTF8ToString(ptr); return ret } }; function ___syscall_dup3(fd, newfd, flags) { try { var old = SYSCALLS.getStreamFromFD(fd); if (old.fd === newfd) return -28; if (newfd < 0 || newfd >= FS.MAX_OPEN_FDS) return -8; var existing = FS.getStream(newfd); if (existing) FS.close(existing); return FS.dupStream(old, newfd).fd } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_faccessat(dirfd, path, amode, flags) { try { path = SYSCALLS.getStr(path); path = SYSCALLS.calculateAt(dirfd, path); if (amode & ~7) { return -28 } var lookup = FS.lookupPath(path, { follow: true }); var node = lookup.node; if (!node) { return -44 } var perms = ""; if (amode & 4) perms += "r"; if (amode & 2) perms += "w"; if (amode & 1) perms += "x"; if (perms && FS.nodePermissions(node, perms)) { return -2 } return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function syscallGetVarargI() { var ret = HEAP32[+SYSCALLS.varargs >> 2]; SYSCALLS.varargs += 4; return ret } var syscallGetVarargP = syscallGetVarargI; function ___syscall_fcntl64(fd, cmd, varargs) { SYSCALLS.varargs = varargs; try { var stream = SYSCALLS.getStreamFromFD(fd); switch (cmd) { case 0: { var arg = syscallGetVarargI(); if (arg < 0) { return -28 } while (FS.streams[arg]) { arg++ } var newStream; newStream = FS.dupStream(stream, arg); return newStream.fd } case 1: case 2: return 0; case 3: return stream.flags; case 4: { var arg = syscallGetVarargI(); stream.flags |= arg; return 0 } case 12: { var arg = syscallGetVarargP(); var offset = 0; HEAP16[arg + offset >> 1] = 2; return 0 } case 13: case 14: return 0 }return -28 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_fstat64(fd, buf) { try { var stream = SYSCALLS.getStreamFromFD(fd); return SYSCALLS.doStat(FS.stat, stream.path, buf) } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } var stringToUTF8 = (str, outPtr, maxBytesToWrite) => stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite); function ___syscall_getdents64(fd, dirp, count) { try { var stream = SYSCALLS.getStreamFromFD(fd); stream.getdents ||= FS.readdir(stream.path); var struct_size = 280; var pos = 0; var off = FS.llseek(stream, 0, 1); var idx = Math.floor(off / struct_size); while (idx < stream.getdents.length && pos + struct_size <= count) { var id; var type; var name = stream.getdents[idx]; if (name === ".") { id = stream.node.id; type = 4 } else if (name === "..") { var lookup = FS.lookupPath(stream.path, { parent: true }); id = lookup.node.id; type = 4 } else { var child = FS.lookupNode(stream.node, name); id = child.id; type = FS.isChrdev(child.mode) ? 2 : FS.isDir(child.mode) ? 4 : FS.isLink(child.mode) ? 10 : 8 } tempI64 = [id >>> 0, (tempDouble = id, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[dirp + pos >> 2] = tempI64[0], HEAP32[dirp + pos + 4 >> 2] = tempI64[1]; tempI64 = [(idx + 1) * struct_size >>> 0, (tempDouble = (idx + 1) * struct_size, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[dirp + pos + 8 >> 2] = tempI64[0], HEAP32[dirp + pos + 12 >> 2] = tempI64[1]; HEAP16[dirp + pos + 16 >> 1] = 280; HEAP8[dirp + pos + 18] = type; stringToUTF8(name, dirp + pos + 19, 256); pos += struct_size; idx += 1 } FS.llseek(stream, idx * struct_size, 0); return pos } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_ioctl(fd, op, varargs) { SYSCALLS.varargs = varargs; try { var stream = SYSCALLS.getStreamFromFD(fd); switch (op) { case 21509: { if (!stream.tty) return -59; return 0 } case 21505: { if (!stream.tty) return -59; if (stream.tty.ops.ioctl_tcgets) { var termios = stream.tty.ops.ioctl_tcgets(stream); var argp = syscallGetVarargP(); HEAP32[argp >> 2] = termios.c_iflag || 0; HEAP32[argp + 4 >> 2] = termios.c_oflag || 0; HEAP32[argp + 8 >> 2] = termios.c_cflag || 0; HEAP32[argp + 12 >> 2] = termios.c_lflag || 0; for (var i = 0; i < 32; i++) { HEAP8[argp + i + 17] = termios.c_cc[i] || 0 } return 0 } return 0 } case 21510: case 21511: case 21512: { if (!stream.tty) return -59; return 0 } case 21506: case 21507: case 21508: { if (!stream.tty) return -59; if (stream.tty.ops.ioctl_tcsets) { var argp = syscallGetVarargP(); var c_iflag = HEAP32[argp >> 2]; var c_oflag = HEAP32[argp + 4 >> 2]; var c_cflag = HEAP32[argp + 8 >> 2]; var c_lflag = HEAP32[argp + 12 >> 2]; var c_cc = []; for (var i = 0; i < 32; i++) { c_cc.push(HEAP8[argp + i + 17]) } return stream.tty.ops.ioctl_tcsets(stream.tty, op, { c_iflag, c_oflag, c_cflag, c_lflag, c_cc }) } return 0 } case 21519: { if (!stream.tty) return -59; var argp = syscallGetVarargP(); HEAP32[argp >> 2] = 0; return 0 } case 21520: { if (!stream.tty) return -59; return -28 } case 21531: { var argp = syscallGetVarargP(); return FS.ioctl(stream, op, argp) } case 21523: { if (!stream.tty) return -59; if (stream.tty.ops.ioctl_tiocgwinsz) { var winsize = stream.tty.ops.ioctl_tiocgwinsz(stream.tty); var argp = syscallGetVarargP(); HEAP16[argp >> 1] = winsize[0]; HEAP16[argp + 2 >> 1] = winsize[1] } return 0 } case 21524: { if (!stream.tty) return -59; return 0 } case 21515: { if (!stream.tty) return -59; return 0 } default: return -28 } } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_lstat64(path, buf) { try { path = SYSCALLS.getStr(path); return SYSCALLS.doStat(FS.lstat, path, buf) } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_newfstatat(dirfd, path, buf, flags) { try { path = SYSCALLS.getStr(path); var nofollow = flags & 256; var allowEmpty = flags & 4096; flags = flags & ~6400; path = SYSCALLS.calculateAt(dirfd, path, allowEmpty); return SYSCALLS.doStat(nofollow ? FS.lstat : FS.stat, path, buf) } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_openat(dirfd, path, flags, varargs) { SYSCALLS.varargs = varargs; try { path = SYSCALLS.getStr(path); path = SYSCALLS.calculateAt(dirfd, path); var mode = varargs ? syscallGetVarargI() : 0; return FS.open(path, flags, mode).fd } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_renameat(olddirfd, oldpath, newdirfd, newpath) { try { oldpath = SYSCALLS.getStr(oldpath); newpath = SYSCALLS.getStr(newpath); oldpath = SYSCALLS.calculateAt(olddirfd, oldpath); newpath = SYSCALLS.calculateAt(newdirfd, newpath); FS.rename(oldpath, newpath); return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_rmdir(path) { try { path = SYSCALLS.getStr(path); FS.rmdir(path); return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_stat64(path, buf) { try { path = SYSCALLS.getStr(path); return SYSCALLS.doStat(FS.stat, path, buf) } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } function ___syscall_unlinkat(dirfd, path, flags) { try { path = SYSCALLS.getStr(path); path = SYSCALLS.calculateAt(dirfd, path); if (flags === 0) { FS.unlink(path) } else if (flags === 512) { FS.rmdir(path) } else { abort("Invalid flags passed to unlinkat") } return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return -e.errno } } var __abort_js = () => { abort("") }; var nowIsMonotonic = 1; var __emscripten_get_now_is_monotonic = () => nowIsMonotonic; var convertI32PairToI53Checked = (lo, hi) => hi + 2097152 >>> 0 < 4194305 - !!lo ? (lo >>> 0) + hi * 4294967296 : NaN; var _emscripten_date_now = () => Date.now(); var _emscripten_get_now = () => performance.now(); var getHeapMax = () => 2147483648; var growMemory = size => { var b = wasmMemory.buffer; var pages = (size - b.byteLength + 65535) / 65536 | 0; try { wasmMemory.grow(pages); updateMemoryViews(); return 1 } catch (e) { } }; var _emscripten_resize_heap = requestedSize => { var oldSize = HEAPU8.length; requestedSize >>>= 0; var maxHeapSize = getHeapMax(); if (requestedSize > maxHeapSize) { return false } for (var cutDown = 1; cutDown <= 4; cutDown *= 2) { var overGrownHeapSize = oldSize * (1 + .2 / cutDown); overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296); var newSize = Math.min(maxHeapSize, alignMemory(Math.max(requestedSize, overGrownHeapSize), 65536)); var replacement = growMemory(newSize); if (replacement) { return true } } return false }; var ENV = {}; var getExecutableName = () => thisProgram || "./this.program"; var getEnvStrings = () => { if (!getEnvStrings.strings) { var lang = (typeof navigator == "object" && navigator.languages && navigator.languages[0] || "C").replace("-", "_") + ".UTF-8"; var env = { USER: "web_user", LOGNAME: "web_user", PATH: "/", PWD: "/", HOME: "/home/<USER>", LANG: lang, _: getExecutableName() }; for (var x in ENV) { if (ENV[x] === undefined) delete env[x]; else env[x] = ENV[x] } var strings = []; for (var x in env) { strings.push(`${x}=${env[x]}`) } getEnvStrings.strings = strings } return getEnvStrings.strings }; var stringToAscii = (str, buffer) => { for (var i = 0; i < str.length; ++i) { HEAP8[buffer++] = str.charCodeAt(i) } HEAP8[buffer] = 0 }; var _environ_get = (__environ, environ_buf) => { var bufSize = 0; getEnvStrings().forEach((string, i) => { var ptr = environ_buf + bufSize; HEAPU32[__environ + i * 4 >> 2] = ptr; stringToAscii(string, ptr); bufSize += string.length + 1 }); return 0 }; var _environ_sizes_get = (penviron_count, penviron_buf_size) => { var strings = getEnvStrings(); HEAPU32[penviron_count >> 2] = strings.length; var bufSize = 0; strings.forEach(string => bufSize += string.length + 1); HEAPU32[penviron_buf_size >> 2] = bufSize; return 0 }; function _fd_close(fd) { try { var stream = SYSCALLS.getStreamFromFD(fd); FS.close(stream); return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return e.errno } } function _fd_fdstat_get(fd, pbuf) { try { var rightsBase = 0; var rightsInheriting = 0; var flags = 0; { var stream = SYSCALLS.getStreamFromFD(fd); var type = stream.tty ? 2 : FS.isDir(stream.mode) ? 3 : FS.isLink(stream.mode) ? 7 : 4 } HEAP8[pbuf] = type; HEAP16[pbuf + 2 >> 1] = flags; tempI64 = [rightsBase >>> 0, (tempDouble = rightsBase, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[pbuf + 8 >> 2] = tempI64[0], HEAP32[pbuf + 12 >> 2] = tempI64[1]; tempI64 = [rightsInheriting >>> 0, (tempDouble = rightsInheriting, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[pbuf + 16 >> 2] = tempI64[0], HEAP32[pbuf + 20 >> 2] = tempI64[1]; return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return e.errno } } var doReadv = (stream, iov, iovcnt, offset) => { var ret = 0; for (var i = 0; i < iovcnt; i++) { var ptr = HEAPU32[iov >> 2]; var len = HEAPU32[iov + 4 >> 2]; iov += 8; var curr = FS.read(stream, HEAP8, ptr, len, offset); if (curr < 0) return -1; ret += curr; if (curr < len) break; if (typeof offset != "undefined") { offset += curr } } return ret }; function _fd_read(fd, iov, iovcnt, pnum) { try { var stream = SYSCALLS.getStreamFromFD(fd); var num = doReadv(stream, iov, iovcnt); HEAPU32[pnum >> 2] = num; return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return e.errno } } function _fd_seek(fd, offset_low, offset_high, whence, newOffset) { var offset = convertI32PairToI53Checked(offset_low, offset_high); try { if (isNaN(offset)) return 61; var stream = SYSCALLS.getStreamFromFD(fd); FS.llseek(stream, offset, whence); tempI64 = [stream.position >>> 0, (tempDouble = stream.position, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? +Math.floor(tempDouble / 4294967296) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[newOffset >> 2] = tempI64[0], HEAP32[newOffset + 4 >> 2] = tempI64[1]; if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null; return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return e.errno } } var doWritev = (stream, iov, iovcnt, offset) => { var ret = 0; for (var i = 0; i < iovcnt; i++) { var ptr = HEAPU32[iov >> 2]; var len = HEAPU32[iov + 4 >> 2]; iov += 8; var curr = FS.write(stream, HEAP8, ptr, len, offset); if (curr < 0) return -1; ret += curr; if (curr < len) { break } if (typeof offset != "undefined") { offset += curr } } return ret }; function _fd_write(fd, iov, iovcnt, pnum) { try { var stream = SYSCALLS.getStreamFromFD(fd); var num = doWritev(stream, iov, iovcnt); HEAPU32[pnum >> 2] = num; return 0 } catch (e) { if (typeof FS == "undefined" || !(e.name === "ErrnoError")) throw e; return e.errno } } var runAndAbortIfError = func => { try { return func() } catch (e) { abort(e) } }; var handleException = e => { if (e instanceof ExitStatus || e == "unwind") { return EXITSTATUS } quit_(1, e) }; var runtimeKeepaliveCounter = 0; var keepRuntimeAlive = () => noExitRuntime || runtimeKeepaliveCounter > 0; var _proc_exit = code => { EXITSTATUS = code; if (!keepRuntimeAlive()) { Module["onExit"]?.(code); ABORT = true } quit_(code, new ExitStatus(code)) }; var exitJS = (status, implicit) => { EXITSTATUS = status; _proc_exit(status) }; var _exit = exitJS; var maybeExit = () => { if (!keepRuntimeAlive()) { try { _exit(EXITSTATUS) } catch (e) { handleException(e) } } }; var callUserCallback = func => { if (ABORT) { return } try { func(); maybeExit() } catch (e) { handleException(e) } }; var runtimeKeepalivePush = () => { runtimeKeepaliveCounter += 1 }; var runtimeKeepalivePop = () => { runtimeKeepaliveCounter -= 1 }; var Asyncify = { instrumentWasmImports(imports) { var importPattern = /^(libavjs_wait_reader|invoke_.*|__asyncjs__.*)$/; for (let [x, original] of Object.entries(imports)) { if (typeof original == "function") { let isAsyncifyImport = original.isAsync || importPattern.test(x) } } }, instrumentWasmExports(exports) { var ret = {}; for (let [x, original] of Object.entries(exports)) { if (typeof original == "function") { ret[x] = (...args) => { Asyncify.exportCallStack.push(x); try { return original(...args) } finally { if (!ABORT) { var y = Asyncify.exportCallStack.pop(); Asyncify.maybeStopUnwind() } } } } else { ret[x] = original } } return ret }, State: { Normal: 0, Unwinding: 1, Rewinding: 2, Disabled: 3 }, state: 0, StackSize: 4096, currData: null, handleSleepReturnValue: 0, exportCallStack: [], callStackNameToId: {}, callStackIdToName: {}, callStackId: 0, asyncPromiseHandlers: null, sleepCallbacks: [], getCallStackId(funcName) { var id = Asyncify.callStackNameToId[funcName]; if (id === undefined) { id = Asyncify.callStackId++; Asyncify.callStackNameToId[funcName] = id; Asyncify.callStackIdToName[id] = funcName } return id }, maybeStopUnwind() { if (Asyncify.currData && Asyncify.state === Asyncify.State.Unwinding && Asyncify.exportCallStack.length === 0) { Asyncify.state = Asyncify.State.Normal; runAndAbortIfError(_asyncify_stop_unwind); if (typeof Fibers != "undefined") { Fibers.trampoline() } } }, whenDone() { return new Promise((resolve, reject) => { Asyncify.asyncPromiseHandlers = { resolve, reject } }) }, allocateData() { var ptr = _malloc(12 + Asyncify.StackSize); Asyncify.setDataHeader(ptr, ptr + 12, Asyncify.StackSize); Asyncify.setDataRewindFunc(ptr); return ptr }, setDataHeader(ptr, stack, stackSize) { HEAPU32[ptr >> 2] = stack; HEAPU32[ptr + 4 >> 2] = stack + stackSize }, setDataRewindFunc(ptr) { var bottomOfCallStack = Asyncify.exportCallStack[0]; var rewindId = Asyncify.getCallStackId(bottomOfCallStack); HEAP32[ptr + 8 >> 2] = rewindId }, getDataRewindFuncName(ptr) { var id = HEAP32[ptr + 8 >> 2]; var name = Asyncify.callStackIdToName[id]; return name }, getDataRewindFunc(name) { var func = wasmExports[name]; return func }, doRewind(ptr) { var name = Asyncify.getDataRewindFuncName(ptr); var func = Asyncify.getDataRewindFunc(name); return func() }, handleSleep(startAsync) { if (ABORT) return; if (Asyncify.state === Asyncify.State.Normal) { var reachedCallback = false; var reachedAfterCallback = false; startAsync((handleSleepReturnValue = 0) => { if (ABORT) return; Asyncify.handleSleepReturnValue = handleSleepReturnValue; reachedCallback = true; if (!reachedAfterCallback) { return } Asyncify.state = Asyncify.State.Rewinding; runAndAbortIfError(() => _asyncify_start_rewind(Asyncify.currData)); if (typeof MainLoop != "undefined" && MainLoop.func) { MainLoop.resume() } var asyncWasmReturnValue, isError = false; try { asyncWasmReturnValue = Asyncify.doRewind(Asyncify.currData) } catch (err) { asyncWasmReturnValue = err; isError = true } var handled = false; if (!Asyncify.currData) { var asyncPromiseHandlers = Asyncify.asyncPromiseHandlers; if (asyncPromiseHandlers) { Asyncify.asyncPromiseHandlers = null; (isError ? asyncPromiseHandlers.reject : asyncPromiseHandlers.resolve)(asyncWasmReturnValue); handled = true } } if (isError && !handled) { throw asyncWasmReturnValue } }); reachedAfterCallback = true; if (!reachedCallback) { Asyncify.state = Asyncify.State.Unwinding; Asyncify.currData = Asyncify.allocateData(); if (typeof MainLoop != "undefined" && MainLoop.func) { MainLoop.pause() } runAndAbortIfError(() => _asyncify_start_unwind(Asyncify.currData)) } } else if (Asyncify.state === Asyncify.State.Rewinding) { Asyncify.state = Asyncify.State.Normal; runAndAbortIfError(_asyncify_stop_rewind); _free(Asyncify.currData); Asyncify.currData = null; Asyncify.sleepCallbacks.forEach(callUserCallback) } else { abort(`invalid state: ${Asyncify.state}`) } return Asyncify.handleSleepReturnValue }, handleAsync(startAsync) { return Asyncify.handleSleep(wakeUp => { startAsync().then(wakeUp) }) } }; var getCFunc = ident => { var func = Module["_" + ident]; return func }; var writeArrayToMemory = (array, buffer) => { HEAP8.set(array, buffer) }; var stackAlloc = sz => __emscripten_stack_alloc(sz); var stringToUTF8OnStack = str => { var size = lengthBytesUTF8(str) + 1; var ret = stackAlloc(size); stringToUTF8(str, ret, size); return ret }; var ccall = (ident, returnType, argTypes, args, opts) => { var toC = { string: str => { var ret = 0; if (str !== null && str !== undefined && str !== 0) { ret = stringToUTF8OnStack(str) } return ret }, array: arr => { var ret = stackAlloc(arr.length); writeArrayToMemory(arr, ret); return ret } }; function convertReturnValue(ret) { if (returnType === "string") { return UTF8ToString(ret) } if (returnType === "boolean") return Boolean(ret); return ret } var func = getCFunc(ident); var cArgs = []; var stack = 0; if (args) { for (var i = 0; i < args.length; i++) { var converter = toC[argTypes[i]]; if (converter) { if (stack === 0) stack = stackSave(); cArgs[i] = converter(args[i]) } else { cArgs[i] = args[i] } } } var previousAsync = Asyncify.currData; var ret = func(...cArgs); function onDone(ret) { runtimeKeepalivePop(); if (stack !== 0) stackRestore(stack); return convertReturnValue(ret) } var asyncMode = opts?.async; runtimeKeepalivePush(); if (Asyncify.currData != previousAsync) { return Asyncify.whenDone().then(onDone) } ret = onDone(ret); if (asyncMode) return Promise.resolve(ret); return ret }; var cwrap = (ident, returnType, argTypes, opts) => { var numericArgs = !argTypes || argTypes.every(type => type === "number" || type === "boolean"); var numericRet = returnType !== "string"; if (numericRet && numericArgs && !opts) { return getCFunc(ident) } return (...args) => ccall(ident, returnType, argTypes, args, opts) }; FS.createPreloadedFile = FS_createPreloadedFile; FS.staticInit(); var wasmImports = { l: ___syscall_dup3, h: ___syscall_faccessat, d: ___syscall_fcntl64, x: ___syscall_fstat64, q: ___syscall_getdents64, j: ___syscall_ioctl, u: ___syscall_lstat64, v: ___syscall_newfstatat, r: ___syscall_openat, p: ___syscall_renameat, o: ___syscall_rmdir, w: ___syscall_stat64, n: ___syscall_unlinkat, i: __abort_js, y: __emscripten_get_now_is_monotonic, a: _emscripten_date_now, e: _emscripten_get_now, m: _emscripten_resize_heap, s: _environ_get, t: _environ_sizes_get, c: _fd_close, g: _fd_fdstat_get, f: _fd_read, k: _fd_seek, b: _fd_write, z: libavjs_wait_reader, A: writeoutEmscriptenOOM }; var wasmExports = createWasm(); var ___wasm_call_ctors = () => (___wasm_call_ctors = wasmExports["C"])(); var _ff_nothing = Module["_ff_nothing"] = () => (_ff_nothing = Module["_ff_nothing"] = wasmExports["D"])(); var _AVFrame_crop_bottom = Module["_AVFrame_crop_bottom"] = a0 => (_AVFrame_crop_bottom = Module["_AVFrame_crop_bottom"] = wasmExports["E"])(a0); var _AVFrame_crop_bottom_s = Module["_AVFrame_crop_bottom_s"] = (a0, a1) => (_AVFrame_crop_bottom_s = Module["_AVFrame_crop_bottom_s"] = wasmExports["F"])(a0, a1); var _AVFrame_crop_left = Module["_AVFrame_crop_left"] = a0 => (_AVFrame_crop_left = Module["_AVFrame_crop_left"] = wasmExports["G"])(a0); var _AVFrame_crop_left_s = Module["_AVFrame_crop_left_s"] = (a0, a1) => (_AVFrame_crop_left_s = Module["_AVFrame_crop_left_s"] = wasmExports["H"])(a0, a1); var _AVFrame_crop_right = Module["_AVFrame_crop_right"] = a0 => (_AVFrame_crop_right = Module["_AVFrame_crop_right"] = wasmExports["I"])(a0); var _AVFrame_crop_right_s = Module["_AVFrame_crop_right_s"] = (a0, a1) => (_AVFrame_crop_right_s = Module["_AVFrame_crop_right_s"] = wasmExports["J"])(a0, a1); var _AVFrame_crop_top = Module["_AVFrame_crop_top"] = a0 => (_AVFrame_crop_top = Module["_AVFrame_crop_top"] = wasmExports["K"])(a0); var _AVFrame_crop_top_s = Module["_AVFrame_crop_top_s"] = (a0, a1) => (_AVFrame_crop_top_s = Module["_AVFrame_crop_top_s"] = wasmExports["L"])(a0, a1); var _AVFrame_data_a = Module["_AVFrame_data_a"] = (a0, a1) => (_AVFrame_data_a = Module["_AVFrame_data_a"] = wasmExports["M"])(a0, a1); var _AVFrame_data_a_s = Module["_AVFrame_data_a_s"] = (a0, a1, a2) => (_AVFrame_data_a_s = Module["_AVFrame_data_a_s"] = wasmExports["N"])(a0, a1, a2); var _AVFrame_format = Module["_AVFrame_format"] = a0 => (_AVFrame_format = Module["_AVFrame_format"] = wasmExports["O"])(a0); var _AVFrame_format_s = Module["_AVFrame_format_s"] = (a0, a1) => (_AVFrame_format_s = Module["_AVFrame_format_s"] = wasmExports["P"])(a0, a1); var _AVFrame_height = Module["_AVFrame_height"] = a0 => (_AVFrame_height = Module["_AVFrame_height"] = wasmExports["Q"])(a0); var _AVFrame_height_s = Module["_AVFrame_height_s"] = (a0, a1) => (_AVFrame_height_s = Module["_AVFrame_height_s"] = wasmExports["R"])(a0, a1); var _AVFrame_key_frame = Module["_AVFrame_key_frame"] = a0 => (_AVFrame_key_frame = Module["_AVFrame_key_frame"] = wasmExports["S"])(a0); var _AVFrame_key_frame_s = Module["_AVFrame_key_frame_s"] = (a0, a1) => (_AVFrame_key_frame_s = Module["_AVFrame_key_frame_s"] = wasmExports["T"])(a0, a1); var _AVFrame_linesize_a = Module["_AVFrame_linesize_a"] = (a0, a1) => (_AVFrame_linesize_a = Module["_AVFrame_linesize_a"] = wasmExports["U"])(a0, a1); var _AVFrame_linesize_a_s = Module["_AVFrame_linesize_a_s"] = (a0, a1, a2) => (_AVFrame_linesize_a_s = Module["_AVFrame_linesize_a_s"] = wasmExports["V"])(a0, a1, a2); var _AVFrame_nb_samples = Module["_AVFrame_nb_samples"] = a0 => (_AVFrame_nb_samples = Module["_AVFrame_nb_samples"] = wasmExports["W"])(a0); var _AVFrame_nb_samples_s = Module["_AVFrame_nb_samples_s"] = (a0, a1) => (_AVFrame_nb_samples_s = Module["_AVFrame_nb_samples_s"] = wasmExports["X"])(a0, a1); var _AVFrame_pict_type = Module["_AVFrame_pict_type"] = a0 => (_AVFrame_pict_type = Module["_AVFrame_pict_type"] = wasmExports["Y"])(a0); var _AVFrame_pict_type_s = Module["_AVFrame_pict_type_s"] = (a0, a1) => (_AVFrame_pict_type_s = Module["_AVFrame_pict_type_s"] = wasmExports["Z"])(a0, a1); var _AVFrame_pts = Module["_AVFrame_pts"] = a0 => (_AVFrame_pts = Module["_AVFrame_pts"] = wasmExports["_"])(a0); var _AVFrame_ptshi = Module["_AVFrame_ptshi"] = a0 => (_AVFrame_ptshi = Module["_AVFrame_ptshi"] = wasmExports["$"])(a0); var _AVFrame_pts_s = Module["_AVFrame_pts_s"] = (a0, a1) => (_AVFrame_pts_s = Module["_AVFrame_pts_s"] = wasmExports["aa"])(a0, a1); var _AVFrame_ptshi_s = Module["_AVFrame_ptshi_s"] = (a0, a1) => (_AVFrame_ptshi_s = Module["_AVFrame_ptshi_s"] = wasmExports["ba"])(a0, a1); var _AVFrame_sample_rate = Module["_AVFrame_sample_rate"] = a0 => (_AVFrame_sample_rate = Module["_AVFrame_sample_rate"] = wasmExports["ca"])(a0); var _AVFrame_sample_rate_s = Module["_AVFrame_sample_rate_s"] = (a0, a1) => (_AVFrame_sample_rate_s = Module["_AVFrame_sample_rate_s"] = wasmExports["da"])(a0, a1); var _AVFrame_width = Module["_AVFrame_width"] = a0 => (_AVFrame_width = Module["_AVFrame_width"] = wasmExports["ea"])(a0); var _AVFrame_width_s = Module["_AVFrame_width_s"] = (a0, a1) => (_AVFrame_width_s = Module["_AVFrame_width_s"] = wasmExports["fa"])(a0, a1); var _AVFrame_sample_aspect_ratio_num = Module["_AVFrame_sample_aspect_ratio_num"] = a0 => (_AVFrame_sample_aspect_ratio_num = Module["_AVFrame_sample_aspect_ratio_num"] = wasmExports["ga"])(a0); var _AVFrame_sample_aspect_ratio_den = Module["_AVFrame_sample_aspect_ratio_den"] = a0 => (_AVFrame_sample_aspect_ratio_den = Module["_AVFrame_sample_aspect_ratio_den"] = wasmExports["ha"])(a0); var _AVFrame_sample_aspect_ratio_num_s = Module["_AVFrame_sample_aspect_ratio_num_s"] = (a0, a1) => (_AVFrame_sample_aspect_ratio_num_s = Module["_AVFrame_sample_aspect_ratio_num_s"] = wasmExports["ia"])(a0, a1); var _AVFrame_sample_aspect_ratio_den_s = Module["_AVFrame_sample_aspect_ratio_den_s"] = (a0, a1) => (_AVFrame_sample_aspect_ratio_den_s = Module["_AVFrame_sample_aspect_ratio_den_s"] = wasmExports["ja"])(a0, a1); var _AVFrame_sample_aspect_ratio_s = Module["_AVFrame_sample_aspect_ratio_s"] = (a0, a1, a2) => (_AVFrame_sample_aspect_ratio_s = Module["_AVFrame_sample_aspect_ratio_s"] = wasmExports["ka"])(a0, a1, a2); var _AVFrame_time_base_num = Module["_AVFrame_time_base_num"] = a0 => (_AVFrame_time_base_num = Module["_AVFrame_time_base_num"] = wasmExports["la"])(a0); var _AVFrame_time_base_den = Module["_AVFrame_time_base_den"] = a0 => (_AVFrame_time_base_den = Module["_AVFrame_time_base_den"] = wasmExports["ma"])(a0); var _AVFrame_time_base_num_s = Module["_AVFrame_time_base_num_s"] = (a0, a1) => (_AVFrame_time_base_num_s = Module["_AVFrame_time_base_num_s"] = wasmExports["na"])(a0, a1); var _AVFrame_time_base_den_s = Module["_AVFrame_time_base_den_s"] = (a0, a1) => (_AVFrame_time_base_den_s = Module["_AVFrame_time_base_den_s"] = wasmExports["oa"])(a0, a1); var _AVFrame_time_base_s = Module["_AVFrame_time_base_s"] = (a0, a1, a2) => (_AVFrame_time_base_s = Module["_AVFrame_time_base_s"] = wasmExports["pa"])(a0, a1, a2); var _AVFrame_channel_layoutmask_s = Module["_AVFrame_channel_layoutmask_s"] = (a0, a1, a2) => (_AVFrame_channel_layoutmask_s = Module["_AVFrame_channel_layoutmask_s"] = wasmExports["qa"])(a0, a1, a2); var _AVFrame_channel_layoutmask = Module["_AVFrame_channel_layoutmask"] = a0 => (_AVFrame_channel_layoutmask = Module["_AVFrame_channel_layoutmask"] = wasmExports["ra"])(a0); var _AVFrame_channels = Module["_AVFrame_channels"] = a0 => (_AVFrame_channels = Module["_AVFrame_channels"] = wasmExports["sa"])(a0); var _AVFrame_channels_s = Module["_AVFrame_channels_s"] = (a0, a1) => (_AVFrame_channels_s = Module["_AVFrame_channels_s"] = wasmExports["ta"])(a0, a1); var _AVFrame_ch_layout_nb_channels = Module["_AVFrame_ch_layout_nb_channels"] = a0 => (_AVFrame_ch_layout_nb_channels = Module["_AVFrame_ch_layout_nb_channels"] = wasmExports["ua"])(a0); var _AVFrame_ch_layout_nb_channels_s = Module["_AVFrame_ch_layout_nb_channels_s"] = (a0, a1) => (_AVFrame_ch_layout_nb_channels_s = Module["_AVFrame_ch_layout_nb_channels_s"] = wasmExports["va"])(a0, a1); var _AVFrame_channel_layout = Module["_AVFrame_channel_layout"] = a0 => (_AVFrame_channel_layout = Module["_AVFrame_channel_layout"] = wasmExports["wa"])(a0); var _AVFrame_channel_layouthi = Module["_AVFrame_channel_layouthi"] = a0 => (_AVFrame_channel_layouthi = Module["_AVFrame_channel_layouthi"] = wasmExports["xa"])(a0); var _AVFrame_channel_layout_s = Module["_AVFrame_channel_layout_s"] = (a0, a1) => (_AVFrame_channel_layout_s = Module["_AVFrame_channel_layout_s"] = wasmExports["ya"])(a0, a1); var _AVFrame_channel_layouthi_s = Module["_AVFrame_channel_layouthi_s"] = (a0, a1) => (_AVFrame_channel_layouthi_s = Module["_AVFrame_channel_layouthi_s"] = wasmExports["za"])(a0, a1); var _ff_frame_rescale_ts_js = Module["_ff_frame_rescale_ts_js"] = (a0, a1, a2, a3, a4) => (_ff_frame_rescale_ts_js = Module["_ff_frame_rescale_ts_js"] = wasmExports["Aa"])(a0, a1, a2, a3, a4); var _AVPixFmtDescriptor_flags = Module["_AVPixFmtDescriptor_flags"] = a0 => (_AVPixFmtDescriptor_flags = Module["_AVPixFmtDescriptor_flags"] = wasmExports["Ba"])(a0); var _AVPixFmtDescriptor_flags_s = Module["_AVPixFmtDescriptor_flags_s"] = (a0, a1, a2) => (_AVPixFmtDescriptor_flags_s = Module["_AVPixFmtDescriptor_flags_s"] = wasmExports["Ca"])(a0, a1, a2); var _AVPixFmtDescriptor_nb_components = Module["_AVPixFmtDescriptor_nb_components"] = a0 => (_AVPixFmtDescriptor_nb_components = Module["_AVPixFmtDescriptor_nb_components"] = wasmExports["Da"])(a0); var _AVPixFmtDescriptor_nb_components_s = Module["_AVPixFmtDescriptor_nb_components_s"] = (a0, a1) => (_AVPixFmtDescriptor_nb_components_s = Module["_AVPixFmtDescriptor_nb_components_s"] = wasmExports["Ea"])(a0, a1); var _AVPixFmtDescriptor_log2_chroma_h = Module["_AVPixFmtDescriptor_log2_chroma_h"] = a0 => (_AVPixFmtDescriptor_log2_chroma_h = Module["_AVPixFmtDescriptor_log2_chroma_h"] = wasmExports["Fa"])(a0); var _AVPixFmtDescriptor_log2_chroma_h_s = Module["_AVPixFmtDescriptor_log2_chroma_h_s"] = (a0, a1) => (_AVPixFmtDescriptor_log2_chroma_h_s = Module["_AVPixFmtDescriptor_log2_chroma_h_s"] = wasmExports["Ga"])(a0, a1); var _AVPixFmtDescriptor_log2_chroma_w = Module["_AVPixFmtDescriptor_log2_chroma_w"] = a0 => (_AVPixFmtDescriptor_log2_chroma_w = Module["_AVPixFmtDescriptor_log2_chroma_w"] = wasmExports["Ha"])(a0); var _AVPixFmtDescriptor_log2_chroma_w_s = Module["_AVPixFmtDescriptor_log2_chroma_w_s"] = (a0, a1) => (_AVPixFmtDescriptor_log2_chroma_w_s = Module["_AVPixFmtDescriptor_log2_chroma_w_s"] = wasmExports["Ia"])(a0, a1); var _AVPixFmtDescriptor_comp_depth = Module["_AVPixFmtDescriptor_comp_depth"] = (a0, a1) => (_AVPixFmtDescriptor_comp_depth = Module["_AVPixFmtDescriptor_comp_depth"] = wasmExports["Ja"])(a0, a1); var _av_opt_set_int_list_js = Module["_av_opt_set_int_list_js"] = (a0, a1, a2, a3, a4, a5) => (_av_opt_set_int_list_js = Module["_av_opt_set_int_list_js"] = wasmExports["Ka"])(a0, a1, a2, a3, a4, a5); var _AVCodec_name = Module["_AVCodec_name"] = a0 => (_AVCodec_name = Module["_AVCodec_name"] = wasmExports["La"])(a0); var _AVCodec_sample_fmts = Module["_AVCodec_sample_fmts"] = a0 => (_AVCodec_sample_fmts = Module["_AVCodec_sample_fmts"] = wasmExports["Ma"])(a0); var _AVCodec_sample_fmts_s = Module["_AVCodec_sample_fmts_s"] = (a0, a1) => (_AVCodec_sample_fmts_s = Module["_AVCodec_sample_fmts_s"] = wasmExports["Na"])(a0, a1); var _AVCodec_sample_fmts_a = Module["_AVCodec_sample_fmts_a"] = (a0, a1) => (_AVCodec_sample_fmts_a = Module["_AVCodec_sample_fmts_a"] = wasmExports["Oa"])(a0, a1); var _AVCodec_sample_fmts_a_s = Module["_AVCodec_sample_fmts_a_s"] = (a0, a1, a2) => (_AVCodec_sample_fmts_a_s = Module["_AVCodec_sample_fmts_a_s"] = wasmExports["Pa"])(a0, a1, a2); var _AVCodec_supported_samplerates = Module["_AVCodec_supported_samplerates"] = a0 => (_AVCodec_supported_samplerates = Module["_AVCodec_supported_samplerates"] = wasmExports["Qa"])(a0); var _AVCodec_supported_samplerates_s = Module["_AVCodec_supported_samplerates_s"] = (a0, a1) => (_AVCodec_supported_samplerates_s = Module["_AVCodec_supported_samplerates_s"] = wasmExports["Ra"])(a0, a1); var _AVCodec_supported_samplerates_a = Module["_AVCodec_supported_samplerates_a"] = (a0, a1) => (_AVCodec_supported_samplerates_a = Module["_AVCodec_supported_samplerates_a"] = wasmExports["Sa"])(a0, a1); var _AVCodec_supported_samplerates_a_s = Module["_AVCodec_supported_samplerates_a_s"] = (a0, a1, a2) => (_AVCodec_supported_samplerates_a_s = Module["_AVCodec_supported_samplerates_a_s"] = wasmExports["Ta"])(a0, a1, a2); var _AVCodec_type = Module["_AVCodec_type"] = a0 => (_AVCodec_type = Module["_AVCodec_type"] = wasmExports["Ua"])(a0); var _AVCodec_type_s = Module["_AVCodec_type_s"] = (a0, a1) => (_AVCodec_type_s = Module["_AVCodec_type_s"] = wasmExports["Va"])(a0, a1); var _AVCodecContext_codec_id = Module["_AVCodecContext_codec_id"] = a0 => (_AVCodecContext_codec_id = Module["_AVCodecContext_codec_id"] = wasmExports["Wa"])(a0); var _AVCodecContext_codec_id_s = Module["_AVCodecContext_codec_id_s"] = (a0, a1) => (_AVCodecContext_codec_id_s = Module["_AVCodecContext_codec_id_s"] = wasmExports["Xa"])(a0, a1); var _AVCodecContext_codec_type = Module["_AVCodecContext_codec_type"] = a0 => (_AVCodecContext_codec_type = Module["_AVCodecContext_codec_type"] = wasmExports["Ya"])(a0); var _AVCodecContext_codec_type_s = Module["_AVCodecContext_codec_type_s"] = (a0, a1) => (_AVCodecContext_codec_type_s = Module["_AVCodecContext_codec_type_s"] = wasmExports["Za"])(a0, a1); var _AVCodecContext_bit_rate = Module["_AVCodecContext_bit_rate"] = a0 => (_AVCodecContext_bit_rate = Module["_AVCodecContext_bit_rate"] = wasmExports["_a"])(a0); var _AVCodecContext_bit_ratehi = Module["_AVCodecContext_bit_ratehi"] = a0 => (_AVCodecContext_bit_ratehi = Module["_AVCodecContext_bit_ratehi"] = wasmExports["$a"])(a0); var _AVCodecContext_bit_rate_s = Module["_AVCodecContext_bit_rate_s"] = (a0, a1) => (_AVCodecContext_bit_rate_s = Module["_AVCodecContext_bit_rate_s"] = wasmExports["ab"])(a0, a1); var _AVCodecContext_bit_ratehi_s = Module["_AVCodecContext_bit_ratehi_s"] = (a0, a1) => (_AVCodecContext_bit_ratehi_s = Module["_AVCodecContext_bit_ratehi_s"] = wasmExports["bb"])(a0, a1); var _AVCodecContext_extradata = Module["_AVCodecContext_extradata"] = a0 => (_AVCodecContext_extradata = Module["_AVCodecContext_extradata"] = wasmExports["cb"])(a0); var _AVCodecContext_extradata_s = Module["_AVCodecContext_extradata_s"] = (a0, a1) => (_AVCodecContext_extradata_s = Module["_AVCodecContext_extradata_s"] = wasmExports["db"])(a0, a1); var _AVCodecContext_extradata_size = Module["_AVCodecContext_extradata_size"] = a0 => (_AVCodecContext_extradata_size = Module["_AVCodecContext_extradata_size"] = wasmExports["eb"])(a0); var _AVCodecContext_extradata_size_s = Module["_AVCodecContext_extradata_size_s"] = (a0, a1) => (_AVCodecContext_extradata_size_s = Module["_AVCodecContext_extradata_size_s"] = wasmExports["fb"])(a0, a1); var _AVCodecContext_frame_size = Module["_AVCodecContext_frame_size"] = a0 => (_AVCodecContext_frame_size = Module["_AVCodecContext_frame_size"] = wasmExports["gb"])(a0); var _AVCodecContext_frame_size_s = Module["_AVCodecContext_frame_size_s"] = (a0, a1) => (_AVCodecContext_frame_size_s = Module["_AVCodecContext_frame_size_s"] = wasmExports["hb"])(a0, a1); var _AVCodecContext_gop_size = Module["_AVCodecContext_gop_size"] = a0 => (_AVCodecContext_gop_size = Module["_AVCodecContext_gop_size"] = wasmExports["ib"])(a0); var _AVCodecContext_gop_size_s = Module["_AVCodecContext_gop_size_s"] = (a0, a1) => (_AVCodecContext_gop_size_s = Module["_AVCodecContext_gop_size_s"] = wasmExports["jb"])(a0, a1); var _AVCodecContext_height = Module["_AVCodecContext_height"] = a0 => (_AVCodecContext_height = Module["_AVCodecContext_height"] = wasmExports["kb"])(a0); var _AVCodecContext_height_s = Module["_AVCodecContext_height_s"] = (a0, a1) => (_AVCodecContext_height_s = Module["_AVCodecContext_height_s"] = wasmExports["lb"])(a0, a1); var _AVCodecContext_keyint_min = Module["_AVCodecContext_keyint_min"] = a0 => (_AVCodecContext_keyint_min = Module["_AVCodecContext_keyint_min"] = wasmExports["mb"])(a0); var _AVCodecContext_keyint_min_s = Module["_AVCodecContext_keyint_min_s"] = (a0, a1) => (_AVCodecContext_keyint_min_s = Module["_AVCodecContext_keyint_min_s"] = wasmExports["nb"])(a0, a1); var _AVCodecContext_level = Module["_AVCodecContext_level"] = a0 => (_AVCodecContext_level = Module["_AVCodecContext_level"] = wasmExports["ob"])(a0); var _AVCodecContext_level_s = Module["_AVCodecContext_level_s"] = (a0, a1) => (_AVCodecContext_level_s = Module["_AVCodecContext_level_s"] = wasmExports["pb"])(a0, a1); var _AVCodecContext_max_b_frames = Module["_AVCodecContext_max_b_frames"] = a0 => (_AVCodecContext_max_b_frames = Module["_AVCodecContext_max_b_frames"] = wasmExports["qb"])(a0); var _AVCodecContext_max_b_frames_s = Module["_AVCodecContext_max_b_frames_s"] = (a0, a1) => (_AVCodecContext_max_b_frames_s = Module["_AVCodecContext_max_b_frames_s"] = wasmExports["rb"])(a0, a1); var _AVCodecContext_pix_fmt = Module["_AVCodecContext_pix_fmt"] = a0 => (_AVCodecContext_pix_fmt = Module["_AVCodecContext_pix_fmt"] = wasmExports["sb"])(a0); var _AVCodecContext_pix_fmt_s = Module["_AVCodecContext_pix_fmt_s"] = (a0, a1) => (_AVCodecContext_pix_fmt_s = Module["_AVCodecContext_pix_fmt_s"] = wasmExports["tb"])(a0, a1); var _AVCodecContext_profile = Module["_AVCodecContext_profile"] = a0 => (_AVCodecContext_profile = Module["_AVCodecContext_profile"] = wasmExports["ub"])(a0); var _AVCodecContext_profile_s = Module["_AVCodecContext_profile_s"] = (a0, a1) => (_AVCodecContext_profile_s = Module["_AVCodecContext_profile_s"] = wasmExports["vb"])(a0, a1); var _AVCodecContext_rc_max_rate = Module["_AVCodecContext_rc_max_rate"] = a0 => (_AVCodecContext_rc_max_rate = Module["_AVCodecContext_rc_max_rate"] = wasmExports["wb"])(a0); var _AVCodecContext_rc_max_ratehi = Module["_AVCodecContext_rc_max_ratehi"] = a0 => (_AVCodecContext_rc_max_ratehi = Module["_AVCodecContext_rc_max_ratehi"] = wasmExports["xb"])(a0); var _AVCodecContext_rc_max_rate_s = Module["_AVCodecContext_rc_max_rate_s"] = (a0, a1) => (_AVCodecContext_rc_max_rate_s = Module["_AVCodecContext_rc_max_rate_s"] = wasmExports["yb"])(a0, a1); var _AVCodecContext_rc_max_ratehi_s = Module["_AVCodecContext_rc_max_ratehi_s"] = (a0, a1) => (_AVCodecContext_rc_max_ratehi_s = Module["_AVCodecContext_rc_max_ratehi_s"] = wasmExports["zb"])(a0, a1); var _AVCodecContext_rc_min_rate = Module["_AVCodecContext_rc_min_rate"] = a0 => (_AVCodecContext_rc_min_rate = Module["_AVCodecContext_rc_min_rate"] = wasmExports["Ab"])(a0); var _AVCodecContext_rc_min_ratehi = Module["_AVCodecContext_rc_min_ratehi"] = a0 => (_AVCodecContext_rc_min_ratehi = Module["_AVCodecContext_rc_min_ratehi"] = wasmExports["Bb"])(a0); var _AVCodecContext_rc_min_rate_s = Module["_AVCodecContext_rc_min_rate_s"] = (a0, a1) => (_AVCodecContext_rc_min_rate_s = Module["_AVCodecContext_rc_min_rate_s"] = wasmExports["Cb"])(a0, a1); var _AVCodecContext_rc_min_ratehi_s = Module["_AVCodecContext_rc_min_ratehi_s"] = (a0, a1) => (_AVCodecContext_rc_min_ratehi_s = Module["_AVCodecContext_rc_min_ratehi_s"] = wasmExports["Db"])(a0, a1); var _AVCodecContext_sample_fmt = Module["_AVCodecContext_sample_fmt"] = a0 => (_AVCodecContext_sample_fmt = Module["_AVCodecContext_sample_fmt"] = wasmExports["Eb"])(a0); var _AVCodecContext_sample_fmt_s = Module["_AVCodecContext_sample_fmt_s"] = (a0, a1) => (_AVCodecContext_sample_fmt_s = Module["_AVCodecContext_sample_fmt_s"] = wasmExports["Fb"])(a0, a1); var _AVCodecContext_sample_rate = Module["_AVCodecContext_sample_rate"] = a0 => (_AVCodecContext_sample_rate = Module["_AVCodecContext_sample_rate"] = wasmExports["Gb"])(a0); var _AVCodecContext_sample_rate_s = Module["_AVCodecContext_sample_rate_s"] = (a0, a1) => (_AVCodecContext_sample_rate_s = Module["_AVCodecContext_sample_rate_s"] = wasmExports["Hb"])(a0, a1); var _AVCodecContext_qmax = Module["_AVCodecContext_qmax"] = a0 => (_AVCodecContext_qmax = Module["_AVCodecContext_qmax"] = wasmExports["Ib"])(a0); var _AVCodecContext_qmax_s = Module["_AVCodecContext_qmax_s"] = (a0, a1) => (_AVCodecContext_qmax_s = Module["_AVCodecContext_qmax_s"] = wasmExports["Jb"])(a0, a1); var _AVCodecContext_qmin = Module["_AVCodecContext_qmin"] = a0 => (_AVCodecContext_qmin = Module["_AVCodecContext_qmin"] = wasmExports["Kb"])(a0); var _AVCodecContext_qmin_s = Module["_AVCodecContext_qmin_s"] = (a0, a1) => (_AVCodecContext_qmin_s = Module["_AVCodecContext_qmin_s"] = wasmExports["Lb"])(a0, a1); var _AVCodecContext_width = Module["_AVCodecContext_width"] = a0 => (_AVCodecContext_width = Module["_AVCodecContext_width"] = wasmExports["Mb"])(a0); var _AVCodecContext_width_s = Module["_AVCodecContext_width_s"] = (a0, a1) => (_AVCodecContext_width_s = Module["_AVCodecContext_width_s"] = wasmExports["Nb"])(a0, a1); var _AVCodecContext_framerate_num = Module["_AVCodecContext_framerate_num"] = a0 => (_AVCodecContext_framerate_num = Module["_AVCodecContext_framerate_num"] = wasmExports["Ob"])(a0); var _AVCodecContext_framerate_den = Module["_AVCodecContext_framerate_den"] = a0 => (_AVCodecContext_framerate_den = Module["_AVCodecContext_framerate_den"] = wasmExports["Pb"])(a0); var _AVCodecContext_framerate_num_s = Module["_AVCodecContext_framerate_num_s"] = (a0, a1) => (_AVCodecContext_framerate_num_s = Module["_AVCodecContext_framerate_num_s"] = wasmExports["Qb"])(a0, a1); var _AVCodecContext_framerate_den_s = Module["_AVCodecContext_framerate_den_s"] = (a0, a1) => (_AVCodecContext_framerate_den_s = Module["_AVCodecContext_framerate_den_s"] = wasmExports["Rb"])(a0, a1); var _AVCodecContext_framerate_s = Module["_AVCodecContext_framerate_s"] = (a0, a1, a2) => (_AVCodecContext_framerate_s = Module["_AVCodecContext_framerate_s"] = wasmExports["Sb"])(a0, a1, a2); var _AVCodecContext_sample_aspect_ratio_num = Module["_AVCodecContext_sample_aspect_ratio_num"] = a0 => (_AVCodecContext_sample_aspect_ratio_num = Module["_AVCodecContext_sample_aspect_ratio_num"] = wasmExports["Tb"])(a0); var _AVCodecContext_sample_aspect_ratio_den = Module["_AVCodecContext_sample_aspect_ratio_den"] = a0 => (_AVCodecContext_sample_aspect_ratio_den = Module["_AVCodecContext_sample_aspect_ratio_den"] = wasmExports["Ub"])(a0); var _AVCodecContext_sample_aspect_ratio_num_s = Module["_AVCodecContext_sample_aspect_ratio_num_s"] = (a0, a1) => (_AVCodecContext_sample_aspect_ratio_num_s = Module["_AVCodecContext_sample_aspect_ratio_num_s"] = wasmExports["Vb"])(a0, a1); var _AVCodecContext_sample_aspect_ratio_den_s = Module["_AVCodecContext_sample_aspect_ratio_den_s"] = (a0, a1) => (_AVCodecContext_sample_aspect_ratio_den_s = Module["_AVCodecContext_sample_aspect_ratio_den_s"] = wasmExports["Wb"])(a0, a1); var _AVCodecContext_sample_aspect_ratio_s = Module["_AVCodecContext_sample_aspect_ratio_s"] = (a0, a1, a2) => (_AVCodecContext_sample_aspect_ratio_s = Module["_AVCodecContext_sample_aspect_ratio_s"] = wasmExports["Xb"])(a0, a1, a2); var _AVCodecContext_time_base_num = Module["_AVCodecContext_time_base_num"] = a0 => (_AVCodecContext_time_base_num = Module["_AVCodecContext_time_base_num"] = wasmExports["Yb"])(a0); var _AVCodecContext_time_base_den = Module["_AVCodecContext_time_base_den"] = a0 => (_AVCodecContext_time_base_den = Module["_AVCodecContext_time_base_den"] = wasmExports["Zb"])(a0); var _AVCodecContext_time_base_num_s = Module["_AVCodecContext_time_base_num_s"] = (a0, a1) => (_AVCodecContext_time_base_num_s = Module["_AVCodecContext_time_base_num_s"] = wasmExports["_b"])(a0, a1); var _AVCodecContext_time_base_den_s = Module["_AVCodecContext_time_base_den_s"] = (a0, a1) => (_AVCodecContext_time_base_den_s = Module["_AVCodecContext_time_base_den_s"] = wasmExports["$b"])(a0, a1); var _AVCodecContext_time_base_s = Module["_AVCodecContext_time_base_s"] = (a0, a1, a2) => (_AVCodecContext_time_base_s = Module["_AVCodecContext_time_base_s"] = wasmExports["ac"])(a0, a1, a2); var _AVCodecContext_channel_layoutmask_s = Module["_AVCodecContext_channel_layoutmask_s"] = (a0, a1, a2) => (_AVCodecContext_channel_layoutmask_s = Module["_AVCodecContext_channel_layoutmask_s"] = wasmExports["bc"])(a0, a1, a2); var _AVCodecContext_channel_layoutmask = Module["_AVCodecContext_channel_layoutmask"] = a0 => (_AVCodecContext_channel_layoutmask = Module["_AVCodecContext_channel_layoutmask"] = wasmExports["cc"])(a0); var _AVCodecContext_channels = Module["_AVCodecContext_channels"] = a0 => (_AVCodecContext_channels = Module["_AVCodecContext_channels"] = wasmExports["dc"])(a0); var _AVCodecContext_channels_s = Module["_AVCodecContext_channels_s"] = (a0, a1) => (_AVCodecContext_channels_s = Module["_AVCodecContext_channels_s"] = wasmExports["ec"])(a0, a1); var _AVCodecContext_ch_layout_nb_channels = Module["_AVCodecContext_ch_layout_nb_channels"] = a0 => (_AVCodecContext_ch_layout_nb_channels = Module["_AVCodecContext_ch_layout_nb_channels"] = wasmExports["fc"])(a0); var _AVCodecContext_ch_layout_nb_channels_s = Module["_AVCodecContext_ch_layout_nb_channels_s"] = (a0, a1) => (_AVCodecContext_ch_layout_nb_channels_s = Module["_AVCodecContext_ch_layout_nb_channels_s"] = wasmExports["gc"])(a0, a1); var _AVCodecContext_channel_layout = Module["_AVCodecContext_channel_layout"] = a0 => (_AVCodecContext_channel_layout = Module["_AVCodecContext_channel_layout"] = wasmExports["hc"])(a0); var _AVCodecContext_channel_layouthi = Module["_AVCodecContext_channel_layouthi"] = a0 => (_AVCodecContext_channel_layouthi = Module["_AVCodecContext_channel_layouthi"] = wasmExports["ic"])(a0); var _AVCodecContext_channel_layout_s = Module["_AVCodecContext_channel_layout_s"] = (a0, a1) => (_AVCodecContext_channel_layout_s = Module["_AVCodecContext_channel_layout_s"] = wasmExports["jc"])(a0, a1); var _AVCodecContext_channel_layouthi_s = Module["_AVCodecContext_channel_layouthi_s"] = (a0, a1) => (_AVCodecContext_channel_layouthi_s = Module["_AVCodecContext_channel_layouthi_s"] = wasmExports["kc"])(a0, a1); var _AVCodecDescriptor_id = Module["_AVCodecDescriptor_id"] = a0 => (_AVCodecDescriptor_id = Module["_AVCodecDescriptor_id"] = wasmExports["lc"])(a0); var _AVCodecDescriptor_id_s = Module["_AVCodecDescriptor_id_s"] = (a0, a1) => (_AVCodecDescriptor_id_s = Module["_AVCodecDescriptor_id_s"] = wasmExports["mc"])(a0, a1); var _AVCodecDescriptor_long_name = Module["_AVCodecDescriptor_long_name"] = a0 => (_AVCodecDescriptor_long_name = Module["_AVCodecDescriptor_long_name"] = wasmExports["nc"])(a0); var _AVCodecDescriptor_long_name_s = Module["_AVCodecDescriptor_long_name_s"] = (a0, a1) => (_AVCodecDescriptor_long_name_s = Module["_AVCodecDescriptor_long_name_s"] = wasmExports["oc"])(a0, a1); var _AVCodecDescriptor_mime_types_a = Module["_AVCodecDescriptor_mime_types_a"] = (a0, a1) => (_AVCodecDescriptor_mime_types_a = Module["_AVCodecDescriptor_mime_types_a"] = wasmExports["pc"])(a0, a1); var _AVCodecDescriptor_mime_types_a_s = Module["_AVCodecDescriptor_mime_types_a_s"] = (a0, a1, a2) => (_AVCodecDescriptor_mime_types_a_s = Module["_AVCodecDescriptor_mime_types_a_s"] = wasmExports["qc"])(a0, a1, a2); var _AVCodecDescriptor_name = Module["_AVCodecDescriptor_name"] = a0 => (_AVCodecDescriptor_name = Module["_AVCodecDescriptor_name"] = wasmExports["rc"])(a0); var _AVCodecDescriptor_name_s = Module["_AVCodecDescriptor_name_s"] = (a0, a1) => (_AVCodecDescriptor_name_s = Module["_AVCodecDescriptor_name_s"] = wasmExports["sc"])(a0, a1); var _AVCodecDescriptor_props = Module["_AVCodecDescriptor_props"] = a0 => (_AVCodecDescriptor_props = Module["_AVCodecDescriptor_props"] = wasmExports["tc"])(a0); var _AVCodecDescriptor_props_s = Module["_AVCodecDescriptor_props_s"] = (a0, a1) => (_AVCodecDescriptor_props_s = Module["_AVCodecDescriptor_props_s"] = wasmExports["uc"])(a0, a1); var _AVCodecDescriptor_type = Module["_AVCodecDescriptor_type"] = a0 => (_AVCodecDescriptor_type = Module["_AVCodecDescriptor_type"] = wasmExports["vc"])(a0); var _AVCodecDescriptor_type_s = Module["_AVCodecDescriptor_type_s"] = (a0, a1) => (_AVCodecDescriptor_type_s = Module["_AVCodecDescriptor_type_s"] = wasmExports["wc"])(a0, a1); var _AVCodecParameters_codec_id = Module["_AVCodecParameters_codec_id"] = a0 => (_AVCodecParameters_codec_id = Module["_AVCodecParameters_codec_id"] = wasmExports["xc"])(a0); var _AVCodecParameters_codec_id_s = Module["_AVCodecParameters_codec_id_s"] = (a0, a1) => (_AVCodecParameters_codec_id_s = Module["_AVCodecParameters_codec_id_s"] = wasmExports["yc"])(a0, a1); var _AVCodecParameters_codec_tag = Module["_AVCodecParameters_codec_tag"] = a0 => (_AVCodecParameters_codec_tag = Module["_AVCodecParameters_codec_tag"] = wasmExports["zc"])(a0); var _AVCodecParameters_codec_tag_s = Module["_AVCodecParameters_codec_tag_s"] = (a0, a1) => (_AVCodecParameters_codec_tag_s = Module["_AVCodecParameters_codec_tag_s"] = wasmExports["Ac"])(a0, a1); var _AVCodecParameters_codec_type = Module["_AVCodecParameters_codec_type"] = a0 => (_AVCodecParameters_codec_type = Module["_AVCodecParameters_codec_type"] = wasmExports["Bc"])(a0); var _AVCodecParameters_codec_type_s = Module["_AVCodecParameters_codec_type_s"] = (a0, a1) => (_AVCodecParameters_codec_type_s = Module["_AVCodecParameters_codec_type_s"] = wasmExports["Cc"])(a0, a1); var _AVCodecParameters_extradata = Module["_AVCodecParameters_extradata"] = a0 => (_AVCodecParameters_extradata = Module["_AVCodecParameters_extradata"] = wasmExports["Dc"])(a0); var _AVCodecParameters_extradata_s = Module["_AVCodecParameters_extradata_s"] = (a0, a1) => (_AVCodecParameters_extradata_s = Module["_AVCodecParameters_extradata_s"] = wasmExports["Ec"])(a0, a1); var _AVCodecParameters_extradata_size = Module["_AVCodecParameters_extradata_size"] = a0 => (_AVCodecParameters_extradata_size = Module["_AVCodecParameters_extradata_size"] = wasmExports["Fc"])(a0); var _AVCodecParameters_extradata_size_s = Module["_AVCodecParameters_extradata_size_s"] = (a0, a1) => (_AVCodecParameters_extradata_size_s = Module["_AVCodecParameters_extradata_size_s"] = wasmExports["Gc"])(a0, a1); var _AVCodecParameters_format = Module["_AVCodecParameters_format"] = a0 => (_AVCodecParameters_format = Module["_AVCodecParameters_format"] = wasmExports["Hc"])(a0); var _AVCodecParameters_format_s = Module["_AVCodecParameters_format_s"] = (a0, a1) => (_AVCodecParameters_format_s = Module["_AVCodecParameters_format_s"] = wasmExports["Ic"])(a0, a1); var _AVCodecParameters_bit_rate = Module["_AVCodecParameters_bit_rate"] = a0 => (_AVCodecParameters_bit_rate = Module["_AVCodecParameters_bit_rate"] = wasmExports["Jc"])(a0); var _AVCodecParameters_bit_rate_s = Module["_AVCodecParameters_bit_rate_s"] = (a0, a1, a2) => (_AVCodecParameters_bit_rate_s = Module["_AVCodecParameters_bit_rate_s"] = wasmExports["Kc"])(a0, a1, a2); var _AVCodecParameters_profile = Module["_AVCodecParameters_profile"] = a0 => (_AVCodecParameters_profile = Module["_AVCodecParameters_profile"] = wasmExports["Lc"])(a0); var _AVCodecParameters_profile_s = Module["_AVCodecParameters_profile_s"] = (a0, a1) => (_AVCodecParameters_profile_s = Module["_AVCodecParameters_profile_s"] = wasmExports["Mc"])(a0, a1); var _AVCodecParameters_level = Module["_AVCodecParameters_level"] = a0 => (_AVCodecParameters_level = Module["_AVCodecParameters_level"] = wasmExports["Nc"])(a0); var _AVCodecParameters_level_s = Module["_AVCodecParameters_level_s"] = (a0, a1) => (_AVCodecParameters_level_s = Module["_AVCodecParameters_level_s"] = wasmExports["Oc"])(a0, a1); var _AVCodecParameters_width = Module["_AVCodecParameters_width"] = a0 => (_AVCodecParameters_width = Module["_AVCodecParameters_width"] = wasmExports["Pc"])(a0); var _AVCodecParameters_width_s = Module["_AVCodecParameters_width_s"] = (a0, a1) => (_AVCodecParameters_width_s = Module["_AVCodecParameters_width_s"] = wasmExports["Qc"])(a0, a1); var _AVCodecParameters_height = Module["_AVCodecParameters_height"] = a0 => (_AVCodecParameters_height = Module["_AVCodecParameters_height"] = wasmExports["Rc"])(a0); var _AVCodecParameters_height_s = Module["_AVCodecParameters_height_s"] = (a0, a1) => (_AVCodecParameters_height_s = Module["_AVCodecParameters_height_s"] = wasmExports["Sc"])(a0, a1); var _AVCodecParameters_color_range = Module["_AVCodecParameters_color_range"] = a0 => (_AVCodecParameters_color_range = Module["_AVCodecParameters_color_range"] = wasmExports["Tc"])(a0); var _AVCodecParameters_color_range_s = Module["_AVCodecParameters_color_range_s"] = (a0, a1) => (_AVCodecParameters_color_range_s = Module["_AVCodecParameters_color_range_s"] = wasmExports["Uc"])(a0, a1); var _AVCodecParameters_color_primaries = Module["_AVCodecParameters_color_primaries"] = a0 => (_AVCodecParameters_color_primaries = Module["_AVCodecParameters_color_primaries"] = wasmExports["Vc"])(a0); var _AVCodecParameters_color_primaries_s = Module["_AVCodecParameters_color_primaries_s"] = (a0, a1) => (_AVCodecParameters_color_primaries_s = Module["_AVCodecParameters_color_primaries_s"] = wasmExports["Wc"])(a0, a1); var _AVCodecParameters_color_trc = Module["_AVCodecParameters_color_trc"] = a0 => (_AVCodecParameters_color_trc = Module["_AVCodecParameters_color_trc"] = wasmExports["Xc"])(a0); var _AVCodecParameters_color_trc_s = Module["_AVCodecParameters_color_trc_s"] = (a0, a1) => (_AVCodecParameters_color_trc_s = Module["_AVCodecParameters_color_trc_s"] = wasmExports["Yc"])(a0, a1); var _AVCodecParameters_color_space = Module["_AVCodecParameters_color_space"] = a0 => (_AVCodecParameters_color_space = Module["_AVCodecParameters_color_space"] = wasmExports["Zc"])(a0); var _AVCodecParameters_color_space_s = Module["_AVCodecParameters_color_space_s"] = (a0, a1) => (_AVCodecParameters_color_space_s = Module["_AVCodecParameters_color_space_s"] = wasmExports["_c"])(a0, a1); var _AVCodecParameters_chroma_location = Module["_AVCodecParameters_chroma_location"] = a0 => (_AVCodecParameters_chroma_location = Module["_AVCodecParameters_chroma_location"] = wasmExports["$c"])(a0); var _AVCodecParameters_chroma_location_s = Module["_AVCodecParameters_chroma_location_s"] = (a0, a1) => (_AVCodecParameters_chroma_location_s = Module["_AVCodecParameters_chroma_location_s"] = wasmExports["ad"])(a0, a1); var _AVCodecParameters_sample_rate = Module["_AVCodecParameters_sample_rate"] = a0 => (_AVCodecParameters_sample_rate = Module["_AVCodecParameters_sample_rate"] = wasmExports["bd"])(a0); var _AVCodecParameters_sample_rate_s = Module["_AVCodecParameters_sample_rate_s"] = (a0, a1) => (_AVCodecParameters_sample_rate_s = Module["_AVCodecParameters_sample_rate_s"] = wasmExports["cd"])(a0, a1); var _AVCodecParameters_framerate_num = Module["_AVCodecParameters_framerate_num"] = a0 => (_AVCodecParameters_framerate_num = Module["_AVCodecParameters_framerate_num"] = wasmExports["dd"])(a0); var _AVCodecParameters_framerate_den = Module["_AVCodecParameters_framerate_den"] = a0 => (_AVCodecParameters_framerate_den = Module["_AVCodecParameters_framerate_den"] = wasmExports["ed"])(a0); var _AVCodecParameters_framerate_num_s = Module["_AVCodecParameters_framerate_num_s"] = (a0, a1) => (_AVCodecParameters_framerate_num_s = Module["_AVCodecParameters_framerate_num_s"] = wasmExports["fd"])(a0, a1); var _AVCodecParameters_framerate_den_s = Module["_AVCodecParameters_framerate_den_s"] = (a0, a1) => (_AVCodecParameters_framerate_den_s = Module["_AVCodecParameters_framerate_den_s"] = wasmExports["gd"])(a0, a1); var _AVCodecParameters_framerate_s = Module["_AVCodecParameters_framerate_s"] = (a0, a1, a2) => (_AVCodecParameters_framerate_s = Module["_AVCodecParameters_framerate_s"] = wasmExports["hd"])(a0, a1, a2); var _AVCodecParameters_channel_layoutmask_s = Module["_AVCodecParameters_channel_layoutmask_s"] = (a0, a1, a2) => (_AVCodecParameters_channel_layoutmask_s = Module["_AVCodecParameters_channel_layoutmask_s"] = wasmExports["id"])(a0, a1, a2); var _AVCodecParameters_channel_layoutmask = Module["_AVCodecParameters_channel_layoutmask"] = a0 => (_AVCodecParameters_channel_layoutmask = Module["_AVCodecParameters_channel_layoutmask"] = wasmExports["jd"])(a0); var _AVCodecParameters_channels = Module["_AVCodecParameters_channels"] = a0 => (_AVCodecParameters_channels = Module["_AVCodecParameters_channels"] = wasmExports["kd"])(a0); var _AVCodecParameters_channels_s = Module["_AVCodecParameters_channels_s"] = (a0, a1) => (_AVCodecParameters_channels_s = Module["_AVCodecParameters_channels_s"] = wasmExports["ld"])(a0, a1); var _AVCodecParameters_ch_layout_nb_channels = Module["_AVCodecParameters_ch_layout_nb_channels"] = a0 => (_AVCodecParameters_ch_layout_nb_channels = Module["_AVCodecParameters_ch_layout_nb_channels"] = wasmExports["md"])(a0); var _AVCodecParameters_ch_layout_nb_channels_s = Module["_AVCodecParameters_ch_layout_nb_channels_s"] = (a0, a1) => (_AVCodecParameters_ch_layout_nb_channels_s = Module["_AVCodecParameters_ch_layout_nb_channels_s"] = wasmExports["nd"])(a0, a1); var _AVPacket_data = Module["_AVPacket_data"] = a0 => (_AVPacket_data = Module["_AVPacket_data"] = wasmExports["od"])(a0); var _AVPacket_data_s = Module["_AVPacket_data_s"] = (a0, a1) => (_AVPacket_data_s = Module["_AVPacket_data_s"] = wasmExports["pd"])(a0, a1); var _AVPacket_dts = Module["_AVPacket_dts"] = a0 => (_AVPacket_dts = Module["_AVPacket_dts"] = wasmExports["qd"])(a0); var _AVPacket_dtshi = Module["_AVPacket_dtshi"] = a0 => (_AVPacket_dtshi = Module["_AVPacket_dtshi"] = wasmExports["rd"])(a0); var _AVPacket_dts_s = Module["_AVPacket_dts_s"] = (a0, a1) => (_AVPacket_dts_s = Module["_AVPacket_dts_s"] = wasmExports["sd"])(a0, a1); var _AVPacket_dtshi_s = Module["_AVPacket_dtshi_s"] = (a0, a1) => (_AVPacket_dtshi_s = Module["_AVPacket_dtshi_s"] = wasmExports["td"])(a0, a1); var _AVPacket_duration = Module["_AVPacket_duration"] = a0 => (_AVPacket_duration = Module["_AVPacket_duration"] = wasmExports["ud"])(a0); var _AVPacket_durationhi = Module["_AVPacket_durationhi"] = a0 => (_AVPacket_durationhi = Module["_AVPacket_durationhi"] = wasmExports["vd"])(a0); var _AVPacket_duration_s = Module["_AVPacket_duration_s"] = (a0, a1) => (_AVPacket_duration_s = Module["_AVPacket_duration_s"] = wasmExports["wd"])(a0, a1); var _AVPacket_durationhi_s = Module["_AVPacket_durationhi_s"] = (a0, a1) => (_AVPacket_durationhi_s = Module["_AVPacket_durationhi_s"] = wasmExports["xd"])(a0, a1); var _AVPacket_flags = Module["_AVPacket_flags"] = a0 => (_AVPacket_flags = Module["_AVPacket_flags"] = wasmExports["yd"])(a0); var _AVPacket_flags_s = Module["_AVPacket_flags_s"] = (a0, a1) => (_AVPacket_flags_s = Module["_AVPacket_flags_s"] = wasmExports["zd"])(a0, a1); var _AVPacket_pos = Module["_AVPacket_pos"] = a0 => (_AVPacket_pos = Module["_AVPacket_pos"] = wasmExports["Ad"])(a0); var _AVPacket_poshi = Module["_AVPacket_poshi"] = a0 => (_AVPacket_poshi = Module["_AVPacket_poshi"] = wasmExports["Bd"])(a0); var _AVPacket_pos_s = Module["_AVPacket_pos_s"] = (a0, a1) => (_AVPacket_pos_s = Module["_AVPacket_pos_s"] = wasmExports["Cd"])(a0, a1); var _AVPacket_poshi_s = Module["_AVPacket_poshi_s"] = (a0, a1) => (_AVPacket_poshi_s = Module["_AVPacket_poshi_s"] = wasmExports["Dd"])(a0, a1); var _AVPacket_pts = Module["_AVPacket_pts"] = a0 => (_AVPacket_pts = Module["_AVPacket_pts"] = wasmExports["Ed"])(a0); var _AVPacket_ptshi = Module["_AVPacket_ptshi"] = a0 => (_AVPacket_ptshi = Module["_AVPacket_ptshi"] = wasmExports["Fd"])(a0); var _AVPacket_pts_s = Module["_AVPacket_pts_s"] = (a0, a1) => (_AVPacket_pts_s = Module["_AVPacket_pts_s"] = wasmExports["Gd"])(a0, a1); var _AVPacket_ptshi_s = Module["_AVPacket_ptshi_s"] = (a0, a1) => (_AVPacket_ptshi_s = Module["_AVPacket_ptshi_s"] = wasmExports["Hd"])(a0, a1); var _AVPacket_side_data = Module["_AVPacket_side_data"] = a0 => (_AVPacket_side_data = Module["_AVPacket_side_data"] = wasmExports["Id"])(a0); var _AVPacket_side_data_s = Module["_AVPacket_side_data_s"] = (a0, a1) => (_AVPacket_side_data_s = Module["_AVPacket_side_data_s"] = wasmExports["Jd"])(a0, a1); var _AVPacket_side_data_elems = Module["_AVPacket_side_data_elems"] = a0 => (_AVPacket_side_data_elems = Module["_AVPacket_side_data_elems"] = wasmExports["Kd"])(a0); var _AVPacket_side_data_elems_s = Module["_AVPacket_side_data_elems_s"] = (a0, a1) => (_AVPacket_side_data_elems_s = Module["_AVPacket_side_data_elems_s"] = wasmExports["Ld"])(a0, a1); var _AVPacket_size = Module["_AVPacket_size"] = a0 => (_AVPacket_size = Module["_AVPacket_size"] = wasmExports["Md"])(a0); var _AVPacket_size_s = Module["_AVPacket_size_s"] = (a0, a1) => (_AVPacket_size_s = Module["_AVPacket_size_s"] = wasmExports["Nd"])(a0, a1); var _AVPacket_stream_index = Module["_AVPacket_stream_index"] = a0 => (_AVPacket_stream_index = Module["_AVPacket_stream_index"] = wasmExports["Od"])(a0); var _AVPacket_stream_index_s = Module["_AVPacket_stream_index_s"] = (a0, a1) => (_AVPacket_stream_index_s = Module["_AVPacket_stream_index_s"] = wasmExports["Pd"])(a0, a1); var _AVPacket_time_base_num = Module["_AVPacket_time_base_num"] = a0 => (_AVPacket_time_base_num = Module["_AVPacket_time_base_num"] = wasmExports["Qd"])(a0); var _AVPacket_time_base_den = Module["_AVPacket_time_base_den"] = a0 => (_AVPacket_time_base_den = Module["_AVPacket_time_base_den"] = wasmExports["Rd"])(a0); var _AVPacket_time_base_num_s = Module["_AVPacket_time_base_num_s"] = (a0, a1) => (_AVPacket_time_base_num_s = Module["_AVPacket_time_base_num_s"] = wasmExports["Sd"])(a0, a1); var _AVPacket_time_base_den_s = Module["_AVPacket_time_base_den_s"] = (a0, a1) => (_AVPacket_time_base_den_s = Module["_AVPacket_time_base_den_s"] = wasmExports["Td"])(a0, a1); var _AVPacket_time_base_s = Module["_AVPacket_time_base_s"] = (a0, a1, a2) => (_AVPacket_time_base_s = Module["_AVPacket_time_base_s"] = wasmExports["Ud"])(a0, a1, a2); var _AVPacketSideData_data = Module["_AVPacketSideData_data"] = (a0, a1) => (_AVPacketSideData_data = Module["_AVPacketSideData_data"] = wasmExports["Vd"])(a0, a1); var _AVPacketSideData_size = Module["_AVPacketSideData_size"] = (a0, a1) => (_AVPacketSideData_size = Module["_AVPacketSideData_size"] = wasmExports["Wd"])(a0, a1); var _AVPacketSideData_type = Module["_AVPacketSideData_type"] = (a0, a1) => (_AVPacketSideData_type = Module["_AVPacketSideData_type"] = wasmExports["Xd"])(a0, a1); var _avcodec_open2_js = Module["_avcodec_open2_js"] = (a0, a1, a2) => (_avcodec_open2_js = Module["_avcodec_open2_js"] = wasmExports["Yd"])(a0, a1, a2); var _avcodec_open2 = Module["_avcodec_open2"] = (a0, a1, a2) => (_avcodec_open2 = Module["_avcodec_open2"] = wasmExports["Zd"])(a0, a1, a2); var _av_packet_rescale_ts_js = Module["_av_packet_rescale_ts_js"] = (a0, a1, a2, a3, a4) => (_av_packet_rescale_ts_js = Module["_av_packet_rescale_ts_js"] = wasmExports["_d"])(a0, a1, a2, a3, a4); var _AVFormatContext_duration = Module["_AVFormatContext_duration"] = a0 => (_AVFormatContext_duration = Module["_AVFormatContext_duration"] = wasmExports["$d"])(a0); var _AVFormatContext_durationhi = Module["_AVFormatContext_durationhi"] = a0 => (_AVFormatContext_durationhi = Module["_AVFormatContext_durationhi"] = wasmExports["ae"])(a0); var _AVFormatContext_duration_s = Module["_AVFormatContext_duration_s"] = (a0, a1) => (_AVFormatContext_duration_s = Module["_AVFormatContext_duration_s"] = wasmExports["be"])(a0, a1); var _AVFormatContext_durationhi_s = Module["_AVFormatContext_durationhi_s"] = (a0, a1) => (_AVFormatContext_durationhi_s = Module["_AVFormatContext_durationhi_s"] = wasmExports["ce"])(a0, a1); var _AVFormatContext_flags = Module["_AVFormatContext_flags"] = a0 => (_AVFormatContext_flags = Module["_AVFormatContext_flags"] = wasmExports["de"])(a0); var _AVFormatContext_flags_s = Module["_AVFormatContext_flags_s"] = (a0, a1) => (_AVFormatContext_flags_s = Module["_AVFormatContext_flags_s"] = wasmExports["ee"])(a0, a1); var _AVFormatContext_nb_streams = Module["_AVFormatContext_nb_streams"] = a0 => (_AVFormatContext_nb_streams = Module["_AVFormatContext_nb_streams"] = wasmExports["fe"])(a0); var _AVFormatContext_nb_streams_s = Module["_AVFormatContext_nb_streams_s"] = (a0, a1) => (_AVFormatContext_nb_streams_s = Module["_AVFormatContext_nb_streams_s"] = wasmExports["ge"])(a0, a1); var _AVFormatContext_oformat = Module["_AVFormatContext_oformat"] = a0 => (_AVFormatContext_oformat = Module["_AVFormatContext_oformat"] = wasmExports["he"])(a0); var _AVFormatContext_oformat_s = Module["_AVFormatContext_oformat_s"] = (a0, a1) => (_AVFormatContext_oformat_s = Module["_AVFormatContext_oformat_s"] = wasmExports["ie"])(a0, a1); var _AVFormatContext_pb = Module["_AVFormatContext_pb"] = a0 => (_AVFormatContext_pb = Module["_AVFormatContext_pb"] = wasmExports["je"])(a0); var _AVFormatContext_pb_s = Module["_AVFormatContext_pb_s"] = (a0, a1) => (_AVFormatContext_pb_s = Module["_AVFormatContext_pb_s"] = wasmExports["ke"])(a0, a1); var _AVFormatContext_start_time = Module["_AVFormatContext_start_time"] = a0 => (_AVFormatContext_start_time = Module["_AVFormatContext_start_time"] = wasmExports["le"])(a0); var _AVFormatContext_start_timehi = Module["_AVFormatContext_start_timehi"] = a0 => (_AVFormatContext_start_timehi = Module["_AVFormatContext_start_timehi"] = wasmExports["me"])(a0); var _AVFormatContext_start_time_s = Module["_AVFormatContext_start_time_s"] = (a0, a1) => (_AVFormatContext_start_time_s = Module["_AVFormatContext_start_time_s"] = wasmExports["ne"])(a0, a1); var _AVFormatContext_start_timehi_s = Module["_AVFormatContext_start_timehi_s"] = (a0, a1) => (_AVFormatContext_start_timehi_s = Module["_AVFormatContext_start_timehi_s"] = wasmExports["oe"])(a0, a1); var _AVFormatContext_streams_a = Module["_AVFormatContext_streams_a"] = (a0, a1) => (_AVFormatContext_streams_a = Module["_AVFormatContext_streams_a"] = wasmExports["pe"])(a0, a1); var _AVFormatContext_streams_a_s = Module["_AVFormatContext_streams_a_s"] = (a0, a1, a2) => (_AVFormatContext_streams_a_s = Module["_AVFormatContext_streams_a_s"] = wasmExports["qe"])(a0, a1, a2); var _AVStream_codecpar = Module["_AVStream_codecpar"] = a0 => (_AVStream_codecpar = Module["_AVStream_codecpar"] = wasmExports["re"])(a0); var _AVStream_codecpar_s = Module["_AVStream_codecpar_s"] = (a0, a1) => (_AVStream_codecpar_s = Module["_AVStream_codecpar_s"] = wasmExports["se"])(a0, a1); var _AVStream_discard = Module["_AVStream_discard"] = a0 => (_AVStream_discard = Module["_AVStream_discard"] = wasmExports["te"])(a0); var _AVStream_discard_s = Module["_AVStream_discard_s"] = (a0, a1) => (_AVStream_discard_s = Module["_AVStream_discard_s"] = wasmExports["ue"])(a0, a1); var _AVStream_duration = Module["_AVStream_duration"] = a0 => (_AVStream_duration = Module["_AVStream_duration"] = wasmExports["ve"])(a0); var _AVStream_durationhi = Module["_AVStream_durationhi"] = a0 => (_AVStream_durationhi = Module["_AVStream_durationhi"] = wasmExports["we"])(a0); var _AVStream_duration_s = Module["_AVStream_duration_s"] = (a0, a1) => (_AVStream_duration_s = Module["_AVStream_duration_s"] = wasmExports["xe"])(a0, a1); var _AVStream_durationhi_s = Module["_AVStream_durationhi_s"] = (a0, a1) => (_AVStream_durationhi_s = Module["_AVStream_durationhi_s"] = wasmExports["ye"])(a0, a1); var _AVStream_time_base_num = Module["_AVStream_time_base_num"] = a0 => (_AVStream_time_base_num = Module["_AVStream_time_base_num"] = wasmExports["ze"])(a0); var _AVStream_time_base_den = Module["_AVStream_time_base_den"] = a0 => (_AVStream_time_base_den = Module["_AVStream_time_base_den"] = wasmExports["Ae"])(a0); var _AVStream_time_base_num_s = Module["_AVStream_time_base_num_s"] = (a0, a1) => (_AVStream_time_base_num_s = Module["_AVStream_time_base_num_s"] = wasmExports["Be"])(a0, a1); var _AVStream_time_base_den_s = Module["_AVStream_time_base_den_s"] = (a0, a1) => (_AVStream_time_base_den_s = Module["_AVStream_time_base_den_s"] = wasmExports["Ce"])(a0, a1); var _AVStream_time_base_s = Module["_AVStream_time_base_s"] = (a0, a1, a2) => (_AVStream_time_base_s = Module["_AVStream_time_base_s"] = wasmExports["De"])(a0, a1, a2); var _avformat_seek_file_min = Module["_avformat_seek_file_min"] = (a0, a1, a2, a3, a4) => (_avformat_seek_file_min = Module["_avformat_seek_file_min"] = wasmExports["Ee"])(a0, a1, a2, a3, a4); var _avformat_seek_file = Module["_avformat_seek_file"] = (a0, a1, a2, a3, a4, a5, a6, a7, a8) => (_avformat_seek_file = Module["_avformat_seek_file"] = wasmExports["Fe"])(a0, a1, a2, a3, a4, a5, a6, a7, a8); var _avformat_seek_file_max = Module["_avformat_seek_file_max"] = (a0, a1, a2, a3, a4) => (_avformat_seek_file_max = Module["_avformat_seek_file_max"] = wasmExports["Ge"])(a0, a1, a2, a3, a4); var _avformat_seek_file_approx = Module["_avformat_seek_file_approx"] = (a0, a1, a2, a3, a4) => (_avformat_seek_file_approx = Module["_avformat_seek_file_approx"] = wasmExports["He"])(a0, a1, a2, a3, a4); var _AVFilterInOut_filter_ctx = Module["_AVFilterInOut_filter_ctx"] = a0 => (_AVFilterInOut_filter_ctx = Module["_AVFilterInOut_filter_ctx"] = wasmExports["Ie"])(a0); var _AVFilterInOut_filter_ctx_s = Module["_AVFilterInOut_filter_ctx_s"] = (a0, a1) => (_AVFilterInOut_filter_ctx_s = Module["_AVFilterInOut_filter_ctx_s"] = wasmExports["Je"])(a0, a1); var _AVFilterInOut_name = Module["_AVFilterInOut_name"] = a0 => (_AVFilterInOut_name = Module["_AVFilterInOut_name"] = wasmExports["Ke"])(a0); var _AVFilterInOut_name_s = Module["_AVFilterInOut_name_s"] = (a0, a1) => (_AVFilterInOut_name_s = Module["_AVFilterInOut_name_s"] = wasmExports["Le"])(a0, a1); var _AVFilterInOut_next = Module["_AVFilterInOut_next"] = a0 => (_AVFilterInOut_next = Module["_AVFilterInOut_next"] = wasmExports["Me"])(a0); var _AVFilterInOut_next_s = Module["_AVFilterInOut_next_s"] = (a0, a1) => (_AVFilterInOut_next_s = Module["_AVFilterInOut_next_s"] = wasmExports["Ne"])(a0, a1); var _AVFilterInOut_pad_idx = Module["_AVFilterInOut_pad_idx"] = a0 => (_AVFilterInOut_pad_idx = Module["_AVFilterInOut_pad_idx"] = wasmExports["Oe"])(a0); var _AVFilterInOut_pad_idx_s = Module["_AVFilterInOut_pad_idx_s"] = (a0, a1) => (_AVFilterInOut_pad_idx_s = Module["_AVFilterInOut_pad_idx_s"] = wasmExports["Pe"])(a0, a1); var _av_buffersink_get_time_base_num = Module["_av_buffersink_get_time_base_num"] = a0 => (_av_buffersink_get_time_base_num = Module["_av_buffersink_get_time_base_num"] = wasmExports["Qe"])(a0); var _av_buffersink_get_time_base_den = Module["_av_buffersink_get_time_base_den"] = a0 => (_av_buffersink_get_time_base_den = Module["_av_buffersink_get_time_base_den"] = wasmExports["Re"])(a0); var _ff_buffersink_set_ch_layout = Module["_ff_buffersink_set_ch_layout"] = (a0, a1, a2) => (_ff_buffersink_set_ch_layout = Module["_ff_buffersink_set_ch_layout"] = wasmExports["Se"])(a0, a1, a2); var _av_opt_set = Module["_av_opt_set"] = (a0, a1, a2, a3) => (_av_opt_set = Module["_av_opt_set"] = wasmExports["Te"])(a0, a1, a2, a3); var _libavjs_with_swscale = Module["_libavjs_with_swscale"] = () => (_libavjs_with_swscale = Module["_libavjs_with_swscale"] = wasmExports["Ue"])(); var _ffmpeg_main = Module["_ffmpeg_main"] = () => (_ffmpeg_main = Module["_ffmpeg_main"] = wasmExports["Ve"])(); var _ffprobe_main = Module["_ffprobe_main"] = () => (_ffprobe_main = Module["_ffprobe_main"] = wasmExports["We"])(); var _libavjs_create_main_thread = Module["_libavjs_create_main_thread"] = () => (_libavjs_create_main_thread = Module["_libavjs_create_main_thread"] = wasmExports["Xe"])(); var _avformat_alloc_output_context2_js = Module["_avformat_alloc_output_context2_js"] = (a0, a1, a2) => (_avformat_alloc_output_context2_js = Module["_avformat_alloc_output_context2_js"] = wasmExports["Ye"])(a0, a1, a2); var _avformat_open_input_js = Module["_avformat_open_input_js"] = (a0, a1, a2) => (_avformat_open_input_js = Module["_avformat_open_input_js"] = wasmExports["Ze"])(a0, a1, a2); var _avformat_open_input = Module["_avformat_open_input"] = (a0, a1, a2, a3) => (_avformat_open_input = Module["_avformat_open_input"] = wasmExports["_e"])(a0, a1, a2, a3); var _avio_open2_js = Module["_avio_open2_js"] = (a0, a1, a2, a3) => (_avio_open2_js = Module["_avio_open2_js"] = wasmExports["$e"])(a0, a1, a2, a3); var _avfilter_graph_create_filter_js = Module["_avfilter_graph_create_filter_js"] = (a0, a1, a2, a3, a4) => (_avfilter_graph_create_filter_js = Module["_avfilter_graph_create_filter_js"] = wasmExports["af"])(a0, a1, a2, a3, a4); var _av_dict_copy_js = Module["_av_dict_copy_js"] = (a0, a1, a2) => (_av_dict_copy_js = Module["_av_dict_copy_js"] = wasmExports["bf"])(a0, a1, a2); var _av_dict_set_js = Module["_av_dict_set_js"] = (a0, a1, a2, a3) => (_av_dict_set_js = Module["_av_dict_set_js"] = wasmExports["cf"])(a0, a1, a2, a3); var _av_compare_ts_js = Module["_av_compare_ts_js"] = (a0, a1, a2, a3, a4, a5, a6, a7) => (_av_compare_ts_js = Module["_av_compare_ts_js"] = wasmExports["df"])(a0, a1, a2, a3, a4, a5, a6, a7); var _ff_error = Module["_ff_error"] = a0 => (_ff_error = Module["_ff_error"] = wasmExports["ef"])(a0); var _mallinfo_uordblks = Module["_mallinfo_uordblks"] = () => (_mallinfo_uordblks = Module["_mallinfo_uordblks"] = wasmExports["ff"])(); var _av_strdup = Module["_av_strdup"] = a0 => (_av_strdup = Module["_av_strdup"] = wasmExports["gf"])(a0); var _av_dict_free = Module["_av_dict_free"] = a0 => (_av_dict_free = Module["_av_dict_free"] = wasmExports["jf"])(a0); var _av_frame_alloc = Module["_av_frame_alloc"] = () => (_av_frame_alloc = Module["_av_frame_alloc"] = wasmExports["kf"])(); var _av_frame_free = Module["_av_frame_free"] = a0 => (_av_frame_free = Module["_av_frame_free"] = wasmExports["lf"])(a0); var _av_frame_unref = Module["_av_frame_unref"] = a0 => (_av_frame_unref = Module["_av_frame_unref"] = wasmExports["mf"])(a0); var _av_frame_get_buffer = Module["_av_frame_get_buffer"] = (a0, a1) => (_av_frame_get_buffer = Module["_av_frame_get_buffer"] = wasmExports["nf"])(a0, a1); var _av_frame_ref = Module["_av_frame_ref"] = (a0, a1) => (_av_frame_ref = Module["_av_frame_ref"] = wasmExports["of"])(a0, a1); var _av_frame_clone = Module["_av_frame_clone"] = a0 => (_av_frame_clone = Module["_av_frame_clone"] = wasmExports["pf"])(a0); var _av_frame_make_writable = Module["_av_frame_make_writable"] = a0 => (_av_frame_make_writable = Module["_av_frame_make_writable"] = wasmExports["qf"])(a0); var _av_log_get_level = Module["_av_log_get_level"] = () => (_av_log_get_level = Module["_av_log_get_level"] = wasmExports["rf"])(); var _av_log_set_level = Module["_av_log_set_level"] = a0 => (_av_log_set_level = Module["_av_log_set_level"] = wasmExports["sf"])(a0); var _free = Module["_free"] = a0 => (_free = Module["_free"] = wasmExports["tf"])(a0); var _av_get_sample_fmt_name = Module["_av_get_sample_fmt_name"] = a0 => (_av_get_sample_fmt_name = Module["_av_get_sample_fmt_name"] = wasmExports["uf"])(a0); var _av_pix_fmt_desc_get = Module["_av_pix_fmt_desc_get"] = a0 => (_av_pix_fmt_desc_get = Module["_av_pix_fmt_desc_get"] = wasmExports["vf"])(a0); var _open = Module["_open"] = (a0, a1, a2) => (_open = Module["_open"] = wasmExports["wf"])(a0, a1, a2); var _av_get_bytes_per_sample = Module["_av_get_bytes_per_sample"] = a0 => (_av_get_bytes_per_sample = Module["_av_get_bytes_per_sample"] = wasmExports["xf"])(a0); var _avformat_free_context = Module["_avformat_free_context"] = a0 => (_avformat_free_context = Module["_avformat_free_context"] = wasmExports["yf"])(a0); var _av_find_best_stream = Module["_av_find_best_stream"] = (a0, a1, a2, a3, a4, a5) => (_av_find_best_stream = Module["_av_find_best_stream"] = wasmExports["zf"])(a0, a1, a2, a3, a4, a5); var _avio_close = Module["_avio_close"] = a0 => (_avio_close = Module["_avio_close"] = wasmExports["Af"])(a0); var _avio_flush = Module["_avio_flush"] = a0 => (_avio_flush = Module["_avio_flush"] = wasmExports["Bf"])(a0); var _avformat_alloc_context = Module["_avformat_alloc_context"] = () => (_avformat_alloc_context = Module["_avformat_alloc_context"] = wasmExports["Cf"])(); var _avcodec_parameters_to_context = Module["_avcodec_parameters_to_context"] = (a0, a1) => (_avcodec_parameters_to_context = Module["_avcodec_parameters_to_context"] = wasmExports["Df"])(a0, a1); var _avcodec_descriptor_get = Module["_avcodec_descriptor_get"] = a0 => (_avcodec_descriptor_get = Module["_avcodec_descriptor_get"] = wasmExports["Ef"])(a0); var _av_packet_unref = Module["_av_packet_unref"] = a0 => (_av_packet_unref = Module["_av_packet_unref"] = wasmExports["Ff"])(a0); var _avcodec_free_context = Module["_avcodec_free_context"] = a0 => (_avcodec_free_context = Module["_avcodec_free_context"] = wasmExports["Gf"])(a0); var _avcodec_parameters_free = Module["_avcodec_parameters_free"] = a0 => (_avcodec_parameters_free = Module["_avcodec_parameters_free"] = wasmExports["Hf"])(a0); var _av_packet_free = Module["_av_packet_free"] = a0 => (_av_packet_free = Module["_av_packet_free"] = wasmExports["If"])(a0); var _avformat_new_stream = Module["_avformat_new_stream"] = (a0, a1) => (_avformat_new_stream = Module["_avformat_new_stream"] = wasmExports["Jf"])(a0, a1); var _avcodec_parameters_copy = Module["_avcodec_parameters_copy"] = (a0, a1) => (_avcodec_parameters_copy = Module["_avcodec_parameters_copy"] = wasmExports["Kf"])(a0, a1); var _av_packet_ref = Module["_av_packet_ref"] = (a0, a1) => (_av_packet_ref = Module["_av_packet_ref"] = wasmExports["Lf"])(a0, a1); var _avcodec_find_decoder = Module["_avcodec_find_decoder"] = a0 => (_avcodec_find_decoder = Module["_avcodec_find_decoder"] = wasmExports["Mf"])(a0); var _avformat_close_input = Module["_avformat_close_input"] = a0 => (_avformat_close_input = Module["_avformat_close_input"] = wasmExports["Nf"])(a0); var _av_read_frame = Module["_av_read_frame"] = (a0, a1) => (_av_read_frame = Module["_av_read_frame"] = wasmExports["Of"])(a0, a1); var _avcodec_get_name = Module["_avcodec_get_name"] = a0 => (_avcodec_get_name = Module["_avcodec_get_name"] = wasmExports["Pf"])(a0); var _av_packet_new_side_data = Module["_av_packet_new_side_data"] = (a0, a1, a2) => (_av_packet_new_side_data = Module["_av_packet_new_side_data"] = wasmExports["Qf"])(a0, a1, a2); var _avformat_find_stream_info = Module["_avformat_find_stream_info"] = (a0, a1) => (_avformat_find_stream_info = Module["_avformat_find_stream_info"] = wasmExports["Rf"])(a0, a1); var _avcodec_parameters_from_context = Module["_avcodec_parameters_from_context"] = (a0, a1) => (_avcodec_parameters_from_context = Module["_avcodec_parameters_from_context"] = wasmExports["Sf"])(a0, a1); var _avcodec_find_decoder_by_name = Module["_avcodec_find_decoder_by_name"] = a0 => (_avcodec_find_decoder_by_name = Module["_avcodec_find_decoder_by_name"] = wasmExports["Tf"])(a0); var _avcodec_send_packet = Module["_avcodec_send_packet"] = (a0, a1) => (_avcodec_send_packet = Module["_avcodec_send_packet"] = wasmExports["Uf"])(a0, a1); var _avcodec_receive_frame = Module["_avcodec_receive_frame"] = (a0, a1) => (_avcodec_receive_frame = Module["_avcodec_receive_frame"] = wasmExports["Vf"])(a0, a1); var _avcodec_alloc_context3 = Module["_avcodec_alloc_context3"] = a0 => (_avcodec_alloc_context3 = Module["_avcodec_alloc_context3"] = wasmExports["Wf"])(a0); var _avcodec_parameters_alloc = Module["_avcodec_parameters_alloc"] = () => (_avcodec_parameters_alloc = Module["_avcodec_parameters_alloc"] = wasmExports["Xf"])(); var _av_find_input_format = Module["_av_find_input_format"] = a0 => (_av_find_input_format = Module["_av_find_input_format"] = wasmExports["Yf"])(a0); var _avformat_write_header = Module["_avformat_write_header"] = (a0, a1) => (_avformat_write_header = Module["_avformat_write_header"] = wasmExports["Zf"])(a0, a1); var _av_write_frame = Module["_av_write_frame"] = (a0, a1) => (_av_write_frame = Module["_av_write_frame"] = wasmExports["_f"])(a0, a1); var _av_interleaved_write_frame = Module["_av_interleaved_write_frame"] = (a0, a1) => (_av_interleaved_write_frame = Module["_av_interleaved_write_frame"] = wasmExports["$f"])(a0, a1); var _av_write_trailer = Module["_av_write_trailer"] = a0 => (_av_write_trailer = Module["_av_write_trailer"] = wasmExports["ag"])(a0); var _av_packet_alloc = Module["_av_packet_alloc"] = () => (_av_packet_alloc = Module["_av_packet_alloc"] = wasmExports["bg"])(); var _close = Module["_close"] = a0 => (_close = Module["_close"] = wasmExports["cg"])(a0); var _av_seek_frame = Module["_av_seek_frame"] = (a0, a1, a2, a3, a4) => (_av_seek_frame = Module["_av_seek_frame"] = wasmExports["dg"])(a0, a1, a2, a3, a4); var _avformat_flush = Module["_avformat_flush"] = a0 => (_avformat_flush = Module["_avformat_flush"] = wasmExports["eg"])(a0); var _av_grow_packet = Module["_av_grow_packet"] = (a0, a1) => (_av_grow_packet = Module["_av_grow_packet"] = wasmExports["fg"])(a0, a1); var _av_shrink_packet = Module["_av_shrink_packet"] = (a0, a1) => (_av_shrink_packet = Module["_av_shrink_packet"] = wasmExports["gg"])(a0, a1); var _avcodec_find_encoder = Module["_avcodec_find_encoder"] = a0 => (_avcodec_find_encoder = Module["_avcodec_find_encoder"] = wasmExports["hg"])(a0); var _avcodec_find_encoder_by_name = Module["_avcodec_find_encoder_by_name"] = a0 => (_avcodec_find_encoder_by_name = Module["_avcodec_find_encoder_by_name"] = wasmExports["ig"])(a0); var _avcodec_close = Module["_avcodec_close"] = a0 => (_avcodec_close = Module["_avcodec_close"] = wasmExports["jg"])(a0); var _avcodec_descriptor_next = Module["_avcodec_descriptor_next"] = a0 => (_avcodec_descriptor_next = Module["_avcodec_descriptor_next"] = wasmExports["kg"])(a0); var _avcodec_descriptor_get_by_name = Module["_avcodec_descriptor_get_by_name"] = a0 => (_avcodec_descriptor_get_by_name = Module["_avcodec_descriptor_get_by_name"] = wasmExports["lg"])(a0); var _avcodec_send_frame = Module["_avcodec_send_frame"] = (a0, a1) => (_avcodec_send_frame = Module["_avcodec_send_frame"] = wasmExports["mg"])(a0, a1); var _avcodec_receive_packet = Module["_avcodec_receive_packet"] = (a0, a1) => (_avcodec_receive_packet = Module["_avcodec_receive_packet"] = wasmExports["ng"])(a0, a1); var _av_packet_clone = Module["_av_packet_clone"] = a0 => (_av_packet_clone = Module["_av_packet_clone"] = wasmExports["og"])(a0); var _av_packet_make_writable = Module["_av_packet_make_writable"] = a0 => (_av_packet_make_writable = Module["_av_packet_make_writable"] = wasmExports["pg"])(a0); var _avfilter_get_by_name = Module["_avfilter_get_by_name"] = a0 => (_avfilter_get_by_name = Module["_avfilter_get_by_name"] = wasmExports["qg"])(a0); var _avfilter_link = Module["_avfilter_link"] = (a0, a1, a2, a3) => (_avfilter_link = Module["_avfilter_link"] = wasmExports["rg"])(a0, a1, a2, a3); var _avfilter_free = Module["_avfilter_free"] = a0 => (_avfilter_free = Module["_avfilter_free"] = wasmExports["sg"])(a0); var _avfilter_graph_alloc = Module["_avfilter_graph_alloc"] = () => (_avfilter_graph_alloc = Module["_avfilter_graph_alloc"] = wasmExports["tg"])(); var _avfilter_graph_free = Module["_avfilter_graph_free"] = a0 => (_avfilter_graph_free = Module["_avfilter_graph_free"] = wasmExports["ug"])(a0); var _avfilter_graph_config = Module["_avfilter_graph_config"] = (a0, a1) => (_avfilter_graph_config = Module["_avfilter_graph_config"] = wasmExports["vg"])(a0, a1); var _av_buffersink_get_frame = Module["_av_buffersink_get_frame"] = (a0, a1) => (_av_buffersink_get_frame = Module["_av_buffersink_get_frame"] = wasmExports["wg"])(a0, a1); var _av_buffersink_set_frame_size = Module["_av_buffersink_set_frame_size"] = (a0, a1) => (_av_buffersink_set_frame_size = Module["_av_buffersink_set_frame_size"] = wasmExports["xg"])(a0, a1); var _av_buffersrc_add_frame_flags = Module["_av_buffersrc_add_frame_flags"] = (a0, a1, a2) => (_av_buffersrc_add_frame_flags = Module["_av_buffersrc_add_frame_flags"] = wasmExports["yg"])(a0, a1, a2); var _avfilter_inout_alloc = Module["_avfilter_inout_alloc"] = () => (_avfilter_inout_alloc = Module["_avfilter_inout_alloc"] = wasmExports["zg"])(); var _avfilter_inout_free = Module["_avfilter_inout_free"] = a0 => (_avfilter_inout_free = Module["_avfilter_inout_free"] = wasmExports["Ag"])(a0); var _avfilter_graph_parse = Module["_avfilter_graph_parse"] = (a0, a1, a2, a3, a4) => (_avfilter_graph_parse = Module["_avfilter_graph_parse"] = wasmExports["Bg"])(a0, a1, a2, a3, a4); var _sws_freeContext = Module["_sws_freeContext"] = a0 => (_sws_freeContext = Module["_sws_freeContext"] = wasmExports["Cg"])(a0); var _sws_scale_frame = Module["_sws_scale_frame"] = (a0, a1, a2) => (_sws_scale_frame = Module["_sws_scale_frame"] = wasmExports["Dg"])(a0, a1, a2); var _sws_getContext = Module["_sws_getContext"] = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) => (_sws_getContext = Module["_sws_getContext"] = wasmExports["Eg"])(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9); var _emfiberthreads_timeout_expiry = Module["_emfiberthreads_timeout_expiry"] = (a0, a1) => (_emfiberthreads_timeout_expiry = Module["_emfiberthreads_timeout_expiry"] = wasmExports["Fg"])(a0, a1); var _malloc = Module["_malloc"] = a0 => (_malloc = Module["_malloc"] = wasmExports["Gg"])(a0); var _dup2 = Module["_dup2"] = (a0, a1) => (_dup2 = Module["_dup2"] = wasmExports["Hg"])(a0, a1); var _calloc = Module["_calloc"] = (a0, a1) => (_calloc = Module["_calloc"] = wasmExports["Ig"])(a0, a1); var _strerror = Module["_strerror"] = a0 => (_strerror = Module["_strerror"] = wasmExports["Jg"])(a0); var __emscripten_tempret_set = a0 => (__emscripten_tempret_set = wasmExports["Kg"])(a0); var __emscripten_stack_restore = a0 => (__emscripten_stack_restore = wasmExports["Lg"])(a0); var __emscripten_stack_alloc = a0 => (__emscripten_stack_alloc = wasmExports["Mg"])(a0); var _emscripten_stack_get_current = () => (_emscripten_stack_get_current = wasmExports["Ng"])(); var _asyncify_start_unwind = a0 => (_asyncify_start_unwind = wasmExports["Og"])(a0); var _asyncify_stop_unwind = () => (_asyncify_stop_unwind = wasmExports["Pg"])(); var _asyncify_start_rewind = a0 => (_asyncify_start_rewind = wasmExports["Qg"])(a0); var _asyncify_stop_rewind = () => (_asyncify_stop_rewind = wasmExports["Rg"])(); var _ff_h264_cabac_tables = Module["_ff_h264_cabac_tables"] = 184636; Module["ccall"] = ccall; Module["cwrap"] = cwrap; var calledRun; var calledPrerun; dependenciesFulfilled = function runCaller() { if (!calledRun) run(); if (!calledRun) dependenciesFulfilled = runCaller }; function run() { if (runDependencies > 0) { return } if (!calledPrerun) { calledPrerun = 1; preRun(); if (runDependencies > 0) { return } } function doRun() { if (calledRun) return; calledRun = 1; Module["calledRun"] = 1; if (ABORT) return; initRuntime(); readyPromiseResolve(Module); Module["onRuntimeInitialized"]?.(); postRun() } if (Module["setStatus"]) { Module["setStatus"]("Running..."); setTimeout(() => { setTimeout(() => Module["setStatus"](""), 1); doRun() }, 1) } else { doRun() } } if (Module["preInit"]) { if (typeof Module["preInit"] == "function") Module["preInit"] = [Module["preInit"]]; while (Module["preInit"].length > 0) { Module["preInit"].pop()() } } run(); var serializationPromise = null; function serially(f) { var p; if (serializationPromise) { p = serializationPromise.catch(function () { }).then(function () { return f() }) } else { p = f() } serializationPromise = p = p.finally(function () { if (serializationPromise === p) serializationPromise = null }); return p } Module.fsThrownError = null; var ERRNO_CODES = { EPERM: 1, EIO: 5, EAGAIN: 6, ECANCELED: 11, ESPIPE: 29 }; var readerCallbacks = { open: function (stream) { if (stream.flags & 3) { throw new FS.ErrnoError(ERRNO_CODES.EPERM) } }, close: function () { }, read: function (stream, buffer, offset, length, position) { var data = Module.readBuffers[stream.node.name]; if (!data || data.buf.length === 0 && !data.eof) { if (Module.onread) { try { var rr = Module.onread(stream.node.name, position, length); if (rr && rr.then && rr.catch) { rr.catch(function (ex) { ff_reader_dev_send(stream.node.name, null, { error: ex }) }) } } catch (ex) { ff_reader_dev_send(stream.node.name, null, { error: ex }) } } data = Module.readBuffers[stream.node.name] } if (!data) throw new FS.ErrnoError(ERRNO_CODES.EAGAIN); if (data.error) { Module.fsThrownError = data.error; throw new FS.ErrnoError(ERRNO_CODES.ECANCELED) } if (data.errorCode) throw new FS.ErrnoError(data.errorCode); if (data.buf.length === 0) { if (data.eof) { return 0 } else { data.ready = false; throw new FS.ErrnoError(ERRNO_CODES.EAGAIN) } } var ret; if (length < data.buf.length) { ret = data.buf.subarray(0, length); data.buf = data.buf.slice(length) } else { ret = data.buf; data.buf = new Uint8Array(0) } new Uint8Array(buffer.buffer).set(ret, offset); return ret.length }, write: function () { throw new FS.ErrnoError(ERRNO_CODES.EIO) }, llseek: function () { throw new FS.ErrnoError(ERRNO_CODES.ESPIPE) } }; var blockReaderCallbacks = { open: function (stream) { if (stream.flags & 3) throw new FS.ErrnoError(ERRNO_CODES.EPERM) }, close: function () { }, read: function (stream, buffer, offset, length, position) { var data = Module.blockReadBuffers[stream.node.name]; if (!data) throw new FS.ErrnoError(ERRNO_CODES.EAGAIN); if (data.error) { Module.fsThrownError = data.error; throw new FS.ErrnoError(ERRNO_CODES.ECANCELED) } if (data.errorCode) throw new FS.ErrnoError(data.errorCode); var bufMin = data.position; var bufMax = data.position + data.buf.length; if (position < bufMin || position >= bufMax) { if (position >= stream.node.ff_block_reader_dev_size) return 0; if (!Module.onblockread) throw new FS.ErrnoError(ERRNO_CODES.EIO); try { var brr = Module.onblockread(stream.node.name, position, length); if (brr && brr.then && brr.catch) { brr.catch(function (ex) { ff_block_reader_dev_send(stream.node.name, position, null, { error: ex }) }) } } catch (ex) { Module.fsThrownError = ex; throw new FS.ErrnoError(ERRNO_CODES.ECANCELED) } bufMin = data.position; bufMax = data.position + data.buf.length; if (position < bufMin || position >= bufMax) { data.ready = false; throw new FS.ErrnoError(ERRNO_CODES.EAGAIN) } } var bufPos = position - bufMin; var ret; if (bufPos + length < data.buf.length) { ret = data.buf.subarray(bufPos, bufPos + length) } else { ret = data.buf.subarray(bufPos, data.buf.length) } new Uint8Array(buffer.buffer).set(ret, offset); return ret.length }, write: function () { throw new FS.ErrnoError(ERRNO_CODES.EIO) }, llseek: function (stream, offset, whence) { if (whence === 2) offset = stream.node.size + offset; else if (whence === 1) offset += stream.position; return offset } }; var writerCallbacks = { open: function (stream) { if (!(stream.flags & 1)) { throw new FS.ErrnoError(ERRNO_CODES.EPERM) } }, close: function () { }, read: function () { throw new FS.ErrnoError(ERRNO_CODES.EIO) }, write: function (stream, buffer, offset, length, position) { if (!Module.onwrite) throw new FS.ErrnoError(ERRNO_CODES.EIO); Module.onwrite(stream.node.name, position, buffer.subarray(offset, offset + length)); return length }, llseek: function (stream, offset, whence) { if (whence === 2) throw new FS.ErrnoError(ERRNO_CODES.EIO); else if (whence === 1) offset += stream.position; return offset } }; var streamWriterCallbacks = Object.create(writerCallbacks); streamWriterCallbacks.write = function (stream, buffer, offset, length, position) { if (position != stream.position) throw new FS.ErrnoError(ERRNO_CODES.ESPIPE); return writerCallbacks.write(stream, buffer, offset, length, position) }; streamWriterCallbacks.llseek = function () { throw new FS.ErrnoError(ERRNO_CODES.ESPIPE) }; var streamWriterFS = Object.create(MEMFS); streamWriterFS.mount = function (mount) { return streamWriterFS.createNode(null, "/", 16384 | 511, 0) }; streamWriterFS.createNode = function () { var node = MEMFS.createNode.apply(MEMFS, arguments); if (FS.isDir(node.mode)) { if (!streamWriterFS.dir_node_ops) { streamWriterFS.dir_node_ops = Object.create(node.node_ops); streamWriterFS.dir_node_ops.mknod = function (parent, name, mode, dev) { return streamWriterFS.createNode(parent, name, mode, dev) } } node.node_ops = streamWriterFS.dir_node_ops } else if (FS.isFile(node.mode)) { node.stream_ops = writerCallbacks } return node }; var CAccessors = {}; var av_get_bytes_per_sample = Module.av_get_bytes_per_sample = CAccessors.av_get_bytes_per_sample = Module.cwrap("av_get_bytes_per_sample", "number", ["number"]); var av_compare_ts_js = Module.av_compare_ts_js = CAccessors.av_compare_ts_js = Module.cwrap("av_compare_ts_js", "number", ["number", "number", "number", "number", "number", "number", "number", "number"]); var av_opt_set = Module.av_opt_set = CAccessors.av_opt_set = Module.cwrap("av_opt_set", "number", ["number", "string", "string", "number"]); var av_opt_set_int_list_js = Module.av_opt_set_int_list_js = CAccessors.av_opt_set_int_list_js = Module.cwrap("av_opt_set_int_list_js", "number", ["number", "string", "number", "number", "number", "number"]); var av_frame_alloc = Module.av_frame_alloc = CAccessors.av_frame_alloc = Module.cwrap("av_frame_alloc", "number", []); var av_frame_clone = Module.av_frame_clone = CAccessors.av_frame_clone = Module.cwrap("av_frame_clone", "number", ["number", "number"]); var av_frame_free = Module.av_frame_free = CAccessors.av_frame_free = Module.cwrap("av_frame_free", null, ["number"]); var av_frame_get_buffer = Module.av_frame_get_buffer = CAccessors.av_frame_get_buffer = Module.cwrap("av_frame_get_buffer", "number", ["number", "number"]); var av_frame_make_writable = Module.av_frame_make_writable = CAccessors.av_frame_make_writable = Module.cwrap("av_frame_make_writable", "number", ["number"]); var av_frame_ref = Module.av_frame_ref = CAccessors.av_frame_ref = Module.cwrap("av_frame_ref", "number", ["number", "number"]); var av_frame_unref = Module.av_frame_unref = CAccessors.av_frame_unref = Module.cwrap("av_frame_unref", null, ["number"]); var ff_frame_rescale_ts_js = Module.ff_frame_rescale_ts_js = CAccessors.ff_frame_rescale_ts_js = Module.cwrap("ff_frame_rescale_ts_js", null, ["number", "number", "number", "number", "number"]); var av_log_get_level = Module.av_log_get_level = CAccessors.av_log_get_level = Module.cwrap("av_log_get_level", "number", []); var av_log_set_level = Module.av_log_set_level = CAccessors.av_log_set_level = Module.cwrap("av_log_set_level", null, ["number"]); var av_packet_alloc = Module.av_packet_alloc = CAccessors.av_packet_alloc = Module.cwrap("av_packet_alloc", "number", []); var av_packet_clone = Module.av_packet_clone = CAccessors.av_packet_clone = Module.cwrap("av_packet_clone", "number", ["number"]); var av_packet_free = Module.av_packet_free = CAccessors.av_packet_free = Module.cwrap("av_packet_free", null, ["number"]); var av_packet_new_side_data = Module.av_packet_new_side_data = CAccessors.av_packet_new_side_data = Module.cwrap("av_packet_new_side_data", "number", ["number", "number", "number"]); var av_packet_ref = Module.av_packet_ref = CAccessors.av_packet_ref = Module.cwrap("av_packet_ref", "number", ["number", "number"]); var av_packet_rescale_ts_js = Module.av_packet_rescale_ts_js = CAccessors.av_packet_rescale_ts_js = Module.cwrap("av_packet_rescale_ts_js", null, ["number", "number", "number", "number", "number"]); var av_packet_unref = Module.av_packet_unref = CAccessors.av_packet_unref = Module.cwrap("av_packet_unref", null, ["number"]); var av_strdup = Module.av_strdup = CAccessors.av_strdup = Module.cwrap("av_strdup", "number", ["string"]); var av_buffersink_get_frame = Module.av_buffersink_get_frame = CAccessors.av_buffersink_get_frame = Module.cwrap("av_buffersink_get_frame", "number", ["number", "number"]); var av_buffersink_get_time_base_num = Module.av_buffersink_get_time_base_num = CAccessors.av_buffersink_get_time_base_num = Module.cwrap("av_buffersink_get_time_base_num", "number", ["number"]); var av_buffersink_get_time_base_den = Module.av_buffersink_get_time_base_den = CAccessors.av_buffersink_get_time_base_den = Module.cwrap("av_buffersink_get_time_base_den", "number", ["number"]); var av_buffersink_set_frame_size = Module.av_buffersink_set_frame_size = CAccessors.av_buffersink_set_frame_size = Module.cwrap("av_buffersink_set_frame_size", null, ["number", "number"]); var ff_buffersink_set_ch_layout = Module.ff_buffersink_set_ch_layout = CAccessors.ff_buffersink_set_ch_layout = Module.cwrap("ff_buffersink_set_ch_layout", "number", ["number", "number", "number"]); var av_buffersrc_add_frame_flags = Module.av_buffersrc_add_frame_flags = CAccessors.av_buffersrc_add_frame_flags = Module.cwrap("av_buffersrc_add_frame_flags", "number", ["number", "number", "number"]); var avfilter_free = Module.avfilter_free = CAccessors.avfilter_free = Module.cwrap("avfilter_free", null, ["number"]); var avfilter_get_by_name = Module.avfilter_get_by_name = CAccessors.avfilter_get_by_name = Module.cwrap("avfilter_get_by_name", "number", ["string"]); var avfilter_graph_alloc = Module.avfilter_graph_alloc = CAccessors.avfilter_graph_alloc = Module.cwrap("avfilter_graph_alloc", "number", []); var avfilter_graph_config = Module.avfilter_graph_config = CAccessors.avfilter_graph_config = Module.cwrap("avfilter_graph_config", "number", ["number", "number"]); var avfilter_graph_create_filter_js = Module.avfilter_graph_create_filter_js = CAccessors.avfilter_graph_create_filter_js = Module.cwrap("avfilter_graph_create_filter_js", "number", ["number", "string", "string", "number", "number"]); var avfilter_graph_free = Module.avfilter_graph_free = CAccessors.avfilter_graph_free = Module.cwrap("avfilter_graph_free", null, ["number"]); var avfilter_graph_parse = Module.avfilter_graph_parse = CAccessors.avfilter_graph_parse = Module.cwrap("avfilter_graph_parse", "number", ["number", "string", "number", "number", "number"]); var avfilter_inout_alloc = Module.avfilter_inout_alloc = CAccessors.avfilter_inout_alloc = Module.cwrap("avfilter_inout_alloc", "number", []); var avfilter_inout_free = Module.avfilter_inout_free = CAccessors.avfilter_inout_free = Module.cwrap("avfilter_inout_free", null, ["number"]); var avfilter_link = Module.avfilter_link = CAccessors.avfilter_link = Module.cwrap("avfilter_link", "number", ["number", "number", "number", "number"]); var avcodec_alloc_context3 = Module.avcodec_alloc_context3 = CAccessors.avcodec_alloc_context3 = Module.cwrap("avcodec_alloc_context3", "number", ["number"]); var avcodec_close = Module.avcodec_close = CAccessors.avcodec_close = Module.cwrap("avcodec_close", "number", ["number"]); var avcodec_descriptor_get = Module.avcodec_descriptor_get = CAccessors.avcodec_descriptor_get = Module.cwrap("avcodec_descriptor_get", "number", ["number"]); var avcodec_descriptor_get_by_name = Module.avcodec_descriptor_get_by_name = CAccessors.avcodec_descriptor_get_by_name = Module.cwrap("avcodec_descriptor_get_by_name", "number", ["string"]); var avcodec_descriptor_next = Module.avcodec_descriptor_next = CAccessors.avcodec_descriptor_next = Module.cwrap("avcodec_descriptor_next", "number", ["number"]); var avcodec_find_decoder = Module.avcodec_find_decoder = CAccessors.avcodec_find_decoder = Module.cwrap("avcodec_find_decoder", "number", ["number"]); var avcodec_find_decoder_by_name = Module.avcodec_find_decoder_by_name = CAccessors.avcodec_find_decoder_by_name = Module.cwrap("avcodec_find_decoder_by_name", "number", ["string"]); var avcodec_find_encoder = Module.avcodec_find_encoder = CAccessors.avcodec_find_encoder = Module.cwrap("avcodec_find_encoder", "number", ["number"]); var avcodec_find_encoder_by_name = Module.avcodec_find_encoder_by_name = CAccessors.avcodec_find_encoder_by_name = Module.cwrap("avcodec_find_encoder_by_name", "number", ["string"]); var avcodec_free_context = Module.avcodec_free_context = CAccessors.avcodec_free_context = Module.cwrap("avcodec_free_context", null, ["number"]); var avcodec_get_name = Module.avcodec_get_name = CAccessors.avcodec_get_name = Module.cwrap("avcodec_get_name", "string", ["number"]); var avcodec_open2 = Module.avcodec_open2 = CAccessors.avcodec_open2 = Module.cwrap("avcodec_open2", "number", ["number", "number", "number"]); var avcodec_open2_js = Module.avcodec_open2_js = CAccessors.avcodec_open2_js = Module.cwrap("avcodec_open2_js", "number", ["number", "number", "number"]); var avcodec_parameters_alloc = Module.avcodec_parameters_alloc = CAccessors.avcodec_parameters_alloc = Module.cwrap("avcodec_parameters_alloc", "number", []); var avcodec_parameters_copy = Module.avcodec_parameters_copy = CAccessors.avcodec_parameters_copy = Module.cwrap("avcodec_parameters_copy", "number", ["number", "number"]); var avcodec_parameters_free = Module.avcodec_parameters_free = CAccessors.avcodec_parameters_free = Module.cwrap("avcodec_parameters_free", null, ["number"]); var avcodec_parameters_from_context = Module.avcodec_parameters_from_context = CAccessors.avcodec_parameters_from_context = Module.cwrap("avcodec_parameters_from_context", "number", ["number", "number"]); var avcodec_parameters_to_context = Module.avcodec_parameters_to_context = CAccessors.avcodec_parameters_to_context = Module.cwrap("avcodec_parameters_to_context", "number", ["number", "number"]); var avcodec_receive_frame = Module.avcodec_receive_frame = CAccessors.avcodec_receive_frame = Module.cwrap("avcodec_receive_frame", "number", ["number", "number"]); var avcodec_receive_packet = Module.avcodec_receive_packet = CAccessors.avcodec_receive_packet = Module.cwrap("avcodec_receive_packet", "number", ["number", "number"]); var avcodec_send_frame = Module.avcodec_send_frame = CAccessors.avcodec_send_frame = Module.cwrap("avcodec_send_frame", "number", ["number", "number"]); var avcodec_send_packet = Module.avcodec_send_packet = CAccessors.avcodec_send_packet = Module.cwrap("avcodec_send_packet", "number", ["number", "number"]); var av_find_input_format = Module.av_find_input_format = CAccessors.av_find_input_format = Module.cwrap("av_find_input_format", "number", ["string"]); var avformat_alloc_context = Module.avformat_alloc_context = CAccessors.avformat_alloc_context = Module.cwrap("avformat_alloc_context", "number", []); var avformat_alloc_output_context2_js = Module.avformat_alloc_output_context2_js = CAccessors.avformat_alloc_output_context2_js = Module.cwrap("avformat_alloc_output_context2_js", "number", ["number", "string", "string"]); var avformat_close_input = Module.avformat_close_input = CAccessors.avformat_close_input = Module.cwrap("avformat_close_input", null, ["number"]); var avformat_find_stream_info = Module.avformat_find_stream_info = CAccessors.avformat_find_stream_info = Module.cwrap("avformat_find_stream_info", "number", ["number", "number"], { async: true }); var avformat_find_stream_info__raw = avformat_find_stream_info; avformat_find_stream_info = Module.avformat_find_stream_info = function () { var args = arguments; var ret = avformat_find_stream_info__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_find_stream_info = function () { var args = arguments; return serially(function () { return avformat_find_stream_info.apply(void 0, args) }) }; var avformat_flush = Module.avformat_flush = CAccessors.avformat_flush = Module.cwrap("avformat_flush", "number", ["number"]); var avformat_free_context = Module.avformat_free_context = CAccessors.avformat_free_context = Module.cwrap("avformat_free_context", null, ["number"]); var avformat_new_stream = Module.avformat_new_stream = CAccessors.avformat_new_stream = Module.cwrap("avformat_new_stream", "number", ["number", "number"]); var avformat_open_input = Module.avformat_open_input = CAccessors.avformat_open_input = Module.cwrap("avformat_open_input", "number", ["number", "string", "number", "number"], { async: true }); var avformat_open_input__raw = avformat_open_input; avformat_open_input = Module.avformat_open_input = function () { var args = arguments; var ret = avformat_open_input__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_open_input = function () { var args = arguments; return serially(function () { return avformat_open_input.apply(void 0, args) }) }; var avformat_open_input_js = Module.avformat_open_input_js = CAccessors.avformat_open_input_js = Module.cwrap("avformat_open_input_js", "number", ["string", "number", "number"], { async: true }); var avformat_open_input_js__raw = avformat_open_input_js; avformat_open_input_js = Module.avformat_open_input_js = function () { var args = arguments; var ret = avformat_open_input_js__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_open_input_js = function () { var args = arguments; return serially(function () { return avformat_open_input_js.apply(void 0, args) }) }; var av_seek_frame = Module.av_seek_frame = CAccessors.av_seek_frame = Module.cwrap("av_seek_frame", "number", ["number", "number", "number", "number"], { async: true }); var av_seek_frame__raw = av_seek_frame; av_seek_frame = Module.av_seek_frame = function () { var args = arguments; var ret = av_seek_frame__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.av_seek_frame = function () { var args = arguments; return serially(function () { return av_seek_frame.apply(void 0, args) }) }; var avformat_seek_file = Module.avformat_seek_file = CAccessors.avformat_seek_file = Module.cwrap("avformat_seek_file", "number", ["number", "number", "number", "number", "number", "number"], { async: true }); var avformat_seek_file__raw = avformat_seek_file; avformat_seek_file = Module.avformat_seek_file = function () { var args = arguments; var ret = avformat_seek_file__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_seek_file = function () { var args = arguments; return serially(function () { return avformat_seek_file.apply(void 0, args) }) }; var avformat_seek_file_min = Module.avformat_seek_file_min = CAccessors.avformat_seek_file_min = Module.cwrap("avformat_seek_file_min", "number", ["number", "number", "number", "number"], { async: true }); var avformat_seek_file_min__raw = avformat_seek_file_min; avformat_seek_file_min = Module.avformat_seek_file_min = function () { var args = arguments; var ret = avformat_seek_file_min__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_seek_file_min = function () { var args = arguments; return serially(function () { return avformat_seek_file_min.apply(void 0, args) }) }; var avformat_seek_file_max = Module.avformat_seek_file_max = CAccessors.avformat_seek_file_max = Module.cwrap("avformat_seek_file_max", "number", ["number", "number", "number", "number"], { async: true }); var avformat_seek_file_max__raw = avformat_seek_file_max; avformat_seek_file_max = Module.avformat_seek_file_max = function () { var args = arguments; var ret = avformat_seek_file_max__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_seek_file_max = function () { var args = arguments; return serially(function () { return avformat_seek_file_max.apply(void 0, args) }) }; var avformat_seek_file_approx = Module.avformat_seek_file_approx = CAccessors.avformat_seek_file_approx = Module.cwrap("avformat_seek_file_approx", "number", ["number", "number", "number", "number"], { async: true }); var avformat_seek_file_approx__raw = avformat_seek_file_approx; avformat_seek_file_approx = Module.avformat_seek_file_approx = function () { var args = arguments; var ret = avformat_seek_file_approx__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.avformat_seek_file_approx = function () { var args = arguments; return serially(function () { return avformat_seek_file_approx.apply(void 0, args) }) }; var avformat_write_header = Module.avformat_write_header = CAccessors.avformat_write_header = Module.cwrap("avformat_write_header", "number", ["number", "number"]); var avio_open2_js = Module.avio_open2_js = CAccessors.avio_open2_js = Module.cwrap("avio_open2_js", "number", ["string", "number", "number", "number"]); var avio_close = Module.avio_close = CAccessors.avio_close = Module.cwrap("avio_close", "number", ["number"]); var avio_flush = Module.avio_flush = CAccessors.avio_flush = Module.cwrap("avio_flush", null, ["number"]); var av_find_best_stream = Module.av_find_best_stream = CAccessors.av_find_best_stream = Module.cwrap("av_find_best_stream", "number", ["number", "number", "number", "number", "number", "number"]); var av_get_sample_fmt_name = Module.av_get_sample_fmt_name = CAccessors.av_get_sample_fmt_name = Module.cwrap("av_get_sample_fmt_name", "string", ["number"]); var av_grow_packet = Module.av_grow_packet = CAccessors.av_grow_packet = Module.cwrap("av_grow_packet", "number", ["number", "number"]); var av_interleaved_write_frame = Module.av_interleaved_write_frame = CAccessors.av_interleaved_write_frame = Module.cwrap("av_interleaved_write_frame", "number", ["number", "number"]); var av_packet_make_writable = Module.av_packet_make_writable = CAccessors.av_packet_make_writable = Module.cwrap("av_packet_make_writable", "number", ["number"]); var av_pix_fmt_desc_get = Module.av_pix_fmt_desc_get = CAccessors.av_pix_fmt_desc_get = Module.cwrap("av_pix_fmt_desc_get", "number", ["number"]); var av_read_frame = Module.av_read_frame = CAccessors.av_read_frame = Module.cwrap("av_read_frame", "number", ["number", "number"], { async: true }); var av_read_frame__raw = av_read_frame; av_read_frame = Module.av_read_frame = function () { var args = arguments; var ret = av_read_frame__raw.apply(void 0, args); if (ret === -11) throw Module.fsThrownError; else if (ret && ret.then) { return ret.then(function (ret) { if (ret === -11) throw Module.fsThrownError; return ret }) } return ret }; Module.av_read_frame = function () { var args = arguments; return serially(function () { return av_read_frame.apply(void 0, args) }) }; var av_shrink_packet = Module.av_shrink_packet = CAccessors.av_shrink_packet = Module.cwrap("av_shrink_packet", null, ["number", "number"]); var av_write_frame = Module.av_write_frame = CAccessors.av_write_frame = Module.cwrap("av_write_frame", "number", ["number", "number"]); var av_write_trailer = Module.av_write_trailer = CAccessors.av_write_trailer = Module.cwrap("av_write_trailer", "number", ["number"]); var av_dict_copy_js = Module.av_dict_copy_js = CAccessors.av_dict_copy_js = Module.cwrap("av_dict_copy_js", "number", ["number", "number", "number"]); var av_dict_free = Module.av_dict_free = CAccessors.av_dict_free = Module.cwrap("av_dict_free", null, ["number"]); var av_dict_set_js = Module.av_dict_set_js = CAccessors.av_dict_set_js = Module.cwrap("av_dict_set_js", "number", ["number", "string", "string", "number"]); var sws_getContext = Module.sws_getContext = CAccessors.sws_getContext = Module.cwrap("sws_getContext", "number", ["number", "number", "number", "number", "number", "number", "number", "number", "number", "number"]); var sws_freeContext = Module.sws_freeContext = CAccessors.sws_freeContext = Module.cwrap("sws_freeContext", null, ["number"]); var sws_scale_frame = Module.sws_scale_frame = CAccessors.sws_scale_frame = Module.cwrap("sws_scale_frame", "number", ["number", "number", "number"]); var AVPacketSideData_data = Module.AVPacketSideData_data = CAccessors.AVPacketSideData_data = Module.cwrap("AVPacketSideData_data", "number", ["number", "number"]); var AVPacketSideData_size = Module.AVPacketSideData_size = CAccessors.AVPacketSideData_size = Module.cwrap("AVPacketSideData_size", "number", ["number", "number"]); var AVPacketSideData_type = Module.AVPacketSideData_type = CAccessors.AVPacketSideData_type = Module.cwrap("AVPacketSideData_type", "number", ["number", "number"]); var AVPixFmtDescriptor_comp_depth = Module.AVPixFmtDescriptor_comp_depth = CAccessors.AVPixFmtDescriptor_comp_depth = Module.cwrap("AVPixFmtDescriptor_comp_depth", "number", ["number", "number"]); var ff_error = Module.ff_error = CAccessors.ff_error = Module.cwrap("ff_error", "string", ["number"]); var ff_nothing = Module.ff_nothing = CAccessors.ff_nothing = Module.cwrap("ff_nothing", null, [], { async: true }); Module.ff_nothing = function () { var args = arguments; return serially(function () { return ff_nothing.apply(void 0, args) }) }; var calloc = Module.calloc = CAccessors.calloc = Module.cwrap("calloc", "number", ["number", "number"]); var close = Module.close = CAccessors.close = Module.cwrap("close", "number", ["number"]); var dup2 = Module.dup2 = CAccessors.dup2 = Module.cwrap("dup2", "number", ["number", "number"]); var free = Module.free = CAccessors.free = Module.cwrap("free", null, ["number"]); var malloc = Module.malloc = CAccessors.malloc = Module.cwrap("malloc", "number", ["number"]); var mallinfo_uordblks = Module.mallinfo_uordblks = CAccessors.mallinfo_uordblks = Module.cwrap("mallinfo_uordblks", "number", []); var open = Module.open = CAccessors.open = Module.cwrap("open", "number", ["string", "number", "number"]); var strerror = Module.strerror = CAccessors.strerror = Module.cwrap("strerror", "string", ["number"]); var libavjs_with_swscale = Module.libavjs_with_swscale = CAccessors.libavjs_with_swscale = Module.cwrap("libavjs_with_swscale", "number", []); var libavjs_create_main_thread = Module.libavjs_create_main_thread = CAccessors.libavjs_create_main_thread = Module.cwrap("libavjs_create_main_thread", "number", []); var ffmpeg_main = Module.ffmpeg_main = CAccessors.ffmpeg_main = Module.cwrap("ffmpeg_main", "number", ["number", "number"], { async: true }); Module.ffmpeg_main = function () { var args = arguments; return serially(function () { return ffmpeg_main.apply(void 0, args) }) }; var ffprobe_main = Module.ffprobe_main = CAccessors.ffprobe_main = Module.cwrap("ffprobe_main", "number", ["number", "number"], { async: true }); Module.ffprobe_main = function () { var args = arguments; return serially(function () { return ffprobe_main.apply(void 0, args) }) }; var AVFrame_channel_layout = Module.AVFrame_channel_layout = CAccessors.AVFrame_channel_layout = Module.cwrap("AVFrame_channel_layout", "number", ["number"]); var AVFrame_channel_layout_s = Module.AVFrame_channel_layout_s = CAccessors.AVFrame_channel_layout_s = Module.cwrap("AVFrame_channel_layout_s", null, ["number", "number"]); var AVFrame_channel_layouthi = Module.AVFrame_channel_layouthi = CAccessors.AVFrame_channel_layouthi = Module.cwrap("AVFrame_channel_layouthi", "number", ["number"]); var AVFrame_channel_layouthi_s = Module.AVFrame_channel_layouthi_s = CAccessors.AVFrame_channel_layouthi_s = Module.cwrap("AVFrame_channel_layouthi_s", null, ["number", "number"]); var AVFrame_channels = Module.AVFrame_channels = CAccessors.AVFrame_channels = Module.cwrap("AVFrame_channels", "number", ["number"]); var AVFrame_channels_s = Module.AVFrame_channels_s = CAccessors.AVFrame_channels_s = Module.cwrap("AVFrame_channels_s", null, ["number", "number"]); var AVFrame_channel_layoutmask = Module.AVFrame_channel_layoutmask = CAccessors.AVFrame_channel_layoutmask = Module.cwrap("AVFrame_channel_layoutmask", "number", ["number"]); var AVFrame_channel_layoutmask_s = Module.AVFrame_channel_layoutmask_s = CAccessors.AVFrame_channel_layoutmask_s = Module.cwrap("AVFrame_channel_layoutmask_s", null, ["number", "number"]); var AVFrame_ch_layout_nb_channels = Module.AVFrame_ch_layout_nb_channels = CAccessors.AVFrame_ch_layout_nb_channels = Module.cwrap("AVFrame_ch_layout_nb_channels", "number", ["number"]); var AVFrame_ch_layout_nb_channels_s = Module.AVFrame_ch_layout_nb_channels_s = CAccessors.AVFrame_ch_layout_nb_channels_s = Module.cwrap("AVFrame_ch_layout_nb_channels_s", null, ["number", "number"]); var AVFrame_crop_bottom = Module.AVFrame_crop_bottom = CAccessors.AVFrame_crop_bottom = Module.cwrap("AVFrame_crop_bottom", "number", ["number"]); var AVFrame_crop_bottom_s = Module.AVFrame_crop_bottom_s = CAccessors.AVFrame_crop_bottom_s = Module.cwrap("AVFrame_crop_bottom_s", null, ["number", "number"]); var AVFrame_crop_left = Module.AVFrame_crop_left = CAccessors.AVFrame_crop_left = Module.cwrap("AVFrame_crop_left", "number", ["number"]); var AVFrame_crop_left_s = Module.AVFrame_crop_left_s = CAccessors.AVFrame_crop_left_s = Module.cwrap("AVFrame_crop_left_s", null, ["number", "number"]); var AVFrame_crop_right = Module.AVFrame_crop_right = CAccessors.AVFrame_crop_right = Module.cwrap("AVFrame_crop_right", "number", ["number"]); var AVFrame_crop_right_s = Module.AVFrame_crop_right_s = CAccessors.AVFrame_crop_right_s = Module.cwrap("AVFrame_crop_right_s", null, ["number", "number"]); var AVFrame_crop_top = Module.AVFrame_crop_top = CAccessors.AVFrame_crop_top = Module.cwrap("AVFrame_crop_top", "number", ["number"]); var AVFrame_crop_top_s = Module.AVFrame_crop_top_s = CAccessors.AVFrame_crop_top_s = Module.cwrap("AVFrame_crop_top_s", null, ["number", "number"]); var AVFrame_data_a = Module.AVFrame_data_a = CAccessors.AVFrame_data_a = Module.cwrap("AVFrame_data_a", "number", ["number", "number"]); var AVFrame_data_a_s = Module.AVFrame_data_a_s = CAccessors.AVFrame_data_a_s = Module.cwrap("AVFrame_data_a_s", null, ["number", "number", "number"]); var AVFrame_format = Module.AVFrame_format = CAccessors.AVFrame_format = Module.cwrap("AVFrame_format", "number", ["number"]); var AVFrame_format_s = Module.AVFrame_format_s = CAccessors.AVFrame_format_s = Module.cwrap("AVFrame_format_s", null, ["number", "number"]); var AVFrame_height = Module.AVFrame_height = CAccessors.AVFrame_height = Module.cwrap("AVFrame_height", "number", ["number"]); var AVFrame_height_s = Module.AVFrame_height_s = CAccessors.AVFrame_height_s = Module.cwrap("AVFrame_height_s", null, ["number", "number"]); var AVFrame_key_frame = Module.AVFrame_key_frame = CAccessors.AVFrame_key_frame = Module.cwrap("AVFrame_key_frame", "number", ["number"]); var AVFrame_key_frame_s = Module.AVFrame_key_frame_s = CAccessors.AVFrame_key_frame_s = Module.cwrap("AVFrame_key_frame_s", null, ["number", "number"]); var AVFrame_linesize_a = Module.AVFrame_linesize_a = CAccessors.AVFrame_linesize_a = Module.cwrap("AVFrame_linesize_a", "number", ["number", "number"]); var AVFrame_linesize_a_s = Module.AVFrame_linesize_a_s = CAccessors.AVFrame_linesize_a_s = Module.cwrap("AVFrame_linesize_a_s", null, ["number", "number", "number"]); var AVFrame_nb_samples = Module.AVFrame_nb_samples = CAccessors.AVFrame_nb_samples = Module.cwrap("AVFrame_nb_samples", "number", ["number"]); var AVFrame_nb_samples_s = Module.AVFrame_nb_samples_s = CAccessors.AVFrame_nb_samples_s = Module.cwrap("AVFrame_nb_samples_s", null, ["number", "number"]); var AVFrame_pict_type = Module.AVFrame_pict_type = CAccessors.AVFrame_pict_type = Module.cwrap("AVFrame_pict_type", "number", ["number"]); var AVFrame_pict_type_s = Module.AVFrame_pict_type_s = CAccessors.AVFrame_pict_type_s = Module.cwrap("AVFrame_pict_type_s", null, ["number", "number"]); var AVFrame_pts = Module.AVFrame_pts = CAccessors.AVFrame_pts = Module.cwrap("AVFrame_pts", "number", ["number"]); var AVFrame_pts_s = Module.AVFrame_pts_s = CAccessors.AVFrame_pts_s = Module.cwrap("AVFrame_pts_s", null, ["number", "number"]); var AVFrame_ptshi = Module.AVFrame_ptshi = CAccessors.AVFrame_ptshi = Module.cwrap("AVFrame_ptshi", "number", ["number"]); var AVFrame_ptshi_s = Module.AVFrame_ptshi_s = CAccessors.AVFrame_ptshi_s = Module.cwrap("AVFrame_ptshi_s", null, ["number", "number"]); var AVFrame_sample_aspect_ratio_num = Module.AVFrame_sample_aspect_ratio_num = CAccessors.AVFrame_sample_aspect_ratio_num = Module.cwrap("AVFrame_sample_aspect_ratio_num", "number", ["number"]); var AVFrame_sample_aspect_ratio_num_s = Module.AVFrame_sample_aspect_ratio_num_s = CAccessors.AVFrame_sample_aspect_ratio_num_s = Module.cwrap("AVFrame_sample_aspect_ratio_num_s", null, ["number", "number"]); var AVFrame_sample_aspect_ratio_den = Module.AVFrame_sample_aspect_ratio_den = CAccessors.AVFrame_sample_aspect_ratio_den = Module.cwrap("AVFrame_sample_aspect_ratio_den", "number", ["number"]); var AVFrame_sample_aspect_ratio_den_s = Module.AVFrame_sample_aspect_ratio_den_s = CAccessors.AVFrame_sample_aspect_ratio_den_s = Module.cwrap("AVFrame_sample_aspect_ratio_den_s", null, ["number", "number"]); var AVFrame_sample_aspect_ratio_s = Module.AVFrame_sample_aspect_ratio_s = CAccessors.AVFrame_sample_aspect_ratio_s = Module.cwrap("AVFrame_sample_aspect_ratio_s", null, ["number", "number", "number"]); var AVFrame_sample_rate = Module.AVFrame_sample_rate = CAccessors.AVFrame_sample_rate = Module.cwrap("AVFrame_sample_rate", "number", ["number"]); var AVFrame_sample_rate_s = Module.AVFrame_sample_rate_s = CAccessors.AVFrame_sample_rate_s = Module.cwrap("AVFrame_sample_rate_s", null, ["number", "number"]); var AVFrame_time_base_num = Module.AVFrame_time_base_num = CAccessors.AVFrame_time_base_num = Module.cwrap("AVFrame_time_base_num", "number", ["number"]); var AVFrame_time_base_num_s = Module.AVFrame_time_base_num_s = CAccessors.AVFrame_time_base_num_s = Module.cwrap("AVFrame_time_base_num_s", null, ["number", "number"]); var AVFrame_time_base_den = Module.AVFrame_time_base_den = CAccessors.AVFrame_time_base_den = Module.cwrap("AVFrame_time_base_den", "number", ["number"]); var AVFrame_time_base_den_s = Module.AVFrame_time_base_den_s = CAccessors.AVFrame_time_base_den_s = Module.cwrap("AVFrame_time_base_den_s", null, ["number", "number"]); var AVFrame_time_base_s = Module.AVFrame_time_base_s = CAccessors.AVFrame_time_base_s = Module.cwrap("AVFrame_time_base_s", null, ["number", "number", "number"]); var AVFrame_width = Module.AVFrame_width = CAccessors.AVFrame_width = Module.cwrap("AVFrame_width", "number", ["number"]); var AVFrame_width_s = Module.AVFrame_width_s = CAccessors.AVFrame_width_s = Module.cwrap("AVFrame_width_s", null, ["number", "number"]); var AVPixFmtDescriptor_flags = Module.AVPixFmtDescriptor_flags = CAccessors.AVPixFmtDescriptor_flags = Module.cwrap("AVPixFmtDescriptor_flags", "number", ["number"]); var AVPixFmtDescriptor_flags_s = Module.AVPixFmtDescriptor_flags_s = CAccessors.AVPixFmtDescriptor_flags_s = Module.cwrap("AVPixFmtDescriptor_flags_s", null, ["number", "number"]); var AVPixFmtDescriptor_log2_chroma_h = Module.AVPixFmtDescriptor_log2_chroma_h = CAccessors.AVPixFmtDescriptor_log2_chroma_h = Module.cwrap("AVPixFmtDescriptor_log2_chroma_h", "number", ["number"]); var AVPixFmtDescriptor_log2_chroma_h_s = Module.AVPixFmtDescriptor_log2_chroma_h_s = CAccessors.AVPixFmtDescriptor_log2_chroma_h_s = Module.cwrap("AVPixFmtDescriptor_log2_chroma_h_s", null, ["number", "number"]); var AVPixFmtDescriptor_log2_chroma_w = Module.AVPixFmtDescriptor_log2_chroma_w = CAccessors.AVPixFmtDescriptor_log2_chroma_w = Module.cwrap("AVPixFmtDescriptor_log2_chroma_w", "number", ["number"]); var AVPixFmtDescriptor_log2_chroma_w_s = Module.AVPixFmtDescriptor_log2_chroma_w_s = CAccessors.AVPixFmtDescriptor_log2_chroma_w_s = Module.cwrap("AVPixFmtDescriptor_log2_chroma_w_s", null, ["number", "number"]); var AVPixFmtDescriptor_nb_components = Module.AVPixFmtDescriptor_nb_components = CAccessors.AVPixFmtDescriptor_nb_components = Module.cwrap("AVPixFmtDescriptor_nb_components", "number", ["number"]); var AVPixFmtDescriptor_nb_components_s = Module.AVPixFmtDescriptor_nb_components_s = CAccessors.AVPixFmtDescriptor_nb_components_s = Module.cwrap("AVPixFmtDescriptor_nb_components_s", null, ["number", "number"]); var AVCodec_name = Module.AVCodec_name = CAccessors.AVCodec_name = Module.cwrap("AVCodec_name", "string", ["number"]); var AVCodec_sample_fmts = Module.AVCodec_sample_fmts = CAccessors.AVCodec_sample_fmts = Module.cwrap("AVCodec_sample_fmts", "number", ["number"]); var AVCodec_sample_fmts_s = Module.AVCodec_sample_fmts_s = CAccessors.AVCodec_sample_fmts_s = Module.cwrap("AVCodec_sample_fmts_s", null, ["number", "number"]); var AVCodec_sample_fmts_a = Module.AVCodec_sample_fmts_a = CAccessors.AVCodec_sample_fmts_a = Module.cwrap("AVCodec_sample_fmts_a", "number", ["number", "number"]); var AVCodec_sample_fmts_a_s = Module.AVCodec_sample_fmts_a_s = CAccessors.AVCodec_sample_fmts_a_s = Module.cwrap("AVCodec_sample_fmts_a_s", null, ["number", "number", "number"]); var AVCodec_supported_samplerates = Module.AVCodec_supported_samplerates = CAccessors.AVCodec_supported_samplerates = Module.cwrap("AVCodec_supported_samplerates", "number", ["number"]); var AVCodec_supported_samplerates_s = Module.AVCodec_supported_samplerates_s = CAccessors.AVCodec_supported_samplerates_s = Module.cwrap("AVCodec_supported_samplerates_s", null, ["number", "number"]); var AVCodec_supported_samplerates_a = Module.AVCodec_supported_samplerates_a = CAccessors.AVCodec_supported_samplerates_a = Module.cwrap("AVCodec_supported_samplerates_a", "number", ["number", "number"]); var AVCodec_supported_samplerates_a_s = Module.AVCodec_supported_samplerates_a_s = CAccessors.AVCodec_supported_samplerates_a_s = Module.cwrap("AVCodec_supported_samplerates_a_s", null, ["number", "number", "number"]); var AVCodec_type = Module.AVCodec_type = CAccessors.AVCodec_type = Module.cwrap("AVCodec_type", "number", ["number"]); var AVCodec_type_s = Module.AVCodec_type_s = CAccessors.AVCodec_type_s = Module.cwrap("AVCodec_type_s", null, ["number", "number"]); var AVCodecContext_codec_id = Module.AVCodecContext_codec_id = CAccessors.AVCodecContext_codec_id = Module.cwrap("AVCodecContext_codec_id", "number", ["number"]); var AVCodecContext_codec_id_s = Module.AVCodecContext_codec_id_s = CAccessors.AVCodecContext_codec_id_s = Module.cwrap("AVCodecContext_codec_id_s", null, ["number", "number"]); var AVCodecContext_codec_type = Module.AVCodecContext_codec_type = CAccessors.AVCodecContext_codec_type = Module.cwrap("AVCodecContext_codec_type", "number", ["number"]); var AVCodecContext_codec_type_s = Module.AVCodecContext_codec_type_s = CAccessors.AVCodecContext_codec_type_s = Module.cwrap("AVCodecContext_codec_type_s", null, ["number", "number"]); var AVCodecContext_bit_rate = Module.AVCodecContext_bit_rate = CAccessors.AVCodecContext_bit_rate = Module.cwrap("AVCodecContext_bit_rate", "number", ["number"]); var AVCodecContext_bit_rate_s = Module.AVCodecContext_bit_rate_s = CAccessors.AVCodecContext_bit_rate_s = Module.cwrap("AVCodecContext_bit_rate_s", null, ["number", "number"]); var AVCodecContext_bit_ratehi = Module.AVCodecContext_bit_ratehi = CAccessors.AVCodecContext_bit_ratehi = Module.cwrap("AVCodecContext_bit_ratehi", "number", ["number"]); var AVCodecContext_bit_ratehi_s = Module.AVCodecContext_bit_ratehi_s = CAccessors.AVCodecContext_bit_ratehi_s = Module.cwrap("AVCodecContext_bit_ratehi_s", null, ["number", "number"]); var AVCodecContext_channel_layout = Module.AVCodecContext_channel_layout = CAccessors.AVCodecContext_channel_layout = Module.cwrap("AVCodecContext_channel_layout", "number", ["number"]); var AVCodecContext_channel_layout_s = Module.AVCodecContext_channel_layout_s = CAccessors.AVCodecContext_channel_layout_s = Module.cwrap("AVCodecContext_channel_layout_s", null, ["number", "number"]); var AVCodecContext_channel_layouthi = Module.AVCodecContext_channel_layouthi = CAccessors.AVCodecContext_channel_layouthi = Module.cwrap("AVCodecContext_channel_layouthi", "number", ["number"]); var AVCodecContext_channel_layouthi_s = Module.AVCodecContext_channel_layouthi_s = CAccessors.AVCodecContext_channel_layouthi_s = Module.cwrap("AVCodecContext_channel_layouthi_s", null, ["number", "number"]); var AVCodecContext_channels = Module.AVCodecContext_channels = CAccessors.AVCodecContext_channels = Module.cwrap("AVCodecContext_channels", "number", ["number"]); var AVCodecContext_channels_s = Module.AVCodecContext_channels_s = CAccessors.AVCodecContext_channels_s = Module.cwrap("AVCodecContext_channels_s", null, ["number", "number"]); var AVCodecContext_channel_layoutmask = Module.AVCodecContext_channel_layoutmask = CAccessors.AVCodecContext_channel_layoutmask = Module.cwrap("AVCodecContext_channel_layoutmask", "number", ["number"]); var AVCodecContext_channel_layoutmask_s = Module.AVCodecContext_channel_layoutmask_s = CAccessors.AVCodecContext_channel_layoutmask_s = Module.cwrap("AVCodecContext_channel_layoutmask_s", null, ["number", "number"]); var AVCodecContext_ch_layout_nb_channels = Module.AVCodecContext_ch_layout_nb_channels = CAccessors.AVCodecContext_ch_layout_nb_channels = Module.cwrap("AVCodecContext_ch_layout_nb_channels", "number", ["number"]); var AVCodecContext_ch_layout_nb_channels_s = Module.AVCodecContext_ch_layout_nb_channels_s = CAccessors.AVCodecContext_ch_layout_nb_channels_s = Module.cwrap("AVCodecContext_ch_layout_nb_channels_s", null, ["number", "number"]); var AVCodecContext_extradata = Module.AVCodecContext_extradata = CAccessors.AVCodecContext_extradata = Module.cwrap("AVCodecContext_extradata", "number", ["number"]); var AVCodecContext_extradata_s = Module.AVCodecContext_extradata_s = CAccessors.AVCodecContext_extradata_s = Module.cwrap("AVCodecContext_extradata_s", null, ["number", "number"]); var AVCodecContext_extradata_size = Module.AVCodecContext_extradata_size = CAccessors.AVCodecContext_extradata_size = Module.cwrap("AVCodecContext_extradata_size", "number", ["number"]); var AVCodecContext_extradata_size_s = Module.AVCodecContext_extradata_size_s = CAccessors.AVCodecContext_extradata_size_s = Module.cwrap("AVCodecContext_extradata_size_s", null, ["number", "number"]); var AVCodecContext_frame_size = Module.AVCodecContext_frame_size = CAccessors.AVCodecContext_frame_size = Module.cwrap("AVCodecContext_frame_size", "number", ["number"]); var AVCodecContext_frame_size_s = Module.AVCodecContext_frame_size_s = CAccessors.AVCodecContext_frame_size_s = Module.cwrap("AVCodecContext_frame_size_s", null, ["number", "number"]); var AVCodecContext_framerate_num = Module.AVCodecContext_framerate_num = CAccessors.AVCodecContext_framerate_num = Module.cwrap("AVCodecContext_framerate_num", "number", ["number"]); var AVCodecContext_framerate_num_s = Module.AVCodecContext_framerate_num_s = CAccessors.AVCodecContext_framerate_num_s = Module.cwrap("AVCodecContext_framerate_num_s", null, ["number", "number"]); var AVCodecContext_framerate_den = Module.AVCodecContext_framerate_den = CAccessors.AVCodecContext_framerate_den = Module.cwrap("AVCodecContext_framerate_den", "number", ["number"]); var AVCodecContext_framerate_den_s = Module.AVCodecContext_framerate_den_s = CAccessors.AVCodecContext_framerate_den_s = Module.cwrap("AVCodecContext_framerate_den_s", null, ["number", "number"]); var AVCodecContext_framerate_s = Module.AVCodecContext_framerate_s = CAccessors.AVCodecContext_framerate_s = Module.cwrap("AVCodecContext_framerate_s", null, ["number", "number", "number"]); var AVCodecContext_gop_size = Module.AVCodecContext_gop_size = CAccessors.AVCodecContext_gop_size = Module.cwrap("AVCodecContext_gop_size", "number", ["number"]); var AVCodecContext_gop_size_s = Module.AVCodecContext_gop_size_s = CAccessors.AVCodecContext_gop_size_s = Module.cwrap("AVCodecContext_gop_size_s", null, ["number", "number"]); var AVCodecContext_height = Module.AVCodecContext_height = CAccessors.AVCodecContext_height = Module.cwrap("AVCodecContext_height", "number", ["number"]); var AVCodecContext_height_s = Module.AVCodecContext_height_s = CAccessors.AVCodecContext_height_s = Module.cwrap("AVCodecContext_height_s", null, ["number", "number"]); var AVCodecContext_keyint_min = Module.AVCodecContext_keyint_min = CAccessors.AVCodecContext_keyint_min = Module.cwrap("AVCodecContext_keyint_min", "number", ["number"]); var AVCodecContext_keyint_min_s = Module.AVCodecContext_keyint_min_s = CAccessors.AVCodecContext_keyint_min_s = Module.cwrap("AVCodecContext_keyint_min_s", null, ["number", "number"]); var AVCodecContext_level = Module.AVCodecContext_level = CAccessors.AVCodecContext_level = Module.cwrap("AVCodecContext_level", "number", ["number"]); var AVCodecContext_level_s = Module.AVCodecContext_level_s = CAccessors.AVCodecContext_level_s = Module.cwrap("AVCodecContext_level_s", null, ["number", "number"]); var AVCodecContext_max_b_frames = Module.AVCodecContext_max_b_frames = CAccessors.AVCodecContext_max_b_frames = Module.cwrap("AVCodecContext_max_b_frames", "number", ["number"]); var AVCodecContext_max_b_frames_s = Module.AVCodecContext_max_b_frames_s = CAccessors.AVCodecContext_max_b_frames_s = Module.cwrap("AVCodecContext_max_b_frames_s", null, ["number", "number"]); var AVCodecContext_pix_fmt = Module.AVCodecContext_pix_fmt = CAccessors.AVCodecContext_pix_fmt = Module.cwrap("AVCodecContext_pix_fmt", "number", ["number"]); var AVCodecContext_pix_fmt_s = Module.AVCodecContext_pix_fmt_s = CAccessors.AVCodecContext_pix_fmt_s = Module.cwrap("AVCodecContext_pix_fmt_s", null, ["number", "number"]); var AVCodecContext_profile = Module.AVCodecContext_profile = CAccessors.AVCodecContext_profile = Module.cwrap("AVCodecContext_profile", "number", ["number"]); var AVCodecContext_profile_s = Module.AVCodecContext_profile_s = CAccessors.AVCodecContext_profile_s = Module.cwrap("AVCodecContext_profile_s", null, ["number", "number"]); var AVCodecContext_rc_max_rate = Module.AVCodecContext_rc_max_rate = CAccessors.AVCodecContext_rc_max_rate = Module.cwrap("AVCodecContext_rc_max_rate", "number", ["number"]); var AVCodecContext_rc_max_rate_s = Module.AVCodecContext_rc_max_rate_s = CAccessors.AVCodecContext_rc_max_rate_s = Module.cwrap("AVCodecContext_rc_max_rate_s", null, ["number", "number"]); var AVCodecContext_rc_max_ratehi = Module.AVCodecContext_rc_max_ratehi = CAccessors.AVCodecContext_rc_max_ratehi = Module.cwrap("AVCodecContext_rc_max_ratehi", "number", ["number"]); var AVCodecContext_rc_max_ratehi_s = Module.AVCodecContext_rc_max_ratehi_s = CAccessors.AVCodecContext_rc_max_ratehi_s = Module.cwrap("AVCodecContext_rc_max_ratehi_s", null, ["number", "number"]); var AVCodecContext_rc_min_rate = Module.AVCodecContext_rc_min_rate = CAccessors.AVCodecContext_rc_min_rate = Module.cwrap("AVCodecContext_rc_min_rate", "number", ["number"]); var AVCodecContext_rc_min_rate_s = Module.AVCodecContext_rc_min_rate_s = CAccessors.AVCodecContext_rc_min_rate_s = Module.cwrap("AVCodecContext_rc_min_rate_s", null, ["number", "number"]); var AVCodecContext_rc_min_ratehi = Module.AVCodecContext_rc_min_ratehi = CAccessors.AVCodecContext_rc_min_ratehi = Module.cwrap("AVCodecContext_rc_min_ratehi", "number", ["number"]); var AVCodecContext_rc_min_ratehi_s = Module.AVCodecContext_rc_min_ratehi_s = CAccessors.AVCodecContext_rc_min_ratehi_s = Module.cwrap("AVCodecContext_rc_min_ratehi_s", null, ["number", "number"]); var AVCodecContext_sample_aspect_ratio_num = Module.AVCodecContext_sample_aspect_ratio_num = CAccessors.AVCodecContext_sample_aspect_ratio_num = Module.cwrap("AVCodecContext_sample_aspect_ratio_num", "number", ["number"]); var AVCodecContext_sample_aspect_ratio_num_s = Module.AVCodecContext_sample_aspect_ratio_num_s = CAccessors.AVCodecContext_sample_aspect_ratio_num_s = Module.cwrap("AVCodecContext_sample_aspect_ratio_num_s", null, ["number", "number"]); var AVCodecContext_sample_aspect_ratio_den = Module.AVCodecContext_sample_aspect_ratio_den = CAccessors.AVCodecContext_sample_aspect_ratio_den = Module.cwrap("AVCodecContext_sample_aspect_ratio_den", "number", ["number"]); var AVCodecContext_sample_aspect_ratio_den_s = Module.AVCodecContext_sample_aspect_ratio_den_s = CAccessors.AVCodecContext_sample_aspect_ratio_den_s = Module.cwrap("AVCodecContext_sample_aspect_ratio_den_s", null, ["number", "number"]); var AVCodecContext_sample_aspect_ratio_s = Module.AVCodecContext_sample_aspect_ratio_s = CAccessors.AVCodecContext_sample_aspect_ratio_s = Module.cwrap("AVCodecContext_sample_aspect_ratio_s", null, ["number", "number", "number"]); var AVCodecContext_sample_fmt = Module.AVCodecContext_sample_fmt = CAccessors.AVCodecContext_sample_fmt = Module.cwrap("AVCodecContext_sample_fmt", "number", ["number"]); var AVCodecContext_sample_fmt_s = Module.AVCodecContext_sample_fmt_s = CAccessors.AVCodecContext_sample_fmt_s = Module.cwrap("AVCodecContext_sample_fmt_s", null, ["number", "number"]); var AVCodecContext_sample_rate = Module.AVCodecContext_sample_rate = CAccessors.AVCodecContext_sample_rate = Module.cwrap("AVCodecContext_sample_rate", "number", ["number"]); var AVCodecContext_sample_rate_s = Module.AVCodecContext_sample_rate_s = CAccessors.AVCodecContext_sample_rate_s = Module.cwrap("AVCodecContext_sample_rate_s", null, ["number", "number"]); var AVCodecContext_time_base_num = Module.AVCodecContext_time_base_num = CAccessors.AVCodecContext_time_base_num = Module.cwrap("AVCodecContext_time_base_num", "number", ["number"]); var AVCodecContext_time_base_num_s = Module.AVCodecContext_time_base_num_s = CAccessors.AVCodecContext_time_base_num_s = Module.cwrap("AVCodecContext_time_base_num_s", null, ["number", "number"]); var AVCodecContext_time_base_den = Module.AVCodecContext_time_base_den = CAccessors.AVCodecContext_time_base_den = Module.cwrap("AVCodecContext_time_base_den", "number", ["number"]); var AVCodecContext_time_base_den_s = Module.AVCodecContext_time_base_den_s = CAccessors.AVCodecContext_time_base_den_s = Module.cwrap("AVCodecContext_time_base_den_s", null, ["number", "number"]); var AVCodecContext_time_base_s = Module.AVCodecContext_time_base_s = CAccessors.AVCodecContext_time_base_s = Module.cwrap("AVCodecContext_time_base_s", null, ["number", "number", "number"]); var AVCodecContext_qmax = Module.AVCodecContext_qmax = CAccessors.AVCodecContext_qmax = Module.cwrap("AVCodecContext_qmax", "number", ["number"]); var AVCodecContext_qmax_s = Module.AVCodecContext_qmax_s = CAccessors.AVCodecContext_qmax_s = Module.cwrap("AVCodecContext_qmax_s", null, ["number", "number"]); var AVCodecContext_qmin = Module.AVCodecContext_qmin = CAccessors.AVCodecContext_qmin = Module.cwrap("AVCodecContext_qmin", "number", ["number"]); var AVCodecContext_qmin_s = Module.AVCodecContext_qmin_s = CAccessors.AVCodecContext_qmin_s = Module.cwrap("AVCodecContext_qmin_s", null, ["number", "number"]); var AVCodecContext_width = Module.AVCodecContext_width = CAccessors.AVCodecContext_width = Module.cwrap("AVCodecContext_width", "number", ["number"]); var AVCodecContext_width_s = Module.AVCodecContext_width_s = CAccessors.AVCodecContext_width_s = Module.cwrap("AVCodecContext_width_s", null, ["number", "number"]); var AVCodecDescriptor_id = Module.AVCodecDescriptor_id = CAccessors.AVCodecDescriptor_id = Module.cwrap("AVCodecDescriptor_id", "number", ["number"]); var AVCodecDescriptor_id_s = Module.AVCodecDescriptor_id_s = CAccessors.AVCodecDescriptor_id_s = Module.cwrap("AVCodecDescriptor_id_s", null, ["number", "number"]); var AVCodecDescriptor_long_name = Module.AVCodecDescriptor_long_name = CAccessors.AVCodecDescriptor_long_name = Module.cwrap("AVCodecDescriptor_long_name", "number", ["number"]); var AVCodecDescriptor_long_name_s = Module.AVCodecDescriptor_long_name_s = CAccessors.AVCodecDescriptor_long_name_s = Module.cwrap("AVCodecDescriptor_long_name_s", null, ["number", "number"]); var AVCodecDescriptor_mime_types_a = Module.AVCodecDescriptor_mime_types_a = CAccessors.AVCodecDescriptor_mime_types_a = Module.cwrap("AVCodecDescriptor_mime_types_a", "number", ["number", "number"]); var AVCodecDescriptor_mime_types_a_s = Module.AVCodecDescriptor_mime_types_a_s = CAccessors.AVCodecDescriptor_mime_types_a_s = Module.cwrap("AVCodecDescriptor_mime_types_a_s", null, ["number", "number", "number"]); var AVCodecDescriptor_name = Module.AVCodecDescriptor_name = CAccessors.AVCodecDescriptor_name = Module.cwrap("AVCodecDescriptor_name", "number", ["number"]); var AVCodecDescriptor_name_s = Module.AVCodecDescriptor_name_s = CAccessors.AVCodecDescriptor_name_s = Module.cwrap("AVCodecDescriptor_name_s", null, ["number", "number"]); var AVCodecDescriptor_props = Module.AVCodecDescriptor_props = CAccessors.AVCodecDescriptor_props = Module.cwrap("AVCodecDescriptor_props", "number", ["number"]); var AVCodecDescriptor_props_s = Module.AVCodecDescriptor_props_s = CAccessors.AVCodecDescriptor_props_s = Module.cwrap("AVCodecDescriptor_props_s", null, ["number", "number"]); var AVCodecDescriptor_type = Module.AVCodecDescriptor_type = CAccessors.AVCodecDescriptor_type = Module.cwrap("AVCodecDescriptor_type", "number", ["number"]); var AVCodecDescriptor_type_s = Module.AVCodecDescriptor_type_s = CAccessors.AVCodecDescriptor_type_s = Module.cwrap("AVCodecDescriptor_type_s", null, ["number", "number"]); var AVCodecParameters_bit_rate = Module.AVCodecParameters_bit_rate = CAccessors.AVCodecParameters_bit_rate = Module.cwrap("AVCodecParameters_bit_rate", "number", ["number"]); var AVCodecParameters_bit_rate_s = Module.AVCodecParameters_bit_rate_s = CAccessors.AVCodecParameters_bit_rate_s = Module.cwrap("AVCodecParameters_bit_rate_s", null, ["number", "number"]); var AVCodecParameters_channel_layoutmask = Module.AVCodecParameters_channel_layoutmask = CAccessors.AVCodecParameters_channel_layoutmask = Module.cwrap("AVCodecParameters_channel_layoutmask", "number", ["number"]); var AVCodecParameters_channel_layoutmask_s = Module.AVCodecParameters_channel_layoutmask_s = CAccessors.AVCodecParameters_channel_layoutmask_s = Module.cwrap("AVCodecParameters_channel_layoutmask_s", null, ["number", "number"]); var AVCodecParameters_channels = Module.AVCodecParameters_channels = CAccessors.AVCodecParameters_channels = Module.cwrap("AVCodecParameters_channels", "number", ["number"]); var AVCodecParameters_channels_s = Module.AVCodecParameters_channels_s = CAccessors.AVCodecParameters_channels_s = Module.cwrap("AVCodecParameters_channels_s", null, ["number", "number"]); var AVCodecParameters_ch_layout_nb_channels = Module.AVCodecParameters_ch_layout_nb_channels = CAccessors.AVCodecParameters_ch_layout_nb_channels = Module.cwrap("AVCodecParameters_ch_layout_nb_channels", "number", ["number"]); var AVCodecParameters_ch_layout_nb_channels_s = Module.AVCodecParameters_ch_layout_nb_channels_s = CAccessors.AVCodecParameters_ch_layout_nb_channels_s = Module.cwrap("AVCodecParameters_ch_layout_nb_channels_s", null, ["number", "number"]); var AVCodecParameters_chroma_location = Module.AVCodecParameters_chroma_location = CAccessors.AVCodecParameters_chroma_location = Module.cwrap("AVCodecParameters_chroma_location", "number", ["number"]); var AVCodecParameters_chroma_location_s = Module.AVCodecParameters_chroma_location_s = CAccessors.AVCodecParameters_chroma_location_s = Module.cwrap("AVCodecParameters_chroma_location_s", null, ["number", "number"]); var AVCodecParameters_codec_id = Module.AVCodecParameters_codec_id = CAccessors.AVCodecParameters_codec_id = Module.cwrap("AVCodecParameters_codec_id", "number", ["number"]); var AVCodecParameters_codec_id_s = Module.AVCodecParameters_codec_id_s = CAccessors.AVCodecParameters_codec_id_s = Module.cwrap("AVCodecParameters_codec_id_s", null, ["number", "number"]); var AVCodecParameters_codec_tag = Module.AVCodecParameters_codec_tag = CAccessors.AVCodecParameters_codec_tag = Module.cwrap("AVCodecParameters_codec_tag", "number", ["number"]); var AVCodecParameters_codec_tag_s = Module.AVCodecParameters_codec_tag_s = CAccessors.AVCodecParameters_codec_tag_s = Module.cwrap("AVCodecParameters_codec_tag_s", null, ["number", "number"]); var AVCodecParameters_codec_type = Module.AVCodecParameters_codec_type = CAccessors.AVCodecParameters_codec_type = Module.cwrap("AVCodecParameters_codec_type", "number", ["number"]); var AVCodecParameters_codec_type_s = Module.AVCodecParameters_codec_type_s = CAccessors.AVCodecParameters_codec_type_s = Module.cwrap("AVCodecParameters_codec_type_s", null, ["number", "number"]); var AVCodecParameters_color_primaries = Module.AVCodecParameters_color_primaries = CAccessors.AVCodecParameters_color_primaries = Module.cwrap("AVCodecParameters_color_primaries", "number", ["number"]); var AVCodecParameters_color_primaries_s = Module.AVCodecParameters_color_primaries_s = CAccessors.AVCodecParameters_color_primaries_s = Module.cwrap("AVCodecParameters_color_primaries_s", null, ["number", "number"]); var AVCodecParameters_color_range = Module.AVCodecParameters_color_range = CAccessors.AVCodecParameters_color_range = Module.cwrap("AVCodecParameters_color_range", "number", ["number"]); var AVCodecParameters_color_range_s = Module.AVCodecParameters_color_range_s = CAccessors.AVCodecParameters_color_range_s = Module.cwrap("AVCodecParameters_color_range_s", null, ["number", "number"]); var AVCodecParameters_color_space = Module.AVCodecParameters_color_space = CAccessors.AVCodecParameters_color_space = Module.cwrap("AVCodecParameters_color_space", "number", ["number"]); var AVCodecParameters_color_space_s = Module.AVCodecParameters_color_space_s = CAccessors.AVCodecParameters_color_space_s = Module.cwrap("AVCodecParameters_color_space_s", null, ["number", "number"]); var AVCodecParameters_color_trc = Module.AVCodecParameters_color_trc = CAccessors.AVCodecParameters_color_trc = Module.cwrap("AVCodecParameters_color_trc", "number", ["number"]); var AVCodecParameters_color_trc_s = Module.AVCodecParameters_color_trc_s = CAccessors.AVCodecParameters_color_trc_s = Module.cwrap("AVCodecParameters_color_trc_s", null, ["number", "number"]); var AVCodecParameters_extradata = Module.AVCodecParameters_extradata = CAccessors.AVCodecParameters_extradata = Module.cwrap("AVCodecParameters_extradata", "number", ["number"]); var AVCodecParameters_extradata_s = Module.AVCodecParameters_extradata_s = CAccessors.AVCodecParameters_extradata_s = Module.cwrap("AVCodecParameters_extradata_s", null, ["number", "number"]); var AVCodecParameters_extradata_size = Module.AVCodecParameters_extradata_size = CAccessors.AVCodecParameters_extradata_size = Module.cwrap("AVCodecParameters_extradata_size", "number", ["number"]); var AVCodecParameters_extradata_size_s = Module.AVCodecParameters_extradata_size_s = CAccessors.AVCodecParameters_extradata_size_s = Module.cwrap("AVCodecParameters_extradata_size_s", null, ["number", "number"]); var AVCodecParameters_format = Module.AVCodecParameters_format = CAccessors.AVCodecParameters_format = Module.cwrap("AVCodecParameters_format", "number", ["number"]); var AVCodecParameters_format_s = Module.AVCodecParameters_format_s = CAccessors.AVCodecParameters_format_s = Module.cwrap("AVCodecParameters_format_s", null, ["number", "number"]); var AVCodecParameters_framerate_num = Module.AVCodecParameters_framerate_num = CAccessors.AVCodecParameters_framerate_num = Module.cwrap("AVCodecParameters_framerate_num", "number", ["number"]); var AVCodecParameters_framerate_num_s = Module.AVCodecParameters_framerate_num_s = CAccessors.AVCodecParameters_framerate_num_s = Module.cwrap("AVCodecParameters_framerate_num_s", null, ["number", "number"]); var AVCodecParameters_framerate_den = Module.AVCodecParameters_framerate_den = CAccessors.AVCodecParameters_framerate_den = Module.cwrap("AVCodecParameters_framerate_den", "number", ["number"]); var AVCodecParameters_framerate_den_s = Module.AVCodecParameters_framerate_den_s = CAccessors.AVCodecParameters_framerate_den_s = Module.cwrap("AVCodecParameters_framerate_den_s", null, ["number", "number"]); var AVCodecParameters_framerate_s = Module.AVCodecParameters_framerate_s = CAccessors.AVCodecParameters_framerate_s = Module.cwrap("AVCodecParameters_framerate_s", null, ["number", "number", "number"]); var AVCodecParameters_height = Module.AVCodecParameters_height = CAccessors.AVCodecParameters_height = Module.cwrap("AVCodecParameters_height", "number", ["number"]); var AVCodecParameters_height_s = Module.AVCodecParameters_height_s = CAccessors.AVCodecParameters_height_s = Module.cwrap("AVCodecParameters_height_s", null, ["number", "number"]); var AVCodecParameters_level = Module.AVCodecParameters_level = CAccessors.AVCodecParameters_level = Module.cwrap("AVCodecParameters_level", "number", ["number"]); var AVCodecParameters_level_s = Module.AVCodecParameters_level_s = CAccessors.AVCodecParameters_level_s = Module.cwrap("AVCodecParameters_level_s", null, ["number", "number"]); var AVCodecParameters_profile = Module.AVCodecParameters_profile = CAccessors.AVCodecParameters_profile = Module.cwrap("AVCodecParameters_profile", "number", ["number"]); var AVCodecParameters_profile_s = Module.AVCodecParameters_profile_s = CAccessors.AVCodecParameters_profile_s = Module.cwrap("AVCodecParameters_profile_s", null, ["number", "number"]); var AVCodecParameters_sample_rate = Module.AVCodecParameters_sample_rate = CAccessors.AVCodecParameters_sample_rate = Module.cwrap("AVCodecParameters_sample_rate", "number", ["number"]); var AVCodecParameters_sample_rate_s = Module.AVCodecParameters_sample_rate_s = CAccessors.AVCodecParameters_sample_rate_s = Module.cwrap("AVCodecParameters_sample_rate_s", null, ["number", "number"]); var AVCodecParameters_width = Module.AVCodecParameters_width = CAccessors.AVCodecParameters_width = Module.cwrap("AVCodecParameters_width", "number", ["number"]); var AVCodecParameters_width_s = Module.AVCodecParameters_width_s = CAccessors.AVCodecParameters_width_s = Module.cwrap("AVCodecParameters_width_s", null, ["number", "number"]); var AVPacket_data = Module.AVPacket_data = CAccessors.AVPacket_data = Module.cwrap("AVPacket_data", "number", ["number"]); var AVPacket_data_s = Module.AVPacket_data_s = CAccessors.AVPacket_data_s = Module.cwrap("AVPacket_data_s", null, ["number", "number"]); var AVPacket_dts = Module.AVPacket_dts = CAccessors.AVPacket_dts = Module.cwrap("AVPacket_dts", "number", ["number"]); var AVPacket_dts_s = Module.AVPacket_dts_s = CAccessors.AVPacket_dts_s = Module.cwrap("AVPacket_dts_s", null, ["number", "number"]); var AVPacket_dtshi = Module.AVPacket_dtshi = CAccessors.AVPacket_dtshi = Module.cwrap("AVPacket_dtshi", "number", ["number"]); var AVPacket_dtshi_s = Module.AVPacket_dtshi_s = CAccessors.AVPacket_dtshi_s = Module.cwrap("AVPacket_dtshi_s", null, ["number", "number"]); var AVPacket_duration = Module.AVPacket_duration = CAccessors.AVPacket_duration = Module.cwrap("AVPacket_duration", "number", ["number"]); var AVPacket_duration_s = Module.AVPacket_duration_s = CAccessors.AVPacket_duration_s = Module.cwrap("AVPacket_duration_s", null, ["number", "number"]); var AVPacket_durationhi = Module.AVPacket_durationhi = CAccessors.AVPacket_durationhi = Module.cwrap("AVPacket_durationhi", "number", ["number"]); var AVPacket_durationhi_s = Module.AVPacket_durationhi_s = CAccessors.AVPacket_durationhi_s = Module.cwrap("AVPacket_durationhi_s", null, ["number", "number"]); var AVPacket_flags = Module.AVPacket_flags = CAccessors.AVPacket_flags = Module.cwrap("AVPacket_flags", "number", ["number"]); var AVPacket_flags_s = Module.AVPacket_flags_s = CAccessors.AVPacket_flags_s = Module.cwrap("AVPacket_flags_s", null, ["number", "number"]); var AVPacket_pos = Module.AVPacket_pos = CAccessors.AVPacket_pos = Module.cwrap("AVPacket_pos", "number", ["number"]); var AVPacket_pos_s = Module.AVPacket_pos_s = CAccessors.AVPacket_pos_s = Module.cwrap("AVPacket_pos_s", null, ["number", "number"]); var AVPacket_poshi = Module.AVPacket_poshi = CAccessors.AVPacket_poshi = Module.cwrap("AVPacket_poshi", "number", ["number"]); var AVPacket_poshi_s = Module.AVPacket_poshi_s = CAccessors.AVPacket_poshi_s = Module.cwrap("AVPacket_poshi_s", null, ["number", "number"]); var AVPacket_pts = Module.AVPacket_pts = CAccessors.AVPacket_pts = Module.cwrap("AVPacket_pts", "number", ["number"]); var AVPacket_pts_s = Module.AVPacket_pts_s = CAccessors.AVPacket_pts_s = Module.cwrap("AVPacket_pts_s", null, ["number", "number"]); var AVPacket_ptshi = Module.AVPacket_ptshi = CAccessors.AVPacket_ptshi = Module.cwrap("AVPacket_ptshi", "number", ["number"]); var AVPacket_ptshi_s = Module.AVPacket_ptshi_s = CAccessors.AVPacket_ptshi_s = Module.cwrap("AVPacket_ptshi_s", null, ["number", "number"]); var AVPacket_side_data = Module.AVPacket_side_data = CAccessors.AVPacket_side_data = Module.cwrap("AVPacket_side_data", "number", ["number"]); var AVPacket_side_data_s = Module.AVPacket_side_data_s = CAccessors.AVPacket_side_data_s = Module.cwrap("AVPacket_side_data_s", null, ["number", "number"]); var AVPacket_side_data_elems = Module.AVPacket_side_data_elems = CAccessors.AVPacket_side_data_elems = Module.cwrap("AVPacket_side_data_elems", "number", ["number"]); var AVPacket_side_data_elems_s = Module.AVPacket_side_data_elems_s = CAccessors.AVPacket_side_data_elems_s = Module.cwrap("AVPacket_side_data_elems_s", null, ["number", "number"]); var AVPacket_size = Module.AVPacket_size = CAccessors.AVPacket_size = Module.cwrap("AVPacket_size", "number", ["number"]); var AVPacket_size_s = Module.AVPacket_size_s = CAccessors.AVPacket_size_s = Module.cwrap("AVPacket_size_s", null, ["number", "number"]); var AVPacket_stream_index = Module.AVPacket_stream_index = CAccessors.AVPacket_stream_index = Module.cwrap("AVPacket_stream_index", "number", ["number"]); var AVPacket_stream_index_s = Module.AVPacket_stream_index_s = CAccessors.AVPacket_stream_index_s = Module.cwrap("AVPacket_stream_index_s", null, ["number", "number"]); var AVPacket_time_base_num = Module.AVPacket_time_base_num = CAccessors.AVPacket_time_base_num = Module.cwrap("AVPacket_time_base_num", "number", ["number"]); var AVPacket_time_base_num_s = Module.AVPacket_time_base_num_s = CAccessors.AVPacket_time_base_num_s = Module.cwrap("AVPacket_time_base_num_s", null, ["number", "number"]); var AVPacket_time_base_den = Module.AVPacket_time_base_den = CAccessors.AVPacket_time_base_den = Module.cwrap("AVPacket_time_base_den", "number", ["number"]); var AVPacket_time_base_den_s = Module.AVPacket_time_base_den_s = CAccessors.AVPacket_time_base_den_s = Module.cwrap("AVPacket_time_base_den_s", null, ["number", "number"]); var AVPacket_time_base_s = Module.AVPacket_time_base_s = CAccessors.AVPacket_time_base_s = Module.cwrap("AVPacket_time_base_s", null, ["number", "number", "number"]); var AVFormatContext_duration = Module.AVFormatContext_duration = CAccessors.AVFormatContext_duration = Module.cwrap("AVFormatContext_duration", "number", ["number"]); var AVFormatContext_duration_s = Module.AVFormatContext_duration_s = CAccessors.AVFormatContext_duration_s = Module.cwrap("AVFormatContext_duration_s", null, ["number", "number"]); var AVFormatContext_durationhi = Module.AVFormatContext_durationhi = CAccessors.AVFormatContext_durationhi = Module.cwrap("AVFormatContext_durationhi", "number", ["number"]); var AVFormatContext_durationhi_s = Module.AVFormatContext_durationhi_s = CAccessors.AVFormatContext_durationhi_s = Module.cwrap("AVFormatContext_durationhi_s", null, ["number", "number"]); var AVFormatContext_flags = Module.AVFormatContext_flags = CAccessors.AVFormatContext_flags = Module.cwrap("AVFormatContext_flags", "number", ["number"]); var AVFormatContext_flags_s = Module.AVFormatContext_flags_s = CAccessors.AVFormatContext_flags_s = Module.cwrap("AVFormatContext_flags_s", null, ["number", "number"]); var AVFormatContext_nb_streams = Module.AVFormatContext_nb_streams = CAccessors.AVFormatContext_nb_streams = Module.cwrap("AVFormatContext_nb_streams", "number", ["number"]); var AVFormatContext_nb_streams_s = Module.AVFormatContext_nb_streams_s = CAccessors.AVFormatContext_nb_streams_s = Module.cwrap("AVFormatContext_nb_streams_s", null, ["number", "number"]); var AVFormatContext_oformat = Module.AVFormatContext_oformat = CAccessors.AVFormatContext_oformat = Module.cwrap("AVFormatContext_oformat", "number", ["number"]); var AVFormatContext_oformat_s = Module.AVFormatContext_oformat_s = CAccessors.AVFormatContext_oformat_s = Module.cwrap("AVFormatContext_oformat_s", null, ["number", "number"]); var AVFormatContext_pb = Module.AVFormatContext_pb = CAccessors.AVFormatContext_pb = Module.cwrap("AVFormatContext_pb", "number", ["number"]); var AVFormatContext_pb_s = Module.AVFormatContext_pb_s = CAccessors.AVFormatContext_pb_s = Module.cwrap("AVFormatContext_pb_s", null, ["number", "number"]); var AVFormatContext_start_time = Module.AVFormatContext_start_time = CAccessors.AVFormatContext_start_time = Module.cwrap("AVFormatContext_start_time", "number", ["number"]); var AVFormatContext_start_time_s = Module.AVFormatContext_start_time_s = CAccessors.AVFormatContext_start_time_s = Module.cwrap("AVFormatContext_start_time_s", null, ["number", "number"]); var AVFormatContext_start_timehi = Module.AVFormatContext_start_timehi = CAccessors.AVFormatContext_start_timehi = Module.cwrap("AVFormatContext_start_timehi", "number", ["number"]); var AVFormatContext_start_timehi_s = Module.AVFormatContext_start_timehi_s = CAccessors.AVFormatContext_start_timehi_s = Module.cwrap("AVFormatContext_start_timehi_s", null, ["number", "number"]); var AVFormatContext_streams_a = Module.AVFormatContext_streams_a = CAccessors.AVFormatContext_streams_a = Module.cwrap("AVFormatContext_streams_a", "number", ["number", "number"]); var AVFormatContext_streams_a_s = Module.AVFormatContext_streams_a_s = CAccessors.AVFormatContext_streams_a_s = Module.cwrap("AVFormatContext_streams_a_s", null, ["number", "number", "number"]); var AVStream_codecpar = Module.AVStream_codecpar = CAccessors.AVStream_codecpar = Module.cwrap("AVStream_codecpar", "number", ["number"]); var AVStream_codecpar_s = Module.AVStream_codecpar_s = CAccessors.AVStream_codecpar_s = Module.cwrap("AVStream_codecpar_s", null, ["number", "number"]); var AVStream_discard = Module.AVStream_discard = CAccessors.AVStream_discard = Module.cwrap("AVStream_discard", "number", ["number"]); var AVStream_discard_s = Module.AVStream_discard_s = CAccessors.AVStream_discard_s = Module.cwrap("AVStream_discard_s", null, ["number", "number"]); var AVStream_duration = Module.AVStream_duration = CAccessors.AVStream_duration = Module.cwrap("AVStream_duration", "number", ["number"]); var AVStream_duration_s = Module.AVStream_duration_s = CAccessors.AVStream_duration_s = Module.cwrap("AVStream_duration_s", null, ["number", "number"]); var AVStream_durationhi = Module.AVStream_durationhi = CAccessors.AVStream_durationhi = Module.cwrap("AVStream_durationhi", "number", ["number"]); var AVStream_durationhi_s = Module.AVStream_durationhi_s = CAccessors.AVStream_durationhi_s = Module.cwrap("AVStream_durationhi_s", null, ["number", "number"]); var AVStream_time_base_num = Module.AVStream_time_base_num = CAccessors.AVStream_time_base_num = Module.cwrap("AVStream_time_base_num", "number", ["number"]); var AVStream_time_base_num_s = Module.AVStream_time_base_num_s = CAccessors.AVStream_time_base_num_s = Module.cwrap("AVStream_time_base_num_s", null, ["number", "number"]); var AVStream_time_base_den = Module.AVStream_time_base_den = CAccessors.AVStream_time_base_den = Module.cwrap("AVStream_time_base_den", "number", ["number"]); var AVStream_time_base_den_s = Module.AVStream_time_base_den_s = CAccessors.AVStream_time_base_den_s = Module.cwrap("AVStream_time_base_den_s", null, ["number", "number"]); var AVStream_time_base_s = Module.AVStream_time_base_s = CAccessors.AVStream_time_base_s = Module.cwrap("AVStream_time_base_s", null, ["number", "number", "number"]); var AVFilterInOut_filter_ctx = Module.AVFilterInOut_filter_ctx = CAccessors.AVFilterInOut_filter_ctx = Module.cwrap("AVFilterInOut_filter_ctx", "number", ["number"]); var AVFilterInOut_filter_ctx_s = Module.AVFilterInOut_filter_ctx_s = CAccessors.AVFilterInOut_filter_ctx_s = Module.cwrap("AVFilterInOut_filter_ctx_s", null, ["number", "number"]); var AVFilterInOut_name = Module.AVFilterInOut_name = CAccessors.AVFilterInOut_name = Module.cwrap("AVFilterInOut_name", "number", ["number"]); var AVFilterInOut_name_s = Module.AVFilterInOut_name_s = CAccessors.AVFilterInOut_name_s = Module.cwrap("AVFilterInOut_name_s", null, ["number", "number"]); var AVFilterInOut_next = Module.AVFilterInOut_next = CAccessors.AVFilterInOut_next = Module.cwrap("AVFilterInOut_next", "number", ["number"]); var AVFilterInOut_next_s = Module.AVFilterInOut_next_s = CAccessors.AVFilterInOut_next_s = Module.cwrap("AVFilterInOut_next_s", null, ["number", "number"]); var AVFilterInOut_pad_idx = Module.AVFilterInOut_pad_idx = CAccessors.AVFilterInOut_pad_idx = Module.cwrap("AVFilterInOut_pad_idx", "number", ["number"]); var AVFilterInOut_pad_idx_s = Module.AVFilterInOut_pad_idx_s = CAccessors.AVFilterInOut_pad_idx_s = Module.cwrap("AVFilterInOut_pad_idx_s", null, ["number", "number"]); var av_frame_free_js = Module.av_frame_free_js = CAccessors.av_frame_free_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.av_frame_free(p2); free(p2) }; var av_packet_free_js = Module.av_packet_free_js = CAccessors.av_packet_free_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.av_packet_free(p2); free(p2) }; var avformat_close_input_js = Module.avformat_close_input_js = CAccessors.avformat_close_input_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.avformat_close_input(p2); free(p2) }; var avcodec_free_context_js = Module.avcodec_free_context_js = CAccessors.avcodec_free_context_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.avcodec_free_context(p2); free(p2) }; var avcodec_parameters_free_js = Module.avcodec_parameters_free_js = CAccessors.avcodec_parameters_free_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.avcodec_parameters_free(p2); free(p2) }; var avfilter_graph_free_js = Module.avfilter_graph_free_js = CAccessors.avfilter_graph_free_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.avfilter_graph_free(p2); free(p2) }; var avfilter_inout_free_js = Module.avfilter_inout_free_js = CAccessors.avfilter_inout_free_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.avfilter_inout_free(p2); free(p2) }; var av_dict_free_js = Module.av_dict_free_js = CAccessors.av_dict_free_js = function (p) { var p2 = malloc(4); if (p2 === 0) throw new Error("Could not malloc"); new Uint32Array(Module.HEAPU8.buffer, p2, 1)[0] = p; CAccessors.av_dict_free(p2); free(p2) }; var copyin_u8 = Module.copyin_u8 = CAccessors.copyin_u8 = function (ptr, arr) { var buf = new Uint8Array(Module.HEAPU8.buffer, ptr); buf.set(arr) }; var copyout_u8 = Module.copyout_u8 = CAccessors.copyout_u8 = function (ptr, len) { var ret = new Uint8Array(Module.HEAPU8.buffer, ptr, len).slice(0); ret.libavjsTransfer = [ret.buffer]; return ret }; var copyin_s16 = Module.copyin_s16 = CAccessors.copyin_s16 = function (ptr, arr) { var buf = new Int16Array(Module.HEAPU8.buffer, ptr); buf.set(arr) }; var copyout_s16 = Module.copyout_s16 = CAccessors.copyout_s16 = function (ptr, len) { var ret = new Int16Array(Module.HEAPU8.buffer, ptr, len).slice(0); ret.libavjsTransfer = [ret.buffer]; return ret }; var copyin_s32 = Module.copyin_s32 = CAccessors.copyin_s32 = function (ptr, arr) { var buf = new Int32Array(Module.HEAPU8.buffer, ptr); buf.set(arr) }; var copyout_s32 = Module.copyout_s32 = CAccessors.copyout_s32 = function (ptr, len) { var ret = new Int32Array(Module.HEAPU8.buffer, ptr, len).slice(0); ret.libavjsTransfer = [ret.buffer]; return ret }; var copyin_f32 = Module.copyin_f32 = CAccessors.copyin_f32 = function (ptr, arr) { var buf = new Float32Array(Module.HEAPU8.buffer, ptr); buf.set(arr) }; var copyout_f32 = Module.copyout_f32 = CAccessors.copyout_f32 = function (ptr, len) { var ret = new Float32Array(Module.HEAPU8.buffer, ptr, len).slice(0); ret.libavjsTransfer = [ret.buffer]; return ret }; function fsBinding(of) { Module[of] = function () { try { return FS[of].apply(FS, arguments) } catch (ex) { if (ex && ex.name === "ErrnoError") { ex.message = strerror(ex.errno); if (typeof arguments[0] === "string") ex.message = arguments[0] + ": " + ex.message } throw ex } } } var readerDev = FS.makedev(44, 0); FS.registerDevice(readerDev, readerCallbacks); Module.readBuffers = Object.create(null); Module.blockReadBuffers = Object.create(null); var writerDev = FS.makedev(44, 1); FS.registerDevice(writerDev, writerCallbacks); var streamWriterDev = FS.makedev(44, 2); FS.registerDevice(streamWriterDev, streamWriterCallbacks); fsBinding("readFile"); fsBinding("writeFile"); fsBinding("unlink"); fsBinding("unmount"); fsBinding("mkdev"); fsBinding("createLazyFile"); Module.mkreaderdev = function (loc, mode) { FS.mkdev(loc, mode ? mode : 511, readerDev); Module.readBuffers[loc] = { buf: new Uint8Array(0), eof: false, errorCode: 0, error: null }; return 0 }; var mkblockreaderdev = Module.mkblockreaderdev = function (name, size) { FS.writeFile(name, new Uint8Array(0)); var f = FS.open(name, 0); var super_node_ops = f.node.node_ops; var node_ops = f.node.node_ops = Object.create(super_node_ops); node_ops.getattr = function (node) { var ret = super_node_ops.getattr(node); ret.size = size; ret.blksize = 4096; ret.blocks = Math.ceil(size / 4096); return ret }; f.node.stream_ops = blockReaderCallbacks; f.node.ff_block_reader_dev_size = size; Module.blockReadBuffers[name] = { position: -1, buf: new Uint8Array(0), ready: false, errorCode: 0, error: null }; FS.close(f) }; var readaheads = {}; var preReadaheadOnBlockRead = null; function readaheadOnBlockRead(name, position, length) { if (!(name in readaheads)) { if (preReadaheadOnBlockRead) return preReadaheadOnBlockRead(name, position, length); return } var ra = readaheads[name]; function then() { if (ra.position !== position) { ra.position = position; ra.buf = null; ra.bufPromise = ra.file.slice(position, position + length).arrayBuffer().then(function (ret) { ra.buf = ret }).catch(function (ex) { console.error(ex + "\n" + ex.stack); ra.buf = new Uint8Array(0) }).then(then); return } ff_block_reader_dev_send(name, position, new Uint8Array(ra.buf)); position += length; ra.position = position; ra.buf = null; ra.bufPromise = ra.file.slice(position, position + length).arrayBuffer().then(function (ret) { ra.buf = ret }).catch(function (ex) { console.error(ex + "\n" + ex.stack); ra.buf = new Uint8Array(0) }) } if (!ra.buf && ra.bufPromise) ra.bufPromise.then(then); else then() } Module.mkreadaheadfile = function (name, file) { if (Module.onblockread !== readaheadOnBlockRead) { preReadaheadOnBlockRead = Module.onblockread; Module.onblockread = readaheadOnBlockRead } mkblockreaderdev(name, file.size); readaheads[name] = { file, position: -1, bufPromise: null, buf: null } }; Module.unlinkreadaheadfile = function (name) { FS.unlink(name); delete readaheads[name] }; var mkwriterdev = Module.mkwriterdev = function (loc, mode) { FS.mkdev(loc, mode ? mode : 511, writerDev); return 0 }; Module.mkstreamwriterdev = function (loc, mode) { FS.mkdev(loc, mode ? mode : 511, streamWriterDev); return 0 }; Module.mountwriterfs = function (mountpoint) { try { FS.mkdir(mountpoint) } catch (ex) { } FS.mount(streamWriterFS, {}, mountpoint); return 0 }; Module.ff_reader_dev_waiters = Object.create(null); Module.mkworkerfsfile = function (name, blob) { FS.mkdir("/" + name + ".d"); FS.mount(WORKERFS, { blobs: [{ name, data: blob }] }, "/" + name + ".d"); return "/" + name + ".d/" + name }; Module.unlinkworkerfsfile = function (name) { FS.unmount("/" + name + ".d"); FS.rmdir("/" + name + ".d") }; var fsfhs = {}; var preFSFHOnWrite = null; function fsfhOnWrite(name, position, buffer) { if (!(name in fsfhs)) { if (preFSFHOnWrite) return preFSFHOnWrite(name, position, buffer); return } var h = fsfhs[name]; buffer = buffer.slice(0); if (h.syncHandle) { h.syncHandle.write(buffer.buffer, { at: position }); return } var p = h.promise.then(function () { return h.handle.write({ type: "write", position, data: buffer }) }); h.promise = p.catch(console.error); return p } Module.mkfsfhfile = function (name, fsfh) { if (Module.onwrite !== fsfhOnWrite) { preFSFHOnWrite = Module.onwrite; Module.onwrite = fsfhOnWrite } mkwriterdev(name); var h = fsfhs[name] = { promise: Promise.all([]) }; h.promise = h.promise.then(function () { return fsfh.createSyncAccessHandle() }).then(function (syncHandle) { h.syncHandle = syncHandle }).catch(function () { return fsfh.createWritable() }).then(function (handle) { h.handle = handle }); return h.promise }; Module.unlinkfsfhfile = function (name) { FS.unlink(name); var h = fsfhs[name]; delete fsfhs[name]; if (h.syncHandle) { h.syncHandle.close(); return Promise.all([]) } return h.promise.then(function () { return h.handle.close() }) }; var ff_reader_dev_send = Module.ff_reader_dev_send = function (name, data, opts) { opts = opts || {}; var idata = Module.readBuffers[name]; if (data === null) { idata.eof = true } else { var newbuf = new Uint8Array(idata.buf.length + data.length); newbuf.set(idata.buf); newbuf.set(data, idata.buf.length); idata.buf = newbuf } idata.ready = true; idata.errorCode = 0; if (typeof opts.errorCode === "number") idata.errorCode = opts.errorCode; idata.error = null; if (opts.error) idata.error = opts.error; var waiters = Module.ff_reader_dev_waiters[name] || []; delete Module.ff_reader_dev_waiters[name]; for (var i = 0; i < waiters.length; i++)waiters[i]() }; var ff_block_reader_dev_send = Module.ff_block_reader_dev_send = function (name, pos, data, opts) { opts = opts || {}; var idata = Module.blockReadBuffers[name]; idata.position = pos; idata.buf = data; idata.ready = true; idata.errorCode = 0; idata.error = null; if (data === null) idata.buf = new Uint8Array(0); if (typeof opts.errorCode === "number") idata.errorCode = opts.errorCode; if (opts.error) idata.error = opts.error; var waiters = Module.ff_reader_dev_waiters[name] || []; delete Module.ff_reader_dev_waiters[name]; for (var i = 0; i < waiters.length; i++)waiters[i]() }; var ff_reader_dev_waiting = Module.ff_reader_dev_waiting = function (name) { console.log("[libav.js] ff_reader_dev_waiting is deprecated. Use the onread callback."); return ff_nothing().then(function () { if (name) return !!Module.ff_reader_dev_waiters[name]; else return !!Object.keys(Module.ff_reader_dev_waiters).length }) }; Module.readerDevReady = function (fd) { var stream = FS.streams[fd].node.name; if (stream in Module.readBuffers) return Module.readBuffers[stream].ready; else if (stream in Module.blockReadBuffers) return Module.blockReadBuffers[stream].ready; return false }; Module.fdName = function (fd) { return FS.streams[fd].node.name }; var ff_init_encoder = Module.ff_init_encoder = function (name, opts) { opts = opts || {}; var codec = avcodec_find_encoder_by_name(name); if (codec === 0) throw new Error("Codec not found"); var c = avcodec_alloc_context3(codec); if (c === 0) throw new Error("Could not allocate audio codec context"); var ctxProps = opts.ctx || {}; for (var prop in ctxProps) this["AVCodecContext_" + prop + "_s"](c, ctxProps[prop]); var time_base = opts.time_base || [1, 1e3]; AVCodecContext_time_base_s(c, time_base[0], time_base[1]); var options = 0; if (opts.options) { for (var prop in opts.options) options = av_dict_set_js(options, prop, opts.options[prop], 0) } var ret = avcodec_open2_js(c, codec, options); if (ret < 0) throw new Error("Could not open codec: " + ff_error(ret)); var frame = av_frame_alloc(); if (frame === 0) throw new Error("Could not allocate frame"); var pkt = av_packet_alloc(); if (pkt === 0) throw new Error("Could not allocate packet"); var frame_size = AVCodecContext_frame_size(c); return [codec, c, frame, pkt, frame_size] }; var ff_init_decoder = Module.ff_init_decoder = function (name, config) { if (typeof config === "number") { config = { codecpar: config } } else { config = config || {} } var codec, ret; if (typeof name === "string") codec = avcodec_find_decoder_by_name(name); else codec = avcodec_find_decoder(name); if (codec === 0) throw new Error("Codec not found"); var c = avcodec_alloc_context3(codec); if (c === 0) throw new Error("Could not allocate audio codec context"); var codecid = AVCodecContext_codec_id(c); if (config.codecpar) { var codecparPtr = 0; var codecpar = config.codecpar; if (typeof codecpar === "object") { codecparPtr = avcodec_parameters_alloc(); if (codecparPtr === 0) throw new Error("Failed to allocate codec parameters"); ff_copyin_codecpar(codecparPtr, codecpar); codecpar = codecparPtr } ret = avcodec_parameters_to_context(c, codecpar); if (codecparPtr) avcodec_parameters_free_js(codecparPtr); if (ret < 0) throw new Error("Could not set codec parameters: " + ff_error(ret)) } if (AVCodecContext_codec_id(c) === 0) AVCodecContext_codec_id_s(c, codecid); if (config.time_base) AVCodecContext_time_base_s(c, config.time_base[0], config.time_base[1]); ret = avcodec_open2(c, codec, 0); if (ret < 0) throw new Error("Could not open codec: " + ff_error(ret)); var pkt = av_packet_alloc(); if (pkt === 0) throw new Error("Could not allocate packet"); var frame = av_frame_alloc(); if (frame === 0) throw new Error("Could not allocate frame"); return [codec, c, pkt, frame] }; var ff_free_encoder = Module.ff_free_encoder = function (c, frame, pkt) { av_frame_free_js(frame); av_packet_free_js(pkt); avcodec_free_context_js(c) }; var ff_free_decoder = Module.ff_free_decoder = function (c, pkt, frame) { ff_free_encoder(c, frame, pkt) }; var ff_encode_multi = Module.ff_encode_multi = function (ctx, frame, pkt, inFrames, config) { if (typeof config === "boolean") { config = { fin: config } } else { config = config || {} } var outPackets = []; var tbNum = AVCodecContext_time_base_num(ctx); var tbDen = AVCodecContext_time_base_den(ctx); var copyoutPacket = function (ptr) { var ret = ff_copyout_packet(ptr); if (!ret.time_base_num) { ret.time_base_num = tbNum; ret.time_base_den = tbDen } return ret }; if (config.copyoutPacket === "ptr") { copyoutPacket = function (ptr) { var ret = ff_copyout_packet_ptr(ptr); if (!AVPacket_time_base_num(ret)) AVPacket_time_base_s(ret, tbNum, tbDen); return ret } } function handleFrame(inFrame) { if (inFrame !== null) { ff_copyin_frame(frame, inFrame); if (tbNum) { if (typeof inFrame === "number") { var itbn = AVFrame_time_base_num(frame); if (itbn) { ff_frame_rescale_ts_js(frame, itbn, AVFrame_time_base_den(frame), tbNum, tbDen); AVFrame_time_base_s(frame, tbNum, tbDen) } } else if (inFrame && inFrame.time_base_num) { ff_frame_rescale_ts_js(frame, inFrame.time_base_num, inFrame.time_base_den, tbNum, tbDen); AVFrame_time_base_s(frame, tbNum, tbDen) } } } var ret = avcodec_send_frame(ctx, inFrame ? frame : 0); if (ret < 0) throw new Error("Error sending the frame to the encoder: " + ff_error(ret)); if (inFrame) av_frame_unref(frame); while (true) { ret = avcodec_receive_packet(ctx, pkt); if (ret === -6 || ret === -541478725) return; else if (ret < 0) throw new Error("Error encoding audio frame: " + ff_error(ret)); outPackets.push(copyoutPacket(pkt)); av_packet_unref(pkt) } } inFrames.forEach(handleFrame); if (config.fin) handleFrame(null); return outPackets }; var ff_decode_multi = Module.ff_decode_multi = function (ctx, pkt, frame, inPackets, config) { var outFrames = []; var transfer = []; if (typeof config === "boolean") { config = { fin: config } } else { config = config || {} } var tbNum = AVCodecContext_time_base_num(ctx); var tbDen = AVCodecContext_time_base_den(ctx); var copyoutFrameO = ff_copyout_frame; if (config.copyoutFrame) copyoutFrameO = ff_copyout_frame_versions[config.copyoutFrame]; var copyoutFrame = function (ptr) { var ret = copyoutFrameO(ptr); if (!ret.time_base_num) { ret.time_base_num = tbNum; ret.time_base_den = tbDen } return ret }; if (config.copyoutFrame === "ptr") { copyoutFrame = function (ptr) { var ret = ff_copyout_frame_ptr(ptr); if (!AVFrame_time_base_num(ret)) AVFrame_time_base_s(ret, tbNum, tbDen); return ret } } function handlePacket(inPacket) { var ret; if (inPacket !== null) { ret = av_packet_make_writable(pkt); if (ret < 0) throw new Error("Failed to make packet writable: " + ff_error(ret)); ff_copyin_packet(pkt, inPacket); if (tbNum) { if (typeof inPacket === "number") { var iptbn = AVPacket_time_base_num(pkt); if (iptbn) { av_packet_rescale_ts_js(pkt, iptbn, AVPacket_time_base_den(pkt), tbNum, tbDen); AVPacket_time_base_s(pkt, tbNum, tbDen) } } else if (inPacket && inPacket.time_base_num) { av_packet_rescale_ts_js(pkt, inPacket.time_base_num, inPacket.time_base_den, tbNum, tbDen); AVPacket_time_base_s(pkt, tbNum, tbDen) } } } else { av_packet_unref(pkt) } ret = avcodec_send_packet(ctx, pkt); if (ret < 0) { var err = "Error submitting the packet to the decoder: " + ff_error(ret); if (!config.ignoreErrors) throw new Error(err); else { console.log(err); av_packet_unref(pkt); return } } av_packet_unref(pkt); while (true) { ret = avcodec_receive_frame(ctx, frame); if (ret === -6 || ret === -541478725) return; else if (ret < 0) throw new Error("Error decoding audio frame: " + ff_error(ret)); var outFrame = copyoutFrame(frame); if (outFrame && outFrame.libavjsTransfer && outFrame.libavjsTransfer.length) transfer.push.apply(transfer, outFrame.libavjsTransfer); outFrames.push(outFrame); av_frame_unref(frame) } } inPackets.forEach(handlePacket); if (config.fin) handlePacket(null); outFrames.libavjsTransfer = transfer; return outFrames }; var ff_set_packet = Module.ff_set_packet = function (pkt, data) { if (data.length === 0) { av_packet_unref(pkt) } else { var size = AVPacket_size(pkt); if (size < data.length) { var ret = av_grow_packet(pkt, data.length - size); if (ret < 0) throw new Error("Error growing packet: " + ff_error(ret)) } else if (size > data.length) av_shrink_packet(pkt, data.length) } var ptr = AVPacket_data(pkt); Module.HEAPU8.set(data, ptr) }; var ff_init_muxer = Module.ff_init_muxer = function (opts, streamCtxs) { var oformat = opts.oformat ? opts.oformat : 0; var format_name = opts.format_name ? opts.format_name : null; var filename = opts.filename ? opts.filename : null; var oc = avformat_alloc_output_context2_js(oformat, format_name, filename); if (oc === 0) throw new Error("Failed to allocate output context"); var fmt = AVFormatContext_oformat(oc); var sts = []; streamCtxs.forEach(function (ctx) { var st = avformat_new_stream(oc, 0); if (st === 0) throw new Error("Could not allocate stream"); sts.push(st); var codecpar = AVStream_codecpar(st); var ret; if (opts.codecpars) { ret = avcodec_parameters_copy(codecpar, ctx[0]); AVCodecParameters_codec_tag_s(codecpar, 0) } else { ret = avcodec_parameters_from_context(codecpar, ctx[0]) } if (ret < 0) throw new Error("Could not copy the stream parameters: " + ff_error(ret)); AVStream_time_base_s(st, ctx[1], ctx[2]) }); if (opts.device) FS.mkdev(opts.filename, 511, writerDev); var pb = null; if (opts.open) { pb = avio_open2_js(opts.filename, 2, 0, 0); if (pb === 0) throw new Error("Could not open file"); AVFormatContext_pb_s(oc, pb) } return [oc, fmt, pb, sts] }; var ff_free_muxer = Module.ff_free_muxer = function (oc, pb) { avformat_free_context(oc); if (pb) avio_close(pb) }; function ff_init_demuxer_file(filename, fmt) { var fmt_ctx; return avformat_open_input_js(filename, fmt ? fmt : null, null).then(function (ret) { fmt_ctx = ret; if (fmt_ctx === 0) throw new Error("Could not open source file"); return avformat_find_stream_info(fmt_ctx, 0) }).then(function () { var nb_streams = AVFormatContext_nb_streams(fmt_ctx); var streams = []; for (var i = 0; i < nb_streams; i++) { var inStream = AVFormatContext_streams_a(fmt_ctx, i); var outStream = { ptr: inStream, index: i }; var codecpar = AVStream_codecpar(inStream); outStream.codecpar = codecpar; outStream.codec_type = AVCodecParameters_codec_type(codecpar); outStream.codec_id = AVCodecParameters_codec_id(codecpar); outStream.time_base_num = AVStream_time_base_num(inStream); outStream.time_base_den = AVStream_time_base_den(inStream); outStream.duration_time_base = AVStream_duration(inStream) + AVStream_durationhi(inStream) * 4294967296; outStream.duration = outStream.duration_time_base * outStream.time_base_num / outStream.time_base_den; streams.push(outStream) } return [fmt_ctx, streams] }) } Module.ff_init_demuxer_file = function () { var args = arguments; return serially(function () { return ff_init_demuxer_file.apply(void 0, args) }) }; var ff_write_multi = Module.ff_write_multi = function (oc, pkt, inPackets, interleave) { var step = av_interleaved_write_frame; if (interleave === false) step = av_write_frame; var tbs = {}; inPackets.forEach(function (inPacket) { var ret = av_packet_make_writable(pkt); if (ret < 0) throw new Error("Error making packet writable: " + ff_error(ret)); ff_copyin_packet(pkt, inPacket); var sti = inPacket.stream_index || 0; var iptbNum, iptbDen; if (typeof inPacket === "number") { iptbNum = AVPacket_time_base_num(pkt); iptbDen = AVPacket_time_base_den(pkt) } else { iptbNum = inPacket.time_base_num; iptbDen = inPacket.time_base_den } if (iptbNum) { var tb = tbs[sti]; if (!tb) { var str = AVFormatContext_streams_a(oc, sti); tb = tbs[sti] = [AVStream_time_base_num(str), AVStream_time_base_den(str)] } if (tb[0]) { av_packet_rescale_ts_js(pkt, iptbNum, iptbDen, tb[0], tb[1]); AVPacket_time_base_s(pkt, tb[0], tb[1]) } } step(oc, pkt); av_packet_unref(pkt) }); av_packet_unref(pkt) }; function ff_read_frame_multi(fmt_ctx, pkt, opts) { var sz = 0; var outPackets = {}; var tbs = {}; if (typeof opts === "number") opts = { limit: opts }; if (typeof opts === "undefined") opts = {}; var unify = !!opts.unify; var copyoutPacket = ff_copyout_packet; if (opts.copyoutPacket) copyoutPacket = ff_copyout_packet_versions[opts.copyoutPacket]; function step() { return av_read_frame(fmt_ctx, pkt).then(function (ret) { if (ret < 0) return [ret, outPackets]; var packet = copyoutPacket(pkt); var stri = AVPacket_stream_index(pkt); var ptbNum, ptbDen; if (typeof packet === "number") { ptbNum = AVPacket_time_base_num(packet); ptbDen = AVPacket_time_base_den(packet) } else { ptbNum = packet.time_base_num; ptbDen = packet.time_base_den } if (!ptbNum) { var tb = tbs[stri]; if (!tb) { var str = AVFormatContext_streams_a(fmt_ctx, stri); tb = tbs[stri] = [AVStream_time_base_num(str), AVStream_time_base_den(str)] } if (typeof packet === "number") { AVPacket_time_base_s(packet, tb[0], tb[1]) } else { packet.time_base_num = tb[0]; packet.time_base_den = tb[1] } } var idx = unify ? 0 : stri; if (!(idx in outPackets)) outPackets[idx] = []; outPackets[idx].push(packet); sz += AVPacket_size(pkt); av_packet_unref(pkt); if (opts.limit && sz >= opts.limit) return [-6, outPackets]; return Promise.all([]).then(step) }) } return step() } Module.ff_read_frame_multi = function () { var args = arguments; return serially(function () { return ff_read_frame_multi.apply(void 0, args) }) }; Module.ff_read_multi = function (fmt_ctx, pkt, devfile, opts) { console.log("[libav.js] ff_read_multi is deprecated. Use ff_read_frame_multi."); return Module.ff_read_frame_multi(fmt_ctx, pkt, opts) }; var ff_init_filter_graph = Module.ff_init_filter_graph = function (filters_descr, input, output) { var buffersrc, abuffersrc, buffersink, abuffersink, filter_graph, tmp_src_ctx, tmp_sink_ctx, src_ctxs, sink_ctxs, io_outputs, io_inputs, int32s; var instr, outstr; var multiple_inputs = !!input.length; if (!multiple_inputs) input = [input]; var multiple_outputs = !!output.length; if (!multiple_outputs) output = [output]; src_ctxs = []; sink_ctxs = []; try { buffersrc = avfilter_get_by_name("buffer"); abuffersrc = avfilter_get_by_name("abuffer"); buffersink = avfilter_get_by_name("buffersink"); abuffersink = avfilter_get_by_name("abuffersink"); filter_graph = avfilter_graph_alloc(); if (filter_graph === 0) throw new Error("Failed to allocate filter graph"); io_outputs = 0; var ii = 0; input.forEach(function (input) { var next_io_outputs = avfilter_inout_alloc(); if (next_io_outputs === 0) throw new Error("Failed to allocate outputs"); AVFilterInOut_next_s(next_io_outputs, io_outputs); io_outputs = next_io_outputs; var nm = "in" + (multiple_inputs ? ii : ""); if (input.type === 0) { if (buffersrc === 0) throw new Error("Failed to load buffer filter"); var frame_rate = input.frame_rate; var time_base = input.time_base; if (typeof frame_rate === "undefined") frame_rate = 30; if (typeof time_base === "undefined") time_base = [1, frame_rate]; tmp_src_ctx = avfilter_graph_create_filter_js(buffersrc, nm, "time_base=" + time_base[0] + "/" + time_base[1] + ":frame_rate=" + frame_rate + ":pix_fmt=" + (input.pix_fmt ? input.pix_fmt : 0) + ":width=" + (input.width ? input.width : 640) + ":height=" + (input.height ? input.height : 360), null, filter_graph) } else { if (abuffersrc === 0) throw new Error("Failed to load abuffer filter"); var sample_rate = input.sample_rate; var time_base = input.time_base; if (typeof sample_rate === "undefined") sample_rate = 48e3; if (typeof time_base === "undefined") time_base = [1, sample_rate]; tmp_src_ctx = avfilter_graph_create_filter_js(abuffersrc, nm, "time_base=" + time_base[0] + "/" + time_base[1] + ":sample_rate=" + sample_rate + ":sample_fmt=" + (input.sample_fmt ? input.sample_fmt : 3) + ":channel_layout=0x" + (input.channel_layout ? input.channel_layout : 4).toString(16), null, filter_graph) } if (tmp_src_ctx === 0) throw new Error("Cannot create buffer source"); src_ctxs.push(tmp_src_ctx); instr = av_strdup(nm); if (instr === 0) throw new Error("Failed to allocate output"); AVFilterInOut_name_s(io_outputs, instr); instr = 0; AVFilterInOut_filter_ctx_s(io_outputs, tmp_src_ctx); tmp_src_ctx = 0; AVFilterInOut_pad_idx_s(io_outputs, 0); ii++ }); io_inputs = 0; var oi = 0; output.forEach(function (output) { var next_io_inputs = avfilter_inout_alloc(); if (next_io_inputs === 0) throw new Error("Failed to allocate inputs"); AVFilterInOut_next_s(next_io_inputs, io_inputs); io_inputs = next_io_inputs; var nm = "out" + (multiple_outputs ? oi : ""); if (output.type === 0) { if (buffersink === 0) throw new Error("Failed to load buffersink filter"); tmp_sink_ctx = avfilter_graph_create_filter_js(buffersink, nm, null, null, filter_graph) } else { tmp_sink_ctx = avfilter_graph_create_filter_js(abuffersink, nm, null, null, filter_graph) } if (tmp_sink_ctx === 0) throw new Error("Cannot create buffer sink"); sink_ctxs.push(tmp_sink_ctx); if (output.type === 0) { int32s = ff_malloc_int32_list([output.pix_fmt ? output.pix_fmt : 0, -1]); if (int32s === 0) throw new Error("Failed to transfer parameters"); if (av_opt_set_int_list_js(tmp_sink_ctx, "pix_fmts", 4, int32s, -1, 1) < 0) { throw new Error("Failed to set filter parameters") } free(int32s); int32s = 0 } else { int32s = ff_malloc_int32_list([output.sample_fmt ? output.sample_fmt : 3, -1, output.sample_rate ? output.sample_rate : 48e3, -1]); if (int32s === 0) throw new Error("Failed to transfer parameters"); var ch_layout = output.channel_layout ? output.channel_layout : 4; var ch_layout_i64 = [~~ch_layout, Math.floor(ch_layout / 4294967296)]; if (av_opt_set_int_list_js(tmp_sink_ctx, "sample_fmts", 4, int32s, -1, 1) < 0 || ff_buffersink_set_ch_layout(tmp_sink_ctx, ch_layout_i64[0], ch_layout_i64[1]) < 0 || av_opt_set_int_list_js(tmp_sink_ctx, "sample_rates", 4, int32s + 8, -1, 1) < 0) { throw new Error("Failed to set filter parameters") } free(int32s); int32s = 0 } outstr = av_strdup(nm); if (outstr === 0) throw new Error("Failed to transfer parameters"); AVFilterInOut_name_s(io_inputs, outstr); outstr = 0; AVFilterInOut_filter_ctx_s(io_inputs, tmp_sink_ctx); tmp_sink_ctx = 0; AVFilterInOut_pad_idx_s(io_inputs, 0); oi++ }); var ret = avfilter_graph_parse(filter_graph, filters_descr, io_inputs, io_outputs, 0); if (ret < 0) throw new Error("Failed to initialize filters: " + ff_error(ret)); io_inputs = io_outputs = 0; var oi = 0; output.forEach(function (output) { if (output.frame_size) av_buffersink_set_frame_size(sink_ctxs[oi], output.frame_size); oi++ }); ret = avfilter_graph_config(filter_graph, 0); if (ret < 0) throw new Error("Failed to configure filter graph: " + ff_error(ret)) } catch (ex) { if (io_outputs) avfilter_inout_free(io_outputs); if (io_inputs) avfilter_inout_free(io_inputs); if (filter_graph) avfilter_graph_free(filter_graph); if (tmp_src_ctx) avfilter_free(tmp_src_ctx); if (tmp_sink_ctx) avfilter_free(tmp_sink_ctx); if (int32s) free(int32s); if (instr) free(instr); if (outstr) free(outstr); throw ex } return [filter_graph, multiple_inputs ? src_ctxs : src_ctxs[0], multiple_outputs ? sink_ctxs : sink_ctxs[0]] }; var ff_filter_multi = Module.ff_filter_multi = function (srcs, buffersink_ctx, framePtr, inFrames, config) { var outFrames = []; var transfer = []; var tbNum = -1, tbDen = -1; if (!srcs.length) { srcs = [srcs]; inFrames = [inFrames]; config = [config] } config = config.map(function (config) { if (config === true) return { fin: true }; return config || {} }); var max = inFrames.map(function (srcFrames) { return srcFrames.length }).reduce(function (a, b) { return Math.max(a, b) }); function handleFrame(buffersrc_ctx, inFrame, copyoutFrame) { if (inFrame !== null) ff_copyin_frame(framePtr, inFrame); var ret = av_buffersrc_add_frame_flags(buffersrc_ctx, inFrame ? framePtr : 0, 8); if (ret < 0) throw new Error("Error while feeding the audio filtergraph: " + ff_error(ret)); av_frame_unref(framePtr); while (true) { ret = av_buffersink_get_frame(buffersink_ctx, framePtr); if (ret === -6 || ret === -541478725) break; if (ret < 0) throw new Error("Error while receiving a frame from the filtergraph: " + ff_error(ret)); if (tbNum < 0) { tbNum = av_buffersink_get_time_base_num(buffersink_ctx); tbDen = av_buffersink_get_time_base_den(buffersink_ctx) } var outFrame = copyoutFrame(framePtr); if (tbNum) { if (typeof outFrame === "number") { if (!AVFrame_time_base_num(outFrame)) AVFrame_time_base_s(outFrame, tbNum, tbDen) } else if (outFrame && !outFrame.time_base_num) { outFrame.time_base_num = tbNum; outFrame.time_base_den = tbDen } } if (outFrame && outFrame.libavjsTransfer && outFrame.libavjsTransfer.length) transfer.push.apply(transfer, outFrame.libavjsTransfer); outFrames.push(outFrame); av_frame_unref(framePtr) } } var copyoutFrames = []; for (var ti = 0; ti < inFrames.length; ti++)(function (ti) { var copyoutFrameO = ff_copyout_frame; if (config[ti].copyoutFrame) copyoutFrameO = ff_copyout_frame_versions[config[ti].copyoutFrame]; var copyoutFrame = function (ptr) { var ret = copyoutFrameO(ptr); if (!ret.time_base_num) { ret.time_base_num = tbNum; ret.time_base_den = tbDen } return ret }; if (config[ti].copyoutFrame === "ptr") { copyoutFrame = function (ptr) { var ret = ff_copyout_frame_ptr(ptr); if (!AVFrame_time_base_num(ret)) AVFrame_time_base_s(ret, tbNum, tbDen); return ret } } copyoutFrames.push(copyoutFrame) })(ti); for (var fi = 0; fi <= max; fi++) { for (var ti = 0; ti < inFrames.length; ti++) { var inFrame = inFrames[ti][fi]; if (inFrame) handleFrame(srcs[ti], inFrame, copyoutFrames[ti]); else if (config[ti].fin) handleFrame(srcs[ti], null, copyoutFrames[ti]) } } outFrames.libavjsTransfer = transfer; return outFrames }; var ff_decode_filter_multi = Module.ff_decode_filter_multi = function (ctx, buffersrc_ctx, buffersink_ctx, pkt, frame, inPackets, config) { if (typeof config === "boolean") { config = { fin: config } } else { config = config || {} } var decodedFrames = ff_decode_multi(ctx, pkt, frame, inPackets, { fin: !!config.fin, ignoreErrors: !!config.ignoreErrors, copyoutFrame: "ptr" }); return ff_filter_multi(buffersrc_ctx, buffersink_ctx, frame, decodedFrames, { fin: !!config.fin, copyoutFrame: config.copyoutFrame || "default" }) }; var ff_copyout_frame = Module.ff_copyout_frame = function (frame) { var nb_samples = AVFrame_nb_samples(frame); if (nb_samples === 0) { var width = AVFrame_width(frame); if (width) return ff_copyout_frame_video_width(frame, width) } var channels = AVFrame_channels(frame); var format = AVFrame_format(frame); var transfer = []; var outFrame = { data: null, libavjsTransfer: transfer, channel_layout: AVFrame_channel_layout(frame), channels, format, nb_samples, pts: AVFrame_pts(frame), ptshi: AVFrame_ptshi(frame), time_base_num: AVFrame_time_base_num(frame), time_base_den: AVFrame_time_base_den(frame), sample_rate: AVFrame_sample_rate(frame) }; if (format >= 5) { var data = []; for (var ci = 0; ci < channels; ci++) { var inData = AVFrame_data_a(frame, ci); var outData = null; switch (format) { case 5: outData = copyout_u8(inData, nb_samples); break; case 6: outData = copyout_s16(inData, nb_samples); break; case 7: outData = copyout_s32(inData, nb_samples); break; case 8: outData = copyout_f32(inData, nb_samples); break }if (outData) { data.push(outData); transfer.push(outData.buffer) } } outFrame.data = data } else { var ct = channels * nb_samples; var inData = AVFrame_data_a(frame, 0); var outData = null; switch (format) { case 0: outData = copyout_u8(inData, ct); break; case 1: outData = copyout_s16(inData, ct); break; case 2: outData = copyout_s32(inData, ct); break; case 3: outData = copyout_f32(inData, ct); break }if (outData) { outFrame.data = outData; transfer.push(outData.buffer) } } return outFrame }; var ff_copyout_frame_video = Module.ff_copyout_frame_video = function (frame) { return ff_copyout_frame_video_width(frame, AVFrame_width(frame)) }; var ff_copyout_frame_video_width = Module.ff_copyout_frame_video = function (frame, width) { var height = AVFrame_height(frame); var format = AVFrame_format(frame); var desc = av_pix_fmt_desc_get(format); var log2ch = AVPixFmtDescriptor_log2_chroma_h(desc); var layout = []; var transfer = []; var outFrame = { data: null, layout, libavjsTransfer: transfer, width, height, crop: { top: AVFrame_crop_top(frame), bottom: AVFrame_crop_bottom(frame), left: AVFrame_crop_left(frame), right: AVFrame_crop_right(frame) }, format: AVFrame_format(frame), key_frame: AVFrame_key_frame(frame), pict_type: AVFrame_pict_type(frame), pts: AVFrame_pts(frame), ptshi: AVFrame_ptshi(frame), time_base_num: AVFrame_time_base_num(frame), time_base_den: AVFrame_time_base_den(frame), sample_aspect_ratio: [AVFrame_sample_aspect_ratio_num(frame), AVFrame_sample_aspect_ratio_den(frame)] }; var dataLo = 1 / 0; var dataHi = 0; for (var p = 0; p < 8; p++) { var linesize = AVFrame_linesize_a(frame, p); if (!linesize) break; var plane = AVFrame_data_a(frame, p); if (plane < dataLo) dataLo = plane; var h = height; if (p === 1 || p === 2) h >>= log2ch; plane += linesize * h; if (plane > dataHi) dataHi = plane } outFrame.data = Module.HEAPU8.slice(dataLo, dataHi); transfer.push(outFrame.data.buffer); for (var p = 0; p < 8; p++) { var linesize = AVFrame_linesize_a(frame, p); if (!linesize) break; var plane = AVFrame_data_a(frame, p); layout.push({ offset: plane - dataLo, stride: linesize }) } return outFrame }; var ff_frame_video_packed_size = Module.ff_frame_video_packed_size = function (frame) { var width = AVFrame_width(frame); var height = AVFrame_height(frame); var format = AVFrame_format(frame); var desc = av_pix_fmt_desc_get(format); var bpp = 1; if (!(AVPixFmtDescriptor_flags(desc) & 16)) bpp *= AVPixFmtDescriptor_nb_components(desc); var dataSz = 0; for (var i = 0; i < 8; i++) { var linesize = AVFrame_linesize_a(frame, i); if (!linesize) break; var w = width * bpp; var h = height; if (i === 1 || i === 2) { w >>= AVPixFmtDescriptor_log2_chroma_w(desc); h >>= AVPixFmtDescriptor_log2_chroma_h(desc) } dataSz += w * h } return dataSz }; function ff_copyout_frame_data_packed(data, layout, frame) { var width = AVFrame_width(frame); var height = AVFrame_height(frame); var format = AVFrame_format(frame); var desc = av_pix_fmt_desc_get(format); var bpp = 1; if (!(AVPixFmtDescriptor_flags(desc) & 16)) bpp *= AVPixFmtDescriptor_nb_components(desc); var dIdx = 0; for (var i = 0; i < 8; i++) { var linesize = AVFrame_linesize_a(frame, i); if (!linesize) break; var inData = AVFrame_data_a(frame, i); var w = width * bpp; var h = height; if (i === 1 || i === 2) { w >>= AVPixFmtDescriptor_log2_chroma_w(desc); h >>= AVPixFmtDescriptor_log2_chroma_h(desc) } layout.push({ offset: dIdx, stride: w }); for (var y = 0; y < h; y++) { var line = inData + y * linesize; data.set(Module.HEAPU8.subarray(line, line + w), dIdx); dIdx += w } } } var ff_copyout_frame_video_packed = Module.ff_copyout_frame_video_packed = function (frame) { var data = new Uint8Array(ff_frame_video_packed_size(frame)); var layout = []; ff_copyout_frame_data_packed(data, layout, frame); var outFrame = { data, libavjsTransfer: [data.buffer], width: AVFrame_width(frame), height: AVFrame_height(frame), format: AVFrame_format(frame), key_frame: AVFrame_key_frame(frame), pict_type: AVFrame_pict_type(frame), pts: AVFrame_pts(frame), ptshi: AVFrame_ptshi(frame), time_base_num: AVFrame_time_base_num(frame), time_base_den: AVFrame_time_base_den(frame), sample_aspect_ratio: [AVFrame_sample_aspect_ratio_num(frame), AVFrame_sample_aspect_ratio_den(frame)] }; return outFrame }; var ff_copyout_frame_video_imagedata = Module.ff_copyout_frame_video_imagedata = function (frame) { var width = AVFrame_width(frame); var height = AVFrame_height(frame); var id = new ImageData(width, height); var layout = []; ff_copyout_frame_data_packed(id.data, layout, frame); id.libavjsTransfer = [id.data.buffer]; return id }; var ff_copyout_frame_ptr = Module.ff_copyout_frame_ptr = function (frame) { var ret = av_frame_clone(frame); if (!ret) throw new Error("Failed to allocate new frame"); return ret }; var ff_copyout_frame_versions = { default: ff_copyout_frame, video: ff_copyout_frame_video, video_packed: ff_copyout_frame_video_packed, ImageData: ff_copyout_frame_video_imagedata, ptr: ff_copyout_frame_ptr }; var ff_copyin_frame = Module.ff_copyin_frame = function (framePtr, frame) { if (typeof frame === "number") { av_frame_unref(framePtr); var ret = av_frame_ref(framePtr, frame); if (ret < 0) throw new Error("Failed to reference frame data: " + ff_error(ret)); av_frame_unref(frame); av_frame_free_js(frame); return } if (frame.width) return ff_copyin_frame_video(framePtr, frame); var format = frame.format; var channels = frame.channels; if (!channels) { var channel_layout = frame.channel_layout; channels = 0; while (channel_layout) { if (channel_layout & 1) channels++; channel_layout >>>= 1 } } ["channel_layout", "channels", "format", "pts", "ptshi", "sample_rate", "time_base_num", "time_base_den"].forEach(function (key) { if (key in frame) CAccessors["AVFrame_" + key + "_s"](framePtr, frame[key]) }); var nb_samples; if (format >= 5) { nb_samples = frame.data[0].length } else { nb_samples = frame.data.length / channels } AVFrame_nb_samples_s(framePtr, nb_samples); if (av_frame_make_writable(framePtr) < 0) { var ret = av_frame_get_buffer(framePtr, 0); if (ret < 0) throw new Error("Failed to allocate frame buffers: " + ff_error(ret)) } if (format >= 5) { for (var ci = 0; ci < channels; ci++) { var data = AVFrame_data_a(framePtr, ci); var inData = frame.data[ci]; switch (format) { case 5: copyin_u8(data, inData); break; case 6: copyin_s16(data, inData); break; case 7: copyin_s32(data, inData); break; case 8: copyin_f32(data, inData); break } } } else { var data = AVFrame_data_a(framePtr, 0); var inData = frame.data; switch (format) { case 0: copyin_u8(data, inData); break; case 1: copyin_s16(data, inData); break; case 2: copyin_s32(data, inData); break; case 3: copyin_f32(data, inData); break } } }; var ff_copyin_frame_video = Module.ff_copyin_frame_video = function (framePtr, frame) { ["format", "height", "key_frame", "pict_type", "pts", "ptshi", "width", "time_base_num", "time_base_den"].forEach(function (key) { if (key in frame) CAccessors["AVFrame_" + key + "_s"](framePtr, frame[key]) }); if ("sample_aspect_ratio" in frame) { AVFrame_sample_aspect_ratio_s(framePtr, frame.sample_aspect_ratio[0], frame.sample_aspect_ratio[1]) } var crop = frame.crop || { top: 0, bottom: 0, left: 0, right: 0 }; AVFrame_crop_top_s(framePtr, crop.top); AVFrame_crop_bottom_s(framePtr, crop.bottom); AVFrame_crop_left_s(framePtr, crop.left); AVFrame_crop_right_s(framePtr, crop.right); var desc = av_pix_fmt_desc_get(frame.format); var log2cw = AVPixFmtDescriptor_log2_chroma_w(desc); var log2ch = AVPixFmtDescriptor_log2_chroma_h(desc); if (av_frame_make_writable(framePtr) < 0) { var ret = av_frame_get_buffer(framePtr, 0); if (ret < 0) throw new Error("Failed to allocate frame buffers: " + ff_error(ret)) } var layout = frame.layout; if (!layout) { layout = []; var bpp = 1; if (!(AVPixFmtDescriptor_flags(desc) & 16)) bpp *= AVPixFmtDescriptor_nb_components(desc); var off = 0; for (var p = 0; p < 8; p++) { var linesize = AVFrame_linesize_a(framePtr, p); if (!linesize) break; var w = frame.width; var h = frame.height; if (p === 1 || p === 2) { w >>= log2cw; h >>= log2ch } layout.push({ offset: off, stride: w * bpp }); off += w * h * bpp } } for (var p = 0; p < layout.length; p++) { var lplane = layout[p]; var linesize = AVFrame_linesize_a(framePtr, p); var data = AVFrame_data_a(framePtr, p); var h = frame.height; if (p === 1 || p === 2) h >>= log2ch; var ioff = lplane.offset; var ooff = 0; var stride = Math.min(lplane.stride, linesize); for (var y = 0; y < h; y++) { copyin_u8(data + ooff, frame.data.subarray(ioff, ioff + stride)); ooff += linesize; ioff += lplane.stride } } }; var ff_copyout_packet = Module.ff_copyout_packet = function (pkt) { var data = AVPacket_data(pkt); var size = AVPacket_size(pkt); var data = copyout_u8(data, size); return { data, libavjsTransfer: [data.buffer], pts: AVPacket_pts(pkt), ptshi: AVPacket_ptshi(pkt), dts: AVPacket_dts(pkt), dtshi: AVPacket_dtshi(pkt), time_base_num: AVPacket_time_base_num(pkt), time_base_den: AVPacket_time_base_den(pkt), stream_index: AVPacket_stream_index(pkt), flags: AVPacket_flags(pkt), duration: AVPacket_duration(pkt), durationhi: AVPacket_durationhi(pkt), side_data: ff_copyout_side_data(pkt) } }; var ff_copyout_side_data = Module.ff_copyout_side_data = function (pkt) { var side_data = AVPacket_side_data(pkt); var side_data_elems = AVPacket_side_data_elems(pkt); if (!side_data) return null; var ret = []; for (var i = 0; i < side_data_elems; i++) { var data = AVPacketSideData_data(side_data, i); var size = AVPacketSideData_size(side_data, i); ret.push({ data: copyout_u8(data, size), type: AVPacketSideData_type(side_data, i) }) } return ret }; var ff_copyout_packet_ptr = Module.ff_copyout_packet_ptr = function (pkt) { var ret = av_packet_clone(pkt); if (!ret) throw new Error("Failed to clone packet"); return ret }; var ff_copyout_packet_versions = { default: ff_copyout_packet, ptr: ff_copyout_packet_ptr }; var ff_copyin_packet = Module.ff_copyin_packet = function (pktPtr, packet) { if (typeof packet === "number") { av_packet_unref(pktPtr); var res = av_packet_ref(pktPtr, packet); if (res < 0) throw new Error("Failed to reference packet: " + ff_error(res)); av_packet_unref(packet); av_packet_free_js(packet); return } ff_set_packet(pktPtr, packet.data);["dts", "dtshi", "duration", "durationhi", "flags", "side_data", "side_data_elems", "stream_index", "pts", "ptshi", "time_base_num", "time_base_den"].forEach(function (key) { if (key in packet) CAccessors["AVPacket_" + key + "_s"](pktPtr, packet[key]) }); if (packet.side_data) ff_copyin_side_data(pktPtr, packet.side_data) }; var ff_copyin_side_data = Module.ff_copyin_side_data = function (pktPtr, side_data) { side_data.forEach(function (elem) { var data = av_packet_new_side_data(pktPtr, elem.type, elem.data.length); if (data === 0) throw new Error("Failed to allocate side data!"); copyin_u8(data, elem.data) }) }; var ff_copyout_codecpar = Module.ff_copyout_codecpar = function (codecpar) { return { bit_rate: AVCodecParameters_bit_rate(codecpar), channel_layoutmask: AVCodecParameters_channel_layoutmask(codecpar), channels: AVCodecParameters_channels(codecpar), chroma_location: AVCodecParameters_chroma_location(codecpar), codec_id: AVCodecParameters_codec_id(codecpar), codec_tag: AVCodecParameters_codec_tag(codecpar), codec_type: AVCodecParameters_codec_type(codecpar), color_primaries: AVCodecParameters_color_primaries(codecpar), color_range: AVCodecParameters_color_range(codecpar), color_space: AVCodecParameters_color_space(codecpar), color_trc: AVCodecParameters_color_trc(codecpar), format: AVCodecParameters_format(codecpar), height: AVCodecParameters_height(codecpar), level: AVCodecParameters_level(codecpar), profile: AVCodecParameters_profile(codecpar), sample_rate: AVCodecParameters_sample_rate(codecpar), width: AVCodecParameters_width(codecpar), extradata: ff_copyout_codecpar_extradata(codecpar) } }; var ff_copyout_codecpar_extradata = Module.ff_copyout_codecpar_extradata = function (codecpar) { var extradata = AVCodecParameters_extradata(codecpar); var extradata_size = AVCodecParameters_extradata_size(codecpar); if (!extradata || !extradata_size) return null; return copyout_u8(extradata, extradata_size) }; var ff_copyin_codecpar = Module.ff_copyin_codecpar = function (codecparPtr, codecpar) { ["bit_rate", "channel_layoutmask", "channels", "chroma_location", "codec_id", "codec_tag", "codec_type", "color_primaries", "color_range", "color_space", "color_trc", "format", "height", "level", "profile", "sample_rate", "width"].forEach(function (key) { if (key in codecpar) CAccessors["AVCodecParameters_" + key + "_s"](codecparPtr, codecpar[key]) }); if (codecpar.extradata) ff_copyin_codecpar_extradata(codecparPtr, codecpar.extradata) }; var ff_copyin_codecpar_extradata = Module.ff_copyin_codecpar_extradata = function (codecparPtr, extradata) { var extradataPtr = malloc(extradata.length); copyin_u8(extradataPtr, extradata); AVCodecParameters_extradata_s(codecparPtr, extradataPtr); AVCodecParameters_extradata_size_s(codecparPtr, extradata.length) }; var ff_malloc_int32_list = Module.ff_malloc_int32_list = function (list) { var ptr = malloc(list.length * 4); if (ptr === 0) throw new Error("Failed to malloc"); var arr = new Uint32Array(Module.HEAPU8.buffer, ptr, list.length); for (var i = 0; i < list.length; i++)arr[i] = list[i]; return ptr }; var ff_malloc_int64_list = Module.ff_malloc_int64_list = function (list) { var ptr = malloc(list.length * 8); if (ptr === 0) throw new Error("Failed to malloc"); var arr = new Int32Array(Module.HEAPU8.buffer, ptr, list.length * 2); for (var i = 0; i < list.length; i++) { arr[i * 2] = list[i]; arr[i * 2 + 1] = list[i] < 0 ? -1 : 0 } return ptr }; var ff_malloc_string_array = Module.ff_malloc_string_array = function (arr) { var ptr = malloc((arr.length + 1) * 4); if (ptr === 0) throw new Error("Failed to malloc"); var inArr = new Uint32Array(Module.HEAPU8.buffer, ptr, arr.length + 1); var i; for (i = 0; i < arr.length; i++)inArr[i] = av_strdup(arr[i]); inArr[i] = 0; return ptr }; var ff_free_string_array = Module.ff_free_string_array = function (ptr) { var iPtr = ptr / 4; for (; ; iPtr++) { var elPtr = Module.HEAPU32[iPtr]; if (!elPtr) break; free(elPtr) } free(ptr) }; function convertArgs(argv0, args) { var ret = [argv0]; ret = ret.concat(Array.prototype.slice.call(args, 0)); for (var i = 0; i < ret.length; i++) { var arg = ret[i]; if (typeof arg !== "string") { if ("length" in arg) { ret.splice.apply(ret, [i, 1].concat(arg)) } else { ret[i] = "" + arg } } } return ret } function runMain(main, name, args) { args = convertArgs(name, args); var argv = ff_malloc_string_array(args); Module.fsThrownError = null; var ret = null; try { ret = main(args.length, argv) } catch (ex) { if (ex && ex.name === "ExitStatus") ret = ex.status; else if (ex === "unwind") ret = EXITSTATUS; else throw ex } function cleanup() { ff_free_string_array(argv) } if (ret && ret.then) { return ret.then(function (ret) { cleanup(); return ret }).catch(function (ex) { cleanup(); if (ex && ex.name === "ExitStatus") return Promise.resolve(ex.status); else if (ex === "unwind") return Promise.resolve(EXITSTATUS); else return Promise.reject(ex) }).then(function (ret) { if (Module.fsThrownError) { var thr = Module.fsThrownError; Module.fsThrownError = null; throw thr } return ret }) } else { cleanup(); if (Module.fsThrownError) { var thr = Module.fsThrownError; Module.fsThrownError = null; throw thr } return ret } } var ffmpeg = Module.ffmpeg = function () { return runMain(ffmpeg_main, "ffmpeg", arguments) }; var ffprobe = Module.ffprobe = function () { return runMain(ffprobe_main, "ffprobe", arguments) }; moduleRtn = readyPromise;


            return moduleRtn;
        }
    );
})();
if (typeof exports === 'object' && typeof module === 'object')
    module.exports = LibAVFactory;
else if (typeof define === 'function' && define['amd'])
    define([], () => LibAVFactory);
/*
 * Copyright (C) 2019-2024 Yahweasel
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

if (/* We're in a worker */
    typeof importScripts !== "undefined" &&
    /* We're not being loaded with noworker from the main code */
    typeof LibAV === "undefined" &&
    /* We're not being loaded as a thread */
    (
        (typeof self === "undefined" && typeof Module === "undefined") ||
        (typeof self !== "undefined" && self.name !== "em-pthread")
    )
) (function () {
    var libav;

    Promise.all([]).then(function () {
        /* We're the primary code for this worker. The host should ask us to
         * load immediately. */
        return new Promise(function (res, rej) {
            onmessage = function (e) {
                if (e && e.data && e.data.config) {
                    LibAVFactory({
                        wasmurl: e.data.config.wasmurl,
                        variant: e.data.config.variant
                    }).then(res).catch(rej);
                }
            };
        });

    }).then(function (lib) {
        libav = lib;

        // Now we're ready for normal messages
        onmessage = function (e) {
            var id = e.data[0];
            var fun = e.data[1];
            var args = e.data.slice(2);
            var ret = void 0;
            var succ = true;

            function reply() {
                var transfer = [];
                if (ret && ret.libavjsTransfer)
                    transfer = ret.libavjsTransfer
                try {
                    postMessage([id, fun, succ, ret], transfer);
                } catch (ex) {
                    try {
                        ret = JSON.parse(JSON.stringify(
                            ret, function (k, v) { return v; }
                        ));
                        postMessage([id, fun, succ, ret], transfer);
                    } catch (ex) {
                        postMessage([id, fun, succ, "" + ret]);
                    }
                }
            }

            try {
                ret = libav[fun].apply(libav, args);
            } catch (ex) {
                succ = false;
                ret = ex;
            }
            if (succ && ret && ret.then) {
                // Let the promise resolve
                ret.then(function (res) {
                    ret = res;
                }).catch(function (ex) {
                    succ = false;
                    ret = ex;
                }).then(reply);

            } else reply();
        };

        libav.onwrite = function (name, pos, buf) {
            /* We have to buf.slice(0) so we don't duplicate the entire heap just
             * to get one part of it in postMessage */
            buf = buf.slice(0);
            postMessage(["onwrite", "onwrite", true, [name, pos, buf]], [buf.buffer]);
        };

        libav.onread = function (name, pos, len) {
            postMessage(["onread", "onread", true, [name, pos, len]]);
        };

        libav.onblockread = function (name, pos, len) {
            postMessage(["onblockread", "onblockread", true, [name, pos, len]]);
        };

        postMessage(["onready", "onready", true, null]);

    }).catch(function (ex) {
        console.log("Loading LibAV failed\n" + ex + "\n" + ex.stack);
    });
})();
