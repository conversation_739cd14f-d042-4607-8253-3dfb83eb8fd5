<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <title>微信SDK调试</title>
        <link href="sdk/font-awesome.6.0.0.css" rel="stylesheet" />
        <link rel="stylesheet" href="sdk/weui.min.2.5.16.css" />
        <link rel="stylesheet" href="css/main.css" />
        <script src="sdk/tailwindcss.min.3.4.16.js"></script>
        <script type="text/javascript" src="sdk/weui.min.1.2.21.js"></script>
        <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
        <script src="sdk/vconsole.min.3.15.1.js"></script>
        <script>
            new VConsole();
        </script>
    </head>
    <body class="bg-gray-50 min-h-screen flex flex-col">
        <div class="h-12 bg-white"></div>

        <div class="flex-1 flex flex-col items-center justify-center px-4 py-8">
            <div class="w-20 h-20 bg-white rounded-full shadow-lg flex items-center justify-center mb-6">
                <i class="fa fa-weixin text-4xl text-[#07C160]"></i>
            </div>

            <h1 class="text-2xl font-bold text-gray-800 mb-2">欢迎登录</h1>
            <p class="text-gray-500 text-sm mb-8">使用微信账号快捷登录</p>

            <a
                href="#"
                id="wxCloseWindow"
                class="w-full max-w-sm bg-[#07C160] text-white py-3.5 px-4 rounded-lg shadow-md btn-hover flex items-center justify-center mb-6"
            >
                <i class="fa fa-weixin text-xl mr-2"></i>
                <span class="font-medium">关闭微信页面</span>
            </a>

            <a
                href="#"
                id="wxGetLocation"
                class="w-full max-w-sm bg-[#07C160] text-white py-3.5 px-4 rounded-lg shadow-md btn-hover flex items-center justify-center mb-6"
            >
                <i class="fa fa-weixin text-xl mr-2"></i>
                <span class="font-medium">获取地理位置</span>
            </a>

            <div class="text-xs text-gray-400 text-center">
                登录即表示同意
                <a href="#" class="text-[#07C160]">《用户协议》</a>
                和
                <a href="#" class="text-[#07C160]">《隐私政策》</a>
            </div>
        </div>

        <div class="text-center text-gray-400 text-xs py-4">© 2024 版权所有</div>

        <script src="js/config.20250814_1753.js"></script>
        <script src="js/utils.20250814_1753.js"></script>
        <script src="js/wx-sdk-handler.20250814_1753.js"></script>
        <script src="js/wxDebug.20250814_1753.js"></script>
    </body>
</html>