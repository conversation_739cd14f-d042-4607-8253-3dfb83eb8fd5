# 云手机SDK集成部署指南

## 部署前检查

### 1. 文件完整性检查
确保以下文件存在且完整：

**核心文件：**
- `index.html` - 主页面（已集成SDK切换功能）
- `js/index.js` - 主逻辑文件（已集成两种SDK支持）
- `js/config.js` - 配置文件（已添加LD SDK配置）

**LD SDK文件：**
- `ld/LDSDK.min.js` - LD SDK主文件
- `ld/libav-*******-0a1c8f7-zzh.js` - LibAV文件
- `ld/libav-*******-0a1c8f7-zzh.wasm.js` - WASM JS文件
- `ld/libav-*******-0a1c8f7-zzh.wasm.wasm` - WASM二进制文件

**JH SDK文件：**
- `sdk/JHSDK.min.3.21.6.js` - JH SDK主文件
- `lib/` 目录及其内容 - JH SDK依赖文件

### 2. 测试文件
- `test_integration.html` - 集成测试页面
- `SDK_INTEGRATION_README.md` - 功能说明文档

## 配置调整

### 1. 生产环境配置
编辑 `js/config.js` 文件：

```javascript
// 生产环境配置示例
window.API_URL_BASE_VDI = "https://your-production-vdi.domain.com";
window.API_URL_BASE_VG = "https://your-production-vg.domain.com";
window.WEBSOCKET_API_URL = "wss://your-production-ws.domain.com/decs-cvgs/wsforaiactl";

// LD SDK 配置
window.LD_SDK_CONFIG = {
    // 生产环境建议设置为 false，使用动态构建的地址
    USE_TEST_URL: false,
    // 测试地址（仅在 USE_TEST_URL 为 true 时使用）
    TEST_URL: 'wss://your-test-server.domain.com/decs-cvgs/wsforcph-test?ip=x.x.x.x&port=xxxx',
    // libav文件路径
    LIBAV_URL: './ld/libav-*******-0a1c8f7-zzh.js'
};
```

### 2. 微信公众号配置
确保 `WX_MP_APPID` 设置为正确的微信公众号AppID。

## 部署步骤

### 1. 文件上传
将所有文件上传到Web服务器，保持目录结构不变。

### 2. 权限设置
确保Web服务器对以下文件有读取权限：
- 所有 `.html` 文件
- 所有 `.js` 文件
- 所有 `.css` 文件
- `ld/` 目录下的所有文件
- `sdk/` 目录下的所有文件
- `lib/` 目录下的所有文件

### 3. HTTPS配置
**重要：** 由于WebRTC和某些浏览器API的安全要求，建议在HTTPS环境下部署。

### 4. CORS配置
如果API服务器与前端不在同一域名下，需要配置CORS：
```
Access-Control-Allow-Origin: https://your-frontend-domain.com
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

## 测试验证

### 1. 基础功能测试
1. 访问 `test_integration.html`
2. 检查SDK加载状态
3. 验证配置是否正确

### 2. 集成功能测试
1. 访问 `index.html`
2. 验证页面右上角显示SDK切换按钮
3. 测试JH SDK连接功能
4. 测试LD SDK切换功能
5. 验证触摸事件（移动端）

### 3. 兼容性测试
在以下环境中测试：
- Chrome（桌面版和移动版）
- Safari（桌面版和移动版）
- Firefox（桌面版）
- 微信内置浏览器

## 故障排除

### 1. SDK加载失败
**症状：** 控制台显示SDK未定义错误
**解决方案：**
- 检查文件路径是否正确
- 确认文件是否存在且可访问
- 检查网络连接

### 2. LD SDK连接失败
**症状：** LD SDK初始化或连接报错
**解决方案：**
- 检查WebSocket地址是否正确
- 确认服务器支持WebSocket连接
- 检查防火墙设置

### 3. 切换功能异常
**症状：** 点击切换按钮无响应或报错
**解决方案：**
- 检查浏览器控制台错误信息
- 确认两种SDK都已正确加载
- 检查事件监听器是否正确绑定

### 4. 移动端触摸异常
**症状：** 移动端无法正常操作云手机
**解决方案：**
- 检查触摸事件监听器
- 确认容器元素ID正确
- 验证坐标计算逻辑

## 性能优化

### 1. 资源加载优化
- 考虑使用CDN加速SDK文件加载
- 启用Gzip压缩
- 设置适当的缓存策略

### 2. 连接优化
- 实现连接池管理
- 添加自动重连机制
- 优化心跳检测

### 3. 内存管理
- 确保SDK切换时正确清理资源
- 监控内存使用情况
- 实现垃圾回收优化

## 监控和日志

### 1. 错误监控
建议集成错误监控服务，监控：
- SDK初始化失败
- 连接异常
- 切换功能异常

### 2. 性能监控
监控关键指标：
- 页面加载时间
- SDK初始化时间
- 连接建立时间
- 首帧渲染时间

### 3. 用户行为分析
跟踪用户使用情况：
- SDK使用偏好
- 切换频率
- 错误发生率

## 维护建议

### 1. 定期更新
- 关注SDK版本更新
- 及时修复安全漏洞
- 优化用户体验

### 2. 备份策略
- 定期备份配置文件
- 保留稳定版本
- 建立回滚机制

### 3. 文档维护
- 更新部署文档
- 记录配置变更
- 维护故障排除指南
