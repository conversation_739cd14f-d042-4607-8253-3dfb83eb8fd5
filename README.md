# AIA Frontend 项目文档

## 项目概述

AIA Frontend 是一个基于Web的云手机查看和微信登录集成的前端项目。该项目主要提供两个核心功能：
1. 通过Web界面查看和操作云手机
2. 微信快捷登录及SDK集成调试

## 功能特性

### 1. 云手机查看功能
- 基于[JHSDK](sdk/JHSDK.min.3.21.6.js)实现云手机远程访问
- 实时显示云手机屏幕内容
- 支持鼠标/触控操作云手机
- 支持剪切板同步和摄像头调用等高级功能

### 2. 微信快捷登录
- 微信授权登录流程集成
- 微信JS-SDK配置和初始化
- 用户身份验证和token管理
- 登录状态持久化存储

### 3. 微信SDK调试功能
- 微信JS-SDK接口调试
- 地理位置获取测试
- 微信页面关闭功能测试

### 4. 其他功能
- 用户身份信息缓存管理
- 网络请求处理(基于axios)
- 移动端适配和响应式布局
- 控制台调试工具集成(vConsole)

## 技术架构



### 项目结构
```
aia_front/
├── css/                 # 样式文件
├── js/                  # JavaScript逻辑文件
│   ├── config.js        # 全局配置
│   ├── h5login.js       # 微信登录逻辑
│   ├── index.js         # 云手机查看主逻辑
│   ├── utils.js         # 工具函数
│   ├── wx-sdk-handler.js# 微信SDK处理
│   └── wxDebug.js       # 微信调试功能
├── lib/                 # 核心库文件
│   ├── X264Encoder.*    # 视频编码器
│   └── libffmpeg_264_265.* # FFmpeg库
├── sdk/                 # 第三方SDK和库
│   ├── JHSDK.min.*.js   # 云手机SDK
│   ├── axios.min.*.js   # HTTP客户端
│   ├── weui.min.*.css/js# 微信UI库
│   └── 其他第三方库
├── index.html           # 云手机查看主页面
├── h5login.html         # 微信登录页面
├── wxDebug.html         # 微信SDK调试页面
└── 部署脚本...
```

## 架构图

```mermaid
graph TD
    A[用户浏览器] --> B[前端页面]
    B --> C[index.html - 云手机查看]
    B --> D[h5login.html - 微信登录]
    B --> E[wxDebug.html - SDK调试]
    
    C --> F[JHSDK]
    F --> G[云手机服务]
    
    D --> H[微信OAuth2.0]
    H --> I[后端认证服务]
    
    E --> J[微信JS-SDK]
    J --> H
    
    F --> K[libffmpeg]
    F --> L[X264Encoder]
    
    B --> M[Axios]
    M --> N[RESTful API]
    N --> I
    
    B --> O[LocalStorage]
    O --> P[用户凭证缓存]
```

## 核心流程

### 微信登录流程
1. 用户访问[h5login.html](h5login.html)
2. 点击微信登录按钮跳转到微信授权页面
3. 微信授权后回调到应用，携带code参数
4. 前端使用code换取用户身份凭证(token)
5. 凭证存储在LocalStorage中
6. 跳转到[index.html](index.html)云手机查看页面

### 云手机连接流程
1. [index.html](index.html)加载时检查LocalStorage中的用户凭证
2. 若无有效凭证则跳转到登录页面
3. 初始化JHSDK并建立云手机连接
4. 通过WebSocket或WebRTC接收云手机画面
5. 将画面渲染到页面指定容器中

## 组件库

-   https://github.com/Tencent/weui.js
-   https://weui.io/weui.js/
-   https://github.com/Tencent/weui.js/blob/master/docs/README.md

## 微信网页开发文档

-   [JS-SDK 说明文档](https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html)

## 待优化

1. **用户体验问题**:
   - 从index.html无登录态跳转到login.html登录后，无法再跳转回来

2. **功能扩展**:
   - 支持h5和后台创建ws长链接，作为前后台通用的消息通道，用于扩展不同业务
 
3. **业务扩展**:
   - 进入index.html页面，如果参数包含`action=change-address`则通过ws上报
   - 收到后台ws推送的通知，处理消息
   - 详见：https://doc.weixin.qq.com/doc/w3_AJUADwYwABwCNsXqufO4xSQufHkCb?scode=AIUAHwfKAAo865Dvpy


## 部署说明

1. **部署**:
- 项目为纯前端静态资源，可直接部署到任何支持静态文件托管的服务器上。需要注意：
- 打包： `deploy.sh` ; 本地生成 AI-ASSISTANT-FRONT.tar.gz 
- 发布：上传到服务器： 然后解包`tar_deploy.sh`即可
- 服务器目录： `ssh **************` , `sudo su - app`, `cd /data/app/vdi-ctest/pa`

## 体验方式
- https://doc.weixin.qq.com/doc/w3_ABkA9AaaAM4CN4KR7iQcGQ76xyFJ8?scode=AIUAHwfKAAodUVx08y