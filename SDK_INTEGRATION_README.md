# 云手机SDK集成说明

## 概述

本项目已成功集成了两种云手机SDK：
- **JH SDK**: 原有的云手机SDK
- **LD SDK**: 新集成的LD方式云手机SDK

两种SDK可以在同一个页面中并存，并支持动态切换。

## 文件修改说明

### 1. index.html 修改
- 添加了 LD SDK 的脚本引用：`<script src="./ld/LDSDK.min.js"></script>`
- 添加了SDK切换控制界面（右上角显示）
- 增加了相关的CSS样式

### 2. js/index.js 修改
- 添加了LD SDK相关的全局变量
- 重构了初始化逻辑，支持两种SDK的切换
- 添加了LD SDK的初始化函数 `initLDSdk()`
- 添加了LD SDK的连接函数 `connectLD()`
- 添加了触摸事件处理函数 `setupLDTouchEvents()`
- 添加了SDK切换功能相关函数

## 功能特性

### SDK切换
- 页面右上角显示小圆点切换按钮
- 默认使用LD SDK（显示"LD"文案）
- 点击圆点可以切换到JH SDK（显示"JH"文案）
- 不同SDK有不同的颜色标识（JH-蓝色，LD-绿色）
- 切换时会自动清理当前连接并重新初始化
- 按钮有悬停放大和点击缩小的动画效果

### LD SDK功能
- 支持WebSocket连接云手机
- 支持触摸事件处理（移动端）
- 支持屏幕分辨率切换
- 支持媒体流连接（原生协议）
- 支持剪贴板同步
- 支持连接状态监控

### 兼容性
- 保持原有JH SDK的所有功能不变
- 新增LD SDK功能作为可选项
- 两种SDK可以独立工作，互不干扰

## 使用方法

### 1. 基本使用
1. 打开 `index.html`
2. 页面默认使用LD SDK连接云手机（右上角显示绿色"LD"圆点）
3. 如需切换到JH SDK，点击右上角的圆点按钮（将变为蓝色"JH"）

### 2. 配置说明
- JH SDK配置保持不变，使用原有的配置参数
- LD SDK使用测试地址：`wss://testing-vgcph.zdgzc.com/decs-cvgs/wsforcph-test?ip=***********&port=10242`
- 实际部署时需要根据业务需求修改LD SDK的连接地址

### 3. 测试验证
- 打开 `test_integration.html` 进行SDK加载状态检查
- 在 `index.html` 中测试实际的切换功能

## 技术实现细节

### 初始化流程
1. 页面加载时检查用户认证信息
2. 根据当前SDK类型（默认JH）进行初始化
3. 建立WebSocket连接
4. 执行URL参数中的action请求

### 切换流程
1. 清理当前SDK的连接和资源
2. 更新全局SDK类型变量
3. 更新按钮状态显示
4. 清空容器内容
5. 重新执行初始化流程

### 错误处理
- SDK初始化失败时显示错误提示
- 连接失败时显示具体错误信息
- 切换过程中的异常会被捕获并记录

## 注意事项

1. **依赖文件**: 确保 `ld/LDSDK.min.js` 文件存在且可访问
2. **网络环境**: LD SDK需要WebSocket连接，确保网络环境支持
3. **移动端适配**: LD SDK的触摸事件已针对移动端进行优化
4. **资源清理**: 切换SDK时会自动清理资源，避免内存泄漏
5. **配置更新**: 生产环境需要更新LD SDK的连接地址

## 后续扩展

1. 可以添加更多的SDK配置选项
2. 可以实现SDK状态的持久化存储
3. 可以添加更详细的连接状态显示
4. 可以实现自动重连机制的优化

## 测试建议

1. 在不同浏览器中测试SDK切换功能
2. 在移动端测试触摸事件是否正常
3. 测试网络异常情况下的错误处理
4. 验证资源清理是否完整，避免内存泄漏
