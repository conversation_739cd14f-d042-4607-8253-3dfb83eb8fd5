<!DOCTYPE html>
<html>
<head>
    <title>SDK切换按钮样式预览</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-area {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            margin: 20px 0;
        }
        .sdk-switch {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid #07C160;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            transition: all 0.2s ease;
            user-select: none;
            touch-action: none;
        }
        .sdk-switch:hover {
            background: rgba(7, 193, 96, 0.3);
            transform: scale(1.2);
            border-width: 2px;
        }
        .sdk-switch:active {
            transform: scale(0.9);
        }
        .sdk-switch.dragging {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: none;
        }
        .sdk-switch.jh-active {
            border-color: #007bff;
            color: #007bff;
        }
        .sdk-switch.ld-active {
            border-color: #07C160;
            color: #07C160;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>SDK切换按钮样式预览</h1>
    
    <div class="preview-container">
        <h2>新版本按钮样式</h2>
        <div class="demo-area">
            <div id="sdkSwitchBtn" class="sdk-switch ld-active">
                LD
            </div>
        </div>
        
        <div class="controls">
            <button onclick="switchToJH()">切换到 JH SDK</button>
            <button onclick="switchToLD()">切换到 LD SDK</button>
        </div>
        
        <div class="info">
            <strong>当前状态：</strong>
            <span id="currentStatus">LD SDK 激活</span>
        </div>
    </div>

    <div class="preview-container">
        <h2>设计特性</h2>
        <ul class="feature-list">
            <li>超小圆点设计（32x32px），最小化屏幕占用</li>
            <li>支持长按拖动，可移动到任意位置</li>
            <li>显示当前SDK类型（JH/LD）</li>
            <li>不同SDK有不同的颜色标识</li>
            <li>悬停时有放大效果</li>
            <li>拖动时有阴影反馈</li>
            <li>智能点击/拖动识别</li>
            <li>半透明背景，不遮挡内容</li>
            <li>避免误触云手机操作</li>
        </ul>
    </div>

    <div class="preview-container">
        <h2>颜色方案</h2>
        <div style="display: flex; gap: 20px; align-items: center;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="sdk-switch jh-active" style="position: static; margin: 0;">JH</div>
                <span>JH SDK - 蓝色主题 (#007bff)</span>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="sdk-switch ld-active" style="position: static; margin: 0;">LD</div>
                <span>LD SDK - 绿色主题 (#07C160)</span>
            </div>
        </div>
    </div>

    <div class="preview-container">
        <h2>使用说明</h2>
        <div class="info">
            <p><strong>在实际页面中：</strong></p>
            <p>1. 按钮默认位于页面右上角（32x32px）</p>
            <p>2. 显示当前激活的SDK类型（JH/LD）</p>
            <p>3. 短按/点击可切换到另一种SDK</p>
            <p>4. 长按拖动可移动按钮位置</p>
            <p>5. 智能识别点击和拖动操作</p>
            <p>6. 切换时会自动清理当前连接并重新初始化</p>
        </div>
    </div>

    <script>
        const sdkSwitchBtn = document.getElementById('sdkSwitchBtn');
        const currentStatus = document.getElementById('currentStatus');
        let currentSdk = 'LD';

        function switchToJH() {
            currentSdk = 'JH';
            updateButtonState();
        }

        function switchToLD() {
            currentSdk = 'LD';
            updateButtonState();
        }

        function updateButtonState() {
            sdkSwitchBtn.classList.remove('jh-active', 'ld-active');
            
            if (currentSdk === 'JH') {
                sdkSwitchBtn.classList.add('jh-active');
                sdkSwitchBtn.textContent = 'JH';
                currentStatus.textContent = 'JH SDK 激活';
            } else {
                sdkSwitchBtn.classList.add('ld-active');
                sdkSwitchBtn.textContent = 'LD';
                currentStatus.textContent = 'LD SDK 激活';
            }
        }

        // 添加拖动功能
        let isDragging = false;
        let dragStartTime = 0;
        let startX = 0;
        let startY = 0;
        let initialLeft = 0;
        let initialTop = 0;

        function handleStart(e) {
            isDragging = false;
            dragStartTime = Date.now();

            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);

            startX = clientX;
            startY = clientY;

            const rect = sdkSwitchBtn.getBoundingClientRect();
            initialLeft = rect.left;
            initialTop = rect.top;

            e.preventDefault();
        }

        function handleMove(e) {
            const currentTime = Date.now();
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);

            const deltaX = clientX - startX;
            const deltaY = clientY - startY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (distance > 5 || currentTime - dragStartTime > 200) {
                if (!isDragging) {
                    isDragging = true;
                    sdkSwitchBtn.classList.add('dragging');
                }

                const demoArea = document.querySelector('.demo-area');
                const demoRect = demoArea.getBoundingClientRect();

                const newLeft = initialLeft + deltaX - demoRect.left;
                const newTop = initialTop + deltaY - demoRect.top;

                const maxLeft = demoArea.offsetWidth - sdkSwitchBtn.offsetWidth;
                const maxTop = demoArea.offsetHeight - sdkSwitchBtn.offsetHeight;

                const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
                const constrainedTop = Math.max(0, Math.min(newTop, maxTop));

                sdkSwitchBtn.style.left = constrainedLeft + 'px';
                sdkSwitchBtn.style.top = constrainedTop + 'px';
                sdkSwitchBtn.style.right = 'auto';

                e.preventDefault();
            }
        }

        function handleEnd(e) {
            sdkSwitchBtn.classList.remove('dragging');

            if (!isDragging) {
                currentSdk = currentSdk === 'JH' ? 'LD' : 'JH';
                updateButtonState();
            }

            isDragging = false;
            e.preventDefault();
        }

        // 鼠标事件
        sdkSwitchBtn.addEventListener('mousedown', handleStart);
        document.addEventListener('mousemove', handleMove);
        document.addEventListener('mouseup', handleEnd);

        // 触摸事件
        sdkSwitchBtn.addEventListener('touchstart', handleStart, { passive: false });
        document.addEventListener('touchmove', handleMove, { passive: false });
        document.addEventListener('touchend', handleEnd, { passive: false });
    </script>
</body>
</html>
