#
# 1. 构建生成 目标 tar 文件
# 2. ssh 目标机器后，解压到当前目录
#

BUILD_TEMP_DIR="dist"

rm -rf ${BUILD_TEMP_DIR}
mkdir -p ${BUILD_TEMP_DIR}
# mkdir -p ${BUILD_TEMP_DIR}/lib
# mkdir -p ${BUILD_TEMP_DIR}/sdk
# mkdir -p ${BUILD_TEMP_DIR}/webfonts

cp -R lib/ ${BUILD_TEMP_DIR}
cp -R sdk/ ${BUILD_TEMP_DIR}
cp -R webfonts/ ${BUILD_TEMP_DIR}
cp -R public/ ${BUILD_TEMP_DIR}
cp -R js/ ${BUILD_TEMP_DIR}
cp -R css/ ${BUILD_TEMP_DIR}
cp -R *.html ${BUILD_TEMP_DIR}
cp -R tar_deploy.sh ${BUILD_TEMP_DIR}


rm -rf ${BUILD_TEMP_DIR}/.DS_Store

# 为js目录下的每个js文件添加版本号
datetime=`date "+%Y%m%d_%H%M"`
JS_VERSION="${datetime}"

# 在dist目录中重命名js文件并添加版本号
for js_file in ${BUILD_TEMP_DIR}/js/*.js; do
    if [ -f "$js_file" ]; then
        filename=$(basename "$js_file" .js)
        mv "$js_file" "${BUILD_TEMP_DIR}/js/${filename}.${JS_VERSION}.js"
    fi
done

# 更新HTML文件中对js文件的引用
for html_file in ${BUILD_TEMP_DIR}/*.html; do
    if [ -f "$html_file" ]; then
        # 使用sed替换js引用，添加版本号
        sed -i.bak "s/src=\"js\/\([^\"]*\)\.js\"/src=\"js\/\1\.${JS_VERSION}.js\"/g" "$html_file"
        # 删除备份文件
        rm "${html_file}.bak"
    fi
done

rm -rf ${BUILD_TEMP_DIR}/.DS_Store

# PKG_NAME="AI-ASSISTANT-FRONT_${datetime}.tar.gz"
PKG_NAME="AI-ASSISTANT-FRONT.tar.gz"
tar --no-xattrs -czvf ${PKG_NAME} -C ${BUILD_TEMP_DIR} .
