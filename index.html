
<!DOCTYPE html>
<html>
    <head>
        <title>云手机查看</title>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
        <style>
            html,
            body {
                margin: 0;
                padding: 0;
                height: 100%;
            }
            .container {
                /*width: 100%;*/
                /*!*max-width: 100vw;*!*/
                /*height: 100%;*/
                /*background: black;*/
                position: absolute;
                display: block;
                height: 100vh;
                width: 100vw;
                margin: 0;
                padding: 0;
                top: 0;
                left: 0;
            }
            .sdk-switch {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border: 2px solid #07C160;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
                transition: all 0.3s ease;
                user-select: none;
            }
            .sdk-switch:hover {
                background: rgba(7, 193, 96, 0.2);
                transform: scale(1.1);
            }
            .sdk-switch:active {
                transform: scale(0.95);
            }
            .sdk-switch.jh-active {
                border-color: #007bff;
                color: #007bff;
            }
            .sdk-switch.ld-active {
                border-color: #07C160;
                color: #07C160;
            }
        </style>
        <link rel="stylesheet" href="sdk/weui.min.2.5.16.css" />
        <script type="text/javascript" src="sdk/JHSDK.min.3.21.6.js"></script>
        <script src="./ld/LDSDK.min.js"></script>
        <script src="sdk/vconsole.min.3.15.1.js"></script>
        <script src="sdk/axios.min.1.9.0.js"></script>
        <script type="text/javascript" src="sdk/weui.min.1.2.21.js"></script>
        <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>

        <script>
            new VConsole();
        </script>
    </head>
    <body onunload="closePage()">
        <div id="fullscreen-container" class="fullscreen-container">
            <!-- SDK切换控制 -->
            <div id="sdkSwitchBtn" class="sdk-switch ld-active">
                LD
            </div>

            <!-- 用来挂在云手机的节点 -->
            <div id="container" class="container"></div>

            <!-- 添加控制按钮 -->
            <div class="fixed bottom-4 left-0 right-0 flex justify-center gap-4" style="display: none">
                <button id="wxCloseWindow" class="bg-[#07C160] text-white px-4 py-2 rounded-lg shadow-md">
                    关闭微信页面
                </button>
                <button id="wxGetLocation" class="bg-[#07C160] text-white px-4 py-2 rounded-lg shadow-md">
                    获取地理位置
                </button>
            </div>
        </div>
        <script src="js/config.js"></script>
        <script src="js/utils.js"></script>
        <script src="js/wx-sdk-handler.js"></script>
        <script src="js/action.js"></script>
        <script src="js/ws-handler.js"></script>
        <script src="js/index.js"></script>
    </body>
</html>
