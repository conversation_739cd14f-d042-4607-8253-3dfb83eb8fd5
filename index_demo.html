<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" id='viewportMeta'
        content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no" />
    <title></title>

    <script src="./ld/LDSDK.min.js"></script>
    <script src="./sdk/vue.min.js"></script>
    <!-- <script src="../js/config.js"></script> -->

  
</head>
<style>
    * {
        padding: 0;
        margin: 0;
    }

    [v-cloak] {
        display: none;
    }

    html,
    body,
    #app {
        width: 100%;
        height: 100%;
    }
    #app{
        display: flex;
        flex-direction: column;

    }
    .title{
        background-color: #3A485B;
        width: 100%;
        height: 42px;
        font-size: 14px;
        box-sizing: border-box;
        padding: 12px 15px;
        line-height: 14px;
        overflow: hidden;
        color: #FAFAFA;
    }

    #vdiScreen{
        /* background-color: pink; */
        width: 100%;
        flex: 1;
        /* height: 100%; */

    }
</style>

<body>
    <div id="app" v-cloak>
        <div class="title">title模块</div>
        <div id="vdiScreen">
        </div>
    </div>

</body>
<script>
    console.log("begin")
// uid=uid-7426468f&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOiJ1aWQtNzQyNjQ2OGYiLCJleHAiOjE3NTU4NDY0NzEsIm5iZiI6MTc1NDk4MjQ3MSwiaWF0IjoxNzU0OTgyNDcxfQ.DfaNoFrtdF_ILGOOwau5NCXz4Bi9V3vY_Wx5oGafskI
    var app = new Vue({
        el: '#app',
        data() {
            return {
                width: 0,
                height: 0,
                // BASE_DISTANCE: 0,
                BASE_DISTANCE: 42,
                init_flag: false,
                uid: null,
                token: null,
                libavUrl: './libav-*******-0a1c8f7-zzh.js',
                isMobile: true,
                client: null,

                video_container_div: 'vdiScreen',
                video_input_div: 'vdiScreen',

                _connectlock: false,
                _reconnectTimer: null,


            }
        },
        created() {
            this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            this.width = document.body.clientWidth * 2
            this.height = (document.body.clientHeight - this.BASE_DISTANCE) * 2

            let uid = this.getURLParam('uid')
            let token = this.getURLParam('token')

            if (uid && token) {
                console.log('url 参数 uid  和 token:', uid, token);
                this.uid = uid
                this.token = token
                this.connect()
                return
            }

            uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID);
            token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN);
            if (uid && token) {
                console.log('本地缓存 uid  和 token:', uid, token);
                this.uid = uid
                this.token = token
                this.connect()
                return
            }
            // alert('未检测到 uid 和 token 参数，请检查')
        },
        mounted() {
            console.log('mounted');
        },
        methods: {
            async initSdk() {
                try {
                    const libavUrl = this.isMobile ? undefined : this.libavUrl
                    const init_res = await LDSDK.InitSdk(this.libavUrl)
                    this.init_flag = true
                    console.log('sdk初始化成功' + JSON.stringify(init_res));
                    LDSDK.GetMediaCapacity().then(res => {
                        console.log(JSON.stringify(res));
                    })
                } catch (err) {
                    console.log('sdk初始化失败' + JSON.stringify(err));
                    this.init_flag = false
                    this.onError(err)
                    // alert('sdk初始化失败' + JSON.stringify(err));
                }
            },

            async connect() {
                if (!this.init_flag) await this.initSdk()

                if (!this.client) {
                    console.log('连接cph服务');
                    // const device_url = `${window.WEBSOCKET_API_URL}?uid=${this.uid}&token=${this.token}`
                    const device_url = 'wss://testing-vgcph.zdgzc.com/decs-cvgs/wsforcph-test?ip=***********&port=10242'
                    this.client = LDSDK.CreateDevice(device_url, {
                        onScreenSize: (width, height) => {
                            console.log('demo屏幕分辨率' + width + 'x' + height);
                        },
                        onClipboard: (text) => {
                            console.log('demo剪贴板内容' + text);
                        },
                        onClose: (error) => {
                            console.log('demo连接关闭' + JSON.stringify(error));
                           
                            this.onClose()
                        }
                    });
                    this.client.debug.log_media = true
                    this.client.debug.log_device = true
                }

                const connect_res = await this.client.Connect()
                console.log('connect_res', connect_res);
                if (!connect_res.IsSuccess()) {
                    console.log('连接失败', connect_res.message);
                    this.onError(connect_res.message)
                    return
                }
                let login_info = new LDSDK.LDLoginInfo()
                login_info.uid = 'uid'
                login_info.token = 'token'
                login_info.app_key = 'app_key'
                login_info.client_type = LDSDK.LDDeviceType.CLIENT_TYPE_WEB

                const login_result = await this.client.Login(login_info)
                console.log('login_result', login_result);
                if (!login_result.IsSuccess()) {
                    console.log('登录失败', login_result.message)
                    this.onError('login error')
                    return
                }

                await this.$nextTick()

                const video_container = document.getElementById(this.video_container_div)
                // let width = window.screen.width;
                // let height = window.screen.height;
                let info = {
                    width: this.width,
                    height: this.height,
                    // 自动旋转视频方向
                    auto_rotate: false,
                    no_audio: true,//静音
                    quality: LDSDK.LDVideoQuality.kVQ1080p,
                    video_container: video_container,
                    force_wasm: false,
                    // host: this.webrtc_url,
                    onMediaFirstFrame: () => {
                        console.log('demo首帧加载完成');
                        this.onOpen()
                    },
                    onMediaSize: (w, h) => {
                        console.log('横屏变化', w, h)
                    },
                    onMediaRotation: (rotation) => {
                        console.log('旋转', rotation)
                    }
                }
                await this.switchScreenSize()

                this.calVideoDiv()

                // 通过原生协议连接媒体流
                await this.client.ConnectMediaNative(info)
                // 通过 WebRTC 协议连接媒体流
                // await this.client.ConnectMediaWebrtc(info)
                console.log('demo连接成功');
            },

            reconnect() {
                if (this._connectlock) return
                this._connectlock = true

                this._reconnectTimer && clearTimeout(this._reconnectTimer)
                this._reconnectTimer = setTimeout(() => {
                    this._connectlock = false
                    this.connect()
                }, 2 * 1000)
            },
            onOpen(){
                console.log('connect succ');
            },

            onClose(){
                console.log('connect close');
                console.log('start reconnect');
                this.reconnect()
            },
            onError(){
                alert('链接失败')

            },

            async switchScreenSize(w, h) {
                const targetWidth = w ?? this.width;
                const targetHeight = h ?? this.height;
                const switch_screen_size_res = await this.client.SwitchScreenSize(targetWidth, targetHeight)
                if (!switch_screen_size_res.IsSuccess()) {
                    console.log('切换云机屏幕分辨率失败', switch_screen_size_res.message)
                }
            },
            calVideoDiv() {
                if (this.isMobile) {
                    // 移动端设备下，将画布div事件传递给sdk
                    // 需要在nextTick之后再执行后面的内容
                    
                    this.phone_touch_event()
                    
                    return
                }

            },
            async phone_touch_event() {
                await this.$nextTick()


                const video_input_div = document.getElementById(this.video_input_div)
                if (!video_input_div) return

                // 因业务取证存在头部title占位，这里需要考虑到画布元素触摸事件的位置偏移量
                const divRect = video_input_div.getBoundingClientRect();

                const touchstartListener = (event) => {
                    console.log('touch start', this.client);
                    // 注意这里需要传递一次touch 一次TouchMove
                    this.client?.Touch(LDSDK.LDDeviceOperation.TOUCH_DOWN);
                    const touchArray = Array.from(event.changedTouches);
                    this.client?.TouchMove(touchArray.map((touch) => {
                        // 计算相对于 video_input_div 的坐标
                        const relativeX = touch.clientX - divRect.left;
                        const relativeY = touch.clientY - divRect.top;
                        return {
                            index: touch.identifier,
                            targetX: relativeX,
                            targetY: relativeY,
                            width: divRect.width,
                            height: divRect.height
                        };
                    }));
                };
                const touchmoveListener = (event) => {
                    console.log('touch move', this.client);
                    const touchArray = Array.from(event.changedTouches);
                    this.client?.TouchMove(touchArray.map((touch) => {
                        // 计算相对于 video_input_div 的坐标
                        const relativeX = touch.clientX - divRect.left;
                        const relativeY = touch.clientY - divRect.top;
                        return {
                            index: touch.identifier,
                            targetX: relativeX,
                            targetY: relativeY,
                            width: divRect.width,
                            height: divRect.height
                        };
                    }));
                };

                // 手指抬起
                const touchendListener = (event) => {
                    this.client?.Touch(LDSDK.LDDeviceOperation.TOUCH_UP);
                };

                // 触摸取消
                const touchcancelListener = (event) => {
                    console.log('ontouchcancel' + event.changedTouches.length)
                    this.client?.Touch(LDSDK.LDDeviceOperation.TOUCH_UP);
                };

                video_input_div.removeEventListener('touchstart', touchstartListener);
                video_input_div.removeEventListener('touchmove', touchmoveListener);
                video_input_div.removeEventListener('touchend', touchendListener);
                video_input_div.removeEventListener('touchcancel', touchcancelListener);

                video_input_div.addEventListener('touchstart', touchstartListener);
                video_input_div.addEventListener('touchmove', touchmoveListener);
                video_input_div.addEventListener('touchend', touchendListener);
                video_input_div.addEventListener('touchcancel', touchcancelListener);

            },
            getURLParam(key, url = window.location.href) {
                const urlObj = new URL(url)
                const searchParams = new URLSearchParams(urlObj.search)
                return searchParams.get(key) || null
            },
        }
    })
</script>

</html>