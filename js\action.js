// ----请求处理----
// 定义有效的action枚举
const REQUEST_ACTIONS = {
    CHANGE_ADDRESS: {
        action: 'change-address',
        handler: showLoadingPage
    }
}

// 执行及生成"Action请求报文"
function executeAndCreateActionRequest(actionType, argument={}) {
    // 执行action处理
    const requestAction = Object.values(REQUEST_ACTIONS).find(item => item.action === actionType);
    if (requestAction && typeof requestAction.handler === 'function') {
        requestAction.handler(argument);
    } else {
        console.error(`无效的请求action类型: ${actionType}`);
    }

    // 组装请求报文
    return assembleRequestMessage(actionType);
}

// 组装请求报文并返回String类型
function assembleRequestMessage(actionType, data = {}, version = "1.0") {
    return JSON.stringify({
        type: "onclick-event",
        arg: {
            btn: actionType,
            site: "",
            ext: data
        },
        ts: Date.now(),
        v: version
    });
}

// ----响应处理----
// 定义有效的action枚举
const RESPONSE_ACTIONS = {
    SHOW_ELEMENT: {
        action: 'show-element',
        handler: executeShowElement
    }
}

// 页面响应元素
const SHOW_ELEMENT_CONSTANTS = {
    PAGE_LOADING: {
        page: 'loading',
        text: '加载中...',
        handler: showLoadingPage
    },
    PAGE_CLOSE_LOADING: {
        page: 'close-loading',
        text: '关闭加载',
        handler: closeLoadingPage
    },
    PAGE_LOGIN_SUCC: {
        page: 'login-succ',
        text: '已登录{site}，正在帮你下单，请稍等～',
        handler: showPromptPage
    },
    PAGE_PAY_SUCC: {
        page: 'pay-succ',
        text: '已成功下单～',
        handler: showPromptPage
    },
    PAGE_CHANGE_ADDRESS_SUCC: {
        page: 'change-address-succ',
        text: '收货地址已修改成功 ✅',
        handler: showPromptPage
    },
    VERIFY_SUCC: {
        page: 'verify-succ',
        text: '验证成功～',
        handler: showPromptPage
    }
}

// 根据消息类型执行操作
function executeResponseAction(type, argument) {
    // 查找对应的常量对象
    const responseAction = Object.values(RESPONSE_ACTIONS).find(item => item.action === type);
    
    if (responseAction && typeof responseAction.handler === 'function') {
        // 调用对应的处理函数
        responseAction.handler(argument);
    } else {
        console.error(`无效的响应action类型: ${type}`);
    }
}

// 执行显示元素操作
function executeShowElement(argument) {
    const element = argument.page;
    console.log('展示page内容:', element);
    
    // 查找对应的常量对象
    const pageConstant = Object.values(SHOW_ELEMENT_CONSTANTS).find(item => item.page === element);
    
    if (pageConstant && typeof pageConstant.handler === 'function') {
        // 调用对应的处理函数
        pageConstant.handler(argument);
    } else {
        console.error(`无效的展示page: ${element}`);
    }
}

// 显示加载动画
function showLoadingPage() {
    // 检查是否已存在loading元素
    let loadingOverlay = document.getElementById('loading-overlay');
    
    if (!loadingOverlay) {
        // 创建全屏覆盖层
        loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: white;
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        `;
        
        // 创建旋转菊花元素容器
        const spinnerContainer = document.createElement('div');
        spinnerContainer.id = 'loading-spinner-container';
        spinnerContainer.style.cssText = `
            width: 50px;
            height: 50px;
            position: relative;
        `;
        
        // 创建旋转菊花的每个部分
        for (let i = 0; i < 12; i++) {
            const spinnerPart = document.createElement('div');
            spinnerPart.className = 'spinner-part';
            spinnerPart.style.cssText = `
                position: absolute;
                width: 6px;
                height: 6px;
                background-color: #3498db;
                border-radius: 50%;
                animation: spinner-animation 1.2s linear infinite;
                animation-delay: ${i * 0.1}s;
            `;
            const angle = (i * 30) * Math.PI / 180;
            spinnerPart.style.top = `${25 + 18 * Math.sin(angle)}px`;
            spinnerPart.style.left = `${25 + 18 * Math.cos(angle)}px`;
            spinnerContainer.appendChild(spinnerPart);
        }
        
        // 添加旋转动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spinner-animation {
                0%, 80%, 100% { opacity: 0; }
                40% { opacity: 1; }
            }
        `;
        
        // 将样式和旋转元素添加到覆盖层
        loadingOverlay.appendChild(style);
        loadingOverlay.appendChild(spinnerContainer);
        
        // 添加到页面
        document.body.appendChild(loadingOverlay);
    }
    
    // 显示覆盖层
    loadingOverlay.style.display = 'flex';
}

// 关闭加载动画
function closeLoadingPage() {    
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// 展示提示页函数
function showPromptPage(argument) {
    // 查找对应的常量对象
    const pageConstant = Object.values(SHOW_ELEMENT_CONSTANTS).find(item => item.page === argument.page);
    
    if (pageConstant) {
        // 获取文本并替换占位符
        let displayText = pageConstant.text;
        if (argument.site) {
            displayText = displayText.replace(/{site}/g, argument.site.replace('H5', ''));
        }
        
        // 在页面上显示文本
        showPromptMessage(displayText);
    }
}

// 显示提示页面消息的辅助函数
function showPromptMessage(text) {
    // 创建或更新消息元素
    let messageOverlay = document.getElementById('success-message-overlay');
    if (!messageOverlay) {
        // 创建全屏覆盖层
        messageOverlay = document.createElement('div');
        messageOverlay.id = 'success-message-overlay';
        messageOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #D3D3D3;
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        `;
        document.body.appendChild(messageOverlay);
    }
    
    // 清空之前的内容
    messageOverlay.innerHTML = '';
    
    // 创建消息容器
    const messageContainer = document.createElement('div');
    messageContainer.style.cssText = `
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        text-align: center;
        padding: 30px 25px;
        max-width: 400px;
        width: calc(70% - 40px);
        animation: popup 0.3s ease-out;
        position: relative;
        top: -50px;
    `;
    
    // 添加弹出动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes popup {
            0% { transform: scale(0.8) translateY(20px); opacity: 0; }
            100% { transform: scale(1) translateY(0); opacity: 1; }
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            #success-message-container {
                width: calc(100% - 30px);
                padding: 25px 20px;
                border-radius: 10px;
            }
            
            #success-message-text {
                font-size: 17px;
                margin-bottom: 22px;
            }
            
            #success-message-button {
                padding: 14px 25px;
                font-size: 18px;
                min-width: 100px;
            }
        }
        
        @media (max-width: 480px) {
            #success-message-container {
                width: calc(100% - 20px);
                padding: 22px 18px;
            }
            
            #success-message-text {
                font-size: 16px;
                margin-bottom: 20px;
            }
            
            #success-message-button {
                padding: 13px 22px;
                font-size: 17px;
                width: 60%;
            }
        }
    `;
    document.head.appendChild(style);
    
    // 创建文本元素
    const textElement = document.createElement('div');
    textElement.id = 'success-message-text';
    textElement.textContent = text;
    textElement.style.cssText = `
        font-size: 18px;
        color: #333;
        margin-bottom: 25px;
        line-height: 1.5;
    `;
    
    // 创建确认按钮
    const confirmButton = document.createElement('button');
    confirmButton.id = 'success-message-button';
    confirmButton.textContent = '确认';
    confirmButton.style.cssText = `
        padding: 12px 30px;
        font-size: 17px;
        background-color: #007AFF;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
        min-width: 120px;
        transition: background-color 0.2s;
    `;
    
    // 添加按钮悬停效果
    confirmButton.addEventListener('mouseenter', () => {
        confirmButton.style.backgroundColor = '#0062CC';
    });
    
    confirmButton.addEventListener('mouseleave', () => {
        confirmButton.style.backgroundColor = '#007AFF';
    });
    
    // 添加按钮点击事件
    confirmButton.addEventListener('click', () => {
        closeWindow();
    });
    
    // 组装元素
    messageContainer.id = 'success-message-container';
    messageContainer.appendChild(textElement);
    messageContainer.appendChild(confirmButton);
    messageOverlay.appendChild(messageContainer);
    
    // 显示覆盖层
    messageOverlay.style.display = 'flex';
}