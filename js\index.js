

let jhSdk;
let serverToken;

// LD SDK 相关变量
let ldClient;
let currentSdkType = 'LD'; // 'JH' 或 'LD'
let ldInitFlag = true;

window.onload = function () {
    const uid = "001";//localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID);
    const token = "token001";//localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN);
    if (uid && token) {
        console.log('检测到 uid  和 token:', uid, token);
        this.init();
    } else {
        console.log('未检测到 uid  和 token，重新登录');
        const queryString = window.location.search.substring(1);
        redirect2Login('index',queryString);
    }

    // 初始化SDK切换按钮
    initSdkSwitchButtons();
    // 设置初始按钮状态
    updateSdkButtonState();
};

function init() {
    if (currentSdkType === 'JH') {
        initJHSdk();
    } else {
        initLDSdk();
    }

    // 页面加载完成后检查URL参数并自动触发相应action
    const uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID) || ''; // 从缓存获取
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || ''; // 从缓存获取
    const wsUrl = `${window.WEBSOCKET_API_URL}?uid=${uid}&token=${token}`;
    socketManager.init(wsUrl);
    executeActionRequest();
}

function initJHSdk() {
    // 获取剪切板及获取摄像头等功能需要在本地或https安全网址下才能正常进行
    let params = {
        serverToken, // 会话标识
        libPath: getBasePath() + '/lib/', // lib文件路径(默认/lib/)
        linkMethod: '0', //0表示直连，1表示媒体服务器转发
        callbacks: {
            onInitSuccess() {
                console.log('JH SDK 初始化成功');
                connect();
            },
            onInitFail() {
                console.log('JH SDK 初始化失败');
            },
            onConnectSuccess() {
                console.log('JH SDK 连接成功');
            },
            onConnectFail() {
                console.log('JH SDK 连接失败');
            },
            onAuthFailure() {
                console.log('JH SDK 会话失效');
            },
            onUltraVires(phoneIds, msg) {
                console.log('JH SDK 用户越权操作', phoneIds, msg);
            },
            onStoped() {
                console.log('JH SDK 连接终止');
            },
        },
    };
    jhSdk = new JHSDK(params);
}

function connect() {
    if (currentSdkType === 'JH') {
        connectJH();
    } else {
        connectLD();
    }
}

function connectJH() {
    // 启动云手机并开始展示云手机相关画面
    console.log('JH SDK 触发连接');

    const uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID) || ''; // 从缓存获取
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || ''; // 从缓存获取
    const postData = {
        v: '1.0.0',
        arg: {
            uid: uid,
            token: token,
        },
    };
    const connUrl = `${window.API_URL_BASE_VG}/decs-cvgs/api/aia/get-cph-connection`;

    console.log('请求触发, connUrl:', connUrl, ' || postData:', postData);
    axios
        .post(connUrl, postData)
        .then((res) => {
            console.log('connectInfo, res:', res);
            console.log('connectInfo, res.data:', res?.data);

            // if (res?.data?.errorCode === -45813) {
            if (res?.data?.errorCode !== 0) {
                console.log('登录态失效，跳转到登录页');
                const queryString = window.location.search.substring(1);
                redirect2Login('index',queryString);
                return;
            }

            const data = res.data.data;
            data.extParam = `uid=${uid}&token=${token}`;
            console.log('startPhone.container, data:', data);
            jhSdk.startPhone('container', data);

            jhSdk.setCameraDevice();
        })
        .catch((err) => {
            console.log('get conn error:', err);
            weui.alert(err.message || '未知错误');
        });
}

async function initLDSdk() {
    try {
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const libavUrl = isMobile ? undefined : window.LD_SDK_CONFIG.LIBAV_URL;
        const init_res = await LDSDK.InitSdk(libavUrl);
        ldInitFlag = true;
        console.log('LD SDK 初始化成功:' + JSON.stringify(init_res) + ", isMobile="+isMobile);
        LDSDK.GetMediaCapacity().then(res => {
            console.log('LD SDK 媒体能力:', JSON.stringify(res));
        });
        connectLD();
    } catch (err) {
        console.log('LD SDK 初始化失败' + JSON.stringify(err));
        ldInitFlag = false;
        weui.alert('LD SDK 初始化失败: ' + JSON.stringify(err));
    }
}

async function connectLD() {
    if (!ldInitFlag) await initLDSdk();

    const uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID) || '';
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || '';

    if (!ldClient) {
        console.log('LD SDK 连接cph服务');
        // 根据配置选择连接地址
        const device_url = window.LD_SDK_CONFIG.USE_TEST_URL
            ? window.LD_SDK_CONFIG.TEST_URL
            : `${window.WEBSOCKET_API_URL.replace('wsforaiactl', 'wsforcph-test')}?uid=${uid}&token=${token}`;
        ldClient = LDSDK.CreateDevice(device_url, {
            onScreenSize: (width, height) => {
                console.log('LD SDK 屏幕分辨率' + width + 'x' + height);
            },
            onClipboard: (text) => {
                console.log('LD SDK 剪贴板内容' + text);
            },
            onClose: (error) => {
                console.log('LD SDK 连接关闭' + JSON.stringify(error));
                onLDClose();
            }
        });
        ldClient.debug.log_media = true;
        ldClient.debug.log_device = true;
    }

    try {
        const connect_res = await ldClient.Connect();
        console.log('LD SDK connect_res', connect_res);
        if (!connect_res.IsSuccess()) {
            console.log('LD SDK 连接失败', connect_res.message);
            weui.alert('LD SDK 连接失败: ' + connect_res.message);
            return;
        }

        let login_info = new LDSDK.LDLoginInfo();
        login_info.uid = uid;
        login_info.token = token;
        login_info.app_key = 'app_key';
        login_info.client_type = LDSDK.LDDeviceType.CLIENT_TYPE_WEB;

        const login_result = await ldClient.Login(login_info);
        console.log('LD SDK login_result', login_result);
        if (!login_result.IsSuccess()) {
            console.log('LD SDK 登录失败', login_result.message);
            weui.alert('LD SDK 登录失败: ' + login_result.message);
            return;
        }

        const video_container = document.getElementById('container');
        // const width = document.body.clientWidth * 2;
        // const height = (document.body.clientHeight - 42) * 2; // 减去title高度
        // const width = window.screen.width;//document.body.clientWidth;
        // const height = window.screen.height;//document.body.clientHeight; // 减去title高度

        const width = document.documentElement.clientWidth;
        const height = document.documentElement.clientHeight;

        video_container.style.width = width + 'px';
        video_container.style.height = height + 'px';

        let info = {
            width: width,
            height: height,
            auto_rotate: false,
            no_audio: true, // 静音
            quality: LDSDK.LDVideoQuality.kVQ1080p,
            video_container: video_container,
            force_wasm: false,
            onMediaFirstFrame: () => {
                console.log('LD SDK 首帧加载完成');
                onLDOpen();
            },
            onMediaSize: (w, h) => {
                console.log('LD SDK 横屏变化', w, h);
            },
            onMediaRotation: (rotation) => {
                console.log('LD SDK 旋转', rotation);
            }
        };

        await switchLDScreenSize(width, height);
        setupLDTouchEvents();

        // 通过原生协议连接媒体流
        await ldClient.ConnectMediaNative(info);
        console.log('LD SDK 连接成功');
    } catch (err) {
        console.log('LD SDK 连接错误:', err);
        weui.alert('LD SDK 连接错误: ' + JSON.stringify(err));
    }
}

function onLDOpen() {
    console.log('LD SDK connect succ');
}

function onLDClose() {
    console.log('LD SDK connect close');
    // 可以在这里添加重连逻辑
}

async function switchLDScreenSize(w, h) {
    if (!ldClient) return;
    const switch_screen_size_res = await ldClient.SwitchScreenSize(w, h);
    if (!switch_screen_size_res.IsSuccess()) {
        console.log('LD SDK 切换云机屏幕分辨率失败', switch_screen_size_res.message);
    }
}

function setupLDTouchEvents() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (!isMobile) return;

    const video_input_div = document.getElementById('container');
    if (!video_input_div) return;

    const divRect = video_input_div.getBoundingClientRect();

    const touchstartListener = (event) => {
        console.log('LD SDK touch start', ldClient);
        ldClient?.Touch(LDSDK.LDDeviceOperation.TOUCH_DOWN);
        const touchArray = Array.from(event.changedTouches);
        ldClient?.TouchMove(touchArray.map((touch) => {
            const relativeX = touch.clientX - divRect.left;
            const relativeY = touch.clientY - divRect.top;
            return {
                index: touch.identifier,
                targetX: relativeX,
                targetY: relativeY,
                width: divRect.width,
                height: divRect.height
            };
        }));
    };

    const touchmoveListener = (event) => {
        console.log('LD SDK touch move', ldClient);
        const touchArray = Array.from(event.changedTouches);
        ldClient?.TouchMove(touchArray.map((touch) => {
            const relativeX = touch.clientX - divRect.left;
            const relativeY = touch.clientY - divRect.top;
            return {
                index: touch.identifier,
                targetX: relativeX,
                targetY: relativeY,
                width: divRect.width,
                height: divRect.height
            };
        }));
    };

    const touchendListener = () => {
        ldClient?.Touch(LDSDK.LDDeviceOperation.TOUCH_UP);
    };

    const touchcancelListener = (event) => {
        console.log('LD SDK ontouchcancel' + event.changedTouches.length);
        ldClient?.Touch(LDSDK.LDDeviceOperation.TOUCH_UP);
    };

    // 移除旧的事件监听器
    video_input_div.removeEventListener('touchstart', touchstartListener);
    video_input_div.removeEventListener('touchmove', touchmoveListener);
    video_input_div.removeEventListener('touchend', touchendListener);
    video_input_div.removeEventListener('touchcancel', touchcancelListener);

    // 添加新的事件监听器
    video_input_div.addEventListener('touchstart', touchstartListener);
    video_input_div.addEventListener('touchmove', touchmoveListener);
    video_input_div.addEventListener('touchend', touchendListener);
    video_input_div.addEventListener('touchcancel', touchcancelListener);
}

function closePage() {
    console.log('closePage');
    if (ldClient) {
        try {
            ldClient.Disconnect();
        } catch (err) {
            console.log('LD SDK 断开连接错误:', err);
        }
    }
}

// 微信sdk相关处理
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN);
    if (!token) {
        console.error('[DOMContentLoaded] 当前页面需要登录后才能访问');
        return;
    }

    // const pageUrl = `${window.API_URL_BASE_VDI}/${window.URL_PATH}/index.html`;
    const jsApiList = ['checkJsApi', 'closeWindow', 'getLocation'];

    try {
        //处理url参数
        await doQueryParams();

        // 上报位置
        await initWxConfig(jsApiList);

        wx.ready(function () {
            console.log('[wx.ready]');
            wx.checkJsApi({
                jsApiList: jsApiList,
                success: function (res) {
                    console.log('[wx.checkJsApi] success, res:', res);
                    getAndReportLocation();
                },
                fail: function (res) {
                    console.log('[wx.checkJsApi] fail, res:', res);
                },
                complete: function (res) {
                    console.log('[wx.checkJsApi] complete, res:', res);
                },
            });
        });

        wx.error(function (res) {
            console.log('[wx.error] res:', res);
            weui.toast('微信配置失败', 2000);
        });
    } catch (error) {
        console.error('微信初始化失败:', error);
        weui.toast('微信初始化失败', 2000);
    }

    // 关闭窗口按钮点击事件
    const closeWindowBtn = document.getElementById('wxCloseWindow');
    if (closeWindowBtn) {
        closeWindowBtn.addEventListener('click', function (e) {
            e.preventDefault();
            wx.closeWindow();
        });
    }

    // 获取地理位置按钮点击事件
    const getLocationBtn = document.getElementById('wxGetLocation');
    if (getLocationBtn) {
        getLocationBtn.addEventListener('click', async function (e) {
            e.preventDefault();
            await initWxConfig(jsApiList);
            wx.getLocation({
                type: 'wgs84',
                success: function (res) {
                    console.log('获取位置成功：', res);
                    weui.toast('获取位置成功', 2000);
                    alert(
                        `位置信息：\n纬度：${res.latitude}\n经度：${res.longitude}\n速度：${res.speed}\n精确度：${res.accuracy}`,
                    );
                },
                fail: function (res) {
                    console.error('获取位置失败：', res);
                    weui.toast('获取位置失败', 2000);
                },
            });
        });
    }
});


// 获取并上报地理位置
function getAndReportLocation() {
    wx.getLocation({
        type: 'wgs84',
        success: function (res) {
            console.log('获取位置成功：', res);
            weui.toast('获取位置成功', 2000);
            reportGps(res);
        },
        fail: function (res) {
            console.error('获取位置失败：', res);
            weui.toast('获取位置失败', 2000);
        },
    });
}

async function reportGps(locationRes) {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || '';
    const postData = {
        v: '1.0.0',
        arg: {
            gps_type: 'wgs84',
            latitude: locationRes.latitude,
            longitude: locationRes.longitude,
            speed: locationRes.speed,
            accuracy: locationRes.accuracy,
        },
    };
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };
    const gpsUrl = `${window.API_URL_BASE_VDI}/decs-cvgs/api/aia/set-gps`;

    try {
        await fetch(gpsUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(postData),
        });
        console.log('GPS a上报成功:', postData);
    } catch (error) {
        console.error('上报GPS失败:', error);
    }
}


async function doQueryParams() {
    const queryString = window.location.search.substring(1);
    const urlParams = new URLSearchParams(queryString);

    const action = urlParams.get('action');
    console.log('action:', action);
    if (action) {
        await reportAction(action);
    }
}


async function reportAction(action) {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || '';
    const postData = {
        v: '1.0.0',
        arg: {
            action: action,
        },
    };
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };
    const gpsUrl = `${window.API_URL_BASE_VDI}/decs-cvgs/api/aia/report-action`;

    try {
        await fetch(gpsUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(postData),
        });
        console.log('action上报成功:', postData);
    } catch (error) {
        console.error('action上报失败:', error);
    }
}

// url存在action则执行请求
function executeActionRequest() {
    // 获取URL中的查询参数
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');

    // 如果存在action参数，则自动触发
    if (action) {
        console.log('检测到URL中的action参数:', action);
        const actionRequestMessage = executeAndCreateActionRequest(action);
        console.log('生成的action请求消息:', actionRequestMessage);

        // 发送消息
        socketManager.sendWhenReady(actionRequestMessage);
    }
}

// SDK 切换功能
function initSdkSwitchButtons() {
    const sdkSwitchBtn = document.getElementById('sdkSwitchBtn');

    if (sdkSwitchBtn) {
        sdkSwitchBtn.addEventListener('click', () => {
            // 切换到另一种SDK
            const newSdkType = currentSdkType === 'JH' ? 'LD' : 'JH';
            switchSdk(newSdkType);
        });
    }
}

function switchSdk(sdkType) {
    if (currentSdkType === sdkType) return;

    // 清理当前连接
    cleanupCurrentSdk();

    // 切换SDK类型
    currentSdkType = sdkType;

    // 更新按钮状态
    updateSdkButtonState();

    // 清空容器
    const container = document.getElementById('container');
    if (container) {
        container.innerHTML = '';
    }

    // 重新初始化
    init();
}

function cleanupCurrentSdk() {
    if (currentSdkType === 'JH' && jhSdk) {
        try {
            jhSdk.stopPhone();
        } catch (err) {
            console.log('JH SDK 停止错误:', err);
        }
        jhSdk = null;
    } else if (currentSdkType === 'LD' && ldClient) {
        try {
            ldClient.Disconnect();
        } catch (err) {
            console.log('LD SDK 断开连接错误:', err);
        }
        ldClient = null;
        ldInitFlag = false;
    }
}

function updateSdkButtonState() {
    const sdkSwitchBtn = document.getElementById('sdkSwitchBtn');

    if (sdkSwitchBtn) {
        // 移除所有状态类
        sdkSwitchBtn.classList.remove('jh-active', 'ld-active');

        // 根据当前SDK类型设置状态和文案
        if (currentSdkType === 'JH') {
            sdkSwitchBtn.classList.add('jh-active');
            sdkSwitchBtn.textContent = 'JH';
        } else {
            sdkSwitchBtn.classList.add('ld-active');
            sdkSwitchBtn.textContent = 'LD';
        }
    }
}