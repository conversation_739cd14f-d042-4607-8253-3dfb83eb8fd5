

let jhSdk;
let serverToken;

window.onload = function () {
    const uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID);
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN);
    if (uid && token) {
        console.log('检测到 uid  和 token:', uid, token);
        this.init();
    } else {
        console.log('未检测到 uid  和 token，重新登录');
        const queryString = window.location.search.substring(1);
        redirect2Login('index',queryString);
    }
};

function init() {
    // 获取剪切板及获取摄像头等功能需要在本地或https安全网址下才能正常进行
    let params = {
        serverToken, // 会话标识
        libPath: getBasePath() + '/lib/', // lib文件路径(默认/lib/)
        linkMethod: '0', //0表示直连，1表示媒体服务器转发
        callbacks: {
            onInitSuccess() {
                console.log('初始化成功');
                connect();
            },
            onInitFail() {
                console.log('初始化失败');
            },
            onConnectSuccess() {
                console.log('连接成功');
            },
            onConnectFail() {
                console.log('连接失败');
            },
            onAuthFailure() {
                console.log('会话失效');
            },
            onUltraVires(phoneIds, msg) {
                console.log('用户越权操作', phoneIds, msg);
            },
            onStoped() {
                console.log('连接终止');
            },
        },
    };
    jhSdk = new JHSDK(params);

    // 页面加载完成后检查URL参数并自动触发相应action
    const uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID) || ''; // 从缓存获取
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || ''; // 从缓存获取
    const wsUrl = `${window.WEBSOCKET_API_URL}?uid=${uid}&token=${token}`;
    socketManager.init(wsUrl);
    executeActionRequest();
}

function connect() {
    // 启动云手机并开始展示云手机相关画面
    console.log('触发连接');

    const uid = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_UID) || ''; // 从缓存获取
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || ''; // 从缓存获取
    const postData = {
        v: '1.0.0',
        arg: {
            uid: uid,
            token: token,
        },
    };
    const connUrl = `${window.API_URL_BASE_VG}/decs-cvgs/api/aia/get-cph-connection`;

    console.log('请求触发, connUrl:', connUrl, ' || postData:', postData);
    axios
        .post(connUrl, postData)
        .then((res) => {
            console.log('connectInfo, res:', res);
            console.log('connectInfo, res.data:', res?.data);

            // if (res?.data?.errorCode === -45813) {
            if (res?.data?.errorCode !== 0) {
                console.log('登录态失效，跳转到登录页');
                const queryString = window.location.search.substring(1);
                redirect2Login('index',queryString);
                return;
            }

            // if (res?.data?.errorCode !== 0) {
            //     // weui.alert(res?.data?.errorMessage || '云手机连接失败');
            //     weui.alert('由于切换了公众号，需要重新绑定云手机，请先给个人助理发消息"查看云手机"',function () {
            //         console.log('用户点击了确定');
            //         closeWindow();
            //     });
            //     return;
            // }

            const data = res.data.data;
            data.extParam = `uid=${uid}&token=${token}`;
            console.log('startPhone.container, data:', data);
            jhSdk.startPhone('container', data);

            jhSdk.setCameraDevice();
        })
        .catch((err) => {
            console.log('get conn error:', err);
            weui.alert(err.message || '未知错误');
        });
}

function closePage() {
    console.log('closePage');
}

// 微信sdk相关处理
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN);
    if (!token) {
        console.error('[DOMContentLoaded] 当前页面需要登录后才能访问');
        return;
    }

    // const pageUrl = `${window.API_URL_BASE_VDI}/${window.URL_PATH}/index.html`;
    const jsApiList = ['checkJsApi', 'closeWindow', 'getLocation'];

    try {
        //处理url参数
        await doQueryParams();

        // 上报位置
        await initWxConfig(jsApiList);

        wx.ready(function () {
            console.log('[wx.ready]');
            wx.checkJsApi({
                jsApiList: jsApiList,
                success: function (res) {
                    console.log('[wx.checkJsApi] success, res:', res);
                    getAndReportLocation();
                },
                fail: function (res) {
                    console.log('[wx.checkJsApi] fail, res:', res);
                },
                complete: function (res) {
                    console.log('[wx.checkJsApi] complete, res:', res);
                },
            });
        });

        wx.error(function (res) {
            console.log('[wx.error] res:', res);
            weui.toast('微信配置失败', 2000);
        });
    } catch (error) {
        console.error('微信初始化失败:', error);
        weui.toast('微信初始化失败', 2000);
    }

    // 关闭窗口按钮点击事件
    const closeWindowBtn = document.getElementById('wxCloseWindow');
    if (closeWindowBtn) {
        closeWindowBtn.addEventListener('click', function (e) {
            e.preventDefault();
            wx.closeWindow();
        });
    }

    // 获取地理位置按钮点击事件
    const getLocationBtn = document.getElementById('wxGetLocation');
    if (getLocationBtn) {
        getLocationBtn.addEventListener('click', async function (e) {
            e.preventDefault();
            await initWxConfig(jsApiList);
            wx.getLocation({
                type: 'wgs84',
                success: function (res) {
                    console.log('获取位置成功：', res);
                    weui.toast('获取位置成功', 2000);
                    alert(
                        `位置信息：\n纬度：${res.latitude}\n经度：${res.longitude}\n速度：${res.speed}\n精确度：${res.accuracy}`,
                    );
                },
                fail: function (res) {
                    console.error('获取位置失败：', res);
                    weui.toast('获取位置失败', 2000);
                },
            });
        });
    }
});


// 获取并上报地理位置
function getAndReportLocation() {
    wx.getLocation({
        type: 'wgs84',
        success: function (res) {
            console.log('获取位置成功：', res);
            weui.toast('获取位置成功', 2000);
            reportGps(res);
        },
        fail: function (res) {
            console.error('获取位置失败：', res);
            weui.toast('获取位置失败', 2000);
        },
    });
}

async function reportGps(locationRes) {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || '';
    const postData = {
        v: '1.0.0',
        arg: {
            gps_type: 'wgs84',
            latitude: locationRes.latitude,
            longitude: locationRes.longitude,
            speed: locationRes.speed,
            accuracy: locationRes.accuracy,
        },
    };
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };
    const gpsUrl = `${window.API_URL_BASE_VDI}/decs-cvgs/api/aia/set-gps`;

    try {
        await fetch(gpsUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(postData),
        });
        console.log('GPS a上报成功:', postData);
    } catch (error) {
        console.error('上报GPS失败:', error);
    }
}


async function doQueryParams() {
    const queryString = window.location.search.substring(1);
    const urlParams = new URLSearchParams(queryString);

    const action = urlParams.get('action');
    console.log('action:', action);
    if (action) {
        await reportAction(action);
    }
}


async function reportAction(action) {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN) || '';
    const postData = {
        v: '1.0.0',
        arg: {
            action: action,
        },
    };
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
    };
    const gpsUrl = `${window.API_URL_BASE_VDI}/decs-cvgs/api/aia/report-action`;

    try {
        await fetch(gpsUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(postData),
        });
        console.log('action上报成功:', postData);
    } catch (error) {
        console.error('action上报失败:', error);
    }
}

// url存在action则执行请求
function executeActionRequest() {
    // 获取URL中的查询参数
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');

    // 如果存在action参数，则自动触发
    if (action) {
        console.log('检测到URL中的action参数:', action);
        const actionRequestMessage = executeAndCreateActionRequest(action);
        console.log('生成的action请求消息:', actionRequestMessage);

        // 发送消息
        socketManager.sendWhenReady(actionRequestMessage);
    }
}