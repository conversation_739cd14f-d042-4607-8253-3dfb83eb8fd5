!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.LDSDK=t():e.LDSDK=t()}(self,(()=>(()=>{var e={339:function(e,t,i){var s="function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,i){e!=Array.prototype&&e!=Object.prototype&&(e[t]=i.value)},r="undefined"!=typeof window&&window===this?this:void 0!==i.g&&null!=i.g?i.g:this;function n(){n=function(){},r.Symbol||(r.Symbol=l)}function o(e,t){this.a=e,s(this,"description",{configurable:!0,writable:!0,value:t})}o.prototype.toString=function(){return this.a};var a,l=(a=0,function e(t){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new o("jscomp_symbol_"+(t||"")+"_"+a++,t)});function c(){n();var e=r.Symbol.iterator;e||(e=r.Symbol.iterator=r.Symbol("Symbol.iterator")),"function"!=typeof Array.prototype[e]&&s(Array.prototype,e,{configurable:!0,writable:!0,value:function(){return function(e){return c(),(e={next:e})[r.Symbol.iterator]=function(){return this},e}(function(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}}(this))}}),c=function(){}}!function(e,t){if(t){var i=r;e=e.split(".");for(var n=0;n<e.length-1;n++){var o=e[n];o in i||(i[o]={}),i=i[o]}(t=t(n=i[e=e[e.length-1]]))!=n&&null!=t&&s(i,e,{configurable:!0,writable:!0,value:t})}}("Array.prototype.entries",(function(e){return e||function(){return function(e,t){c(),e instanceof String&&(e+="");var i=0,s={next:function(){if(i<e.length){var r=i++;return{value:t(r,e[r]),done:!1}}return s.next=function(){return{done:!0,value:void 0}},s.next()}};return s[Symbol.iterator]=function(){return s},s}(this,(function(e,t){return[e,t]}))}}));var h=this||self;function p(e,t,i){e=e.split("."),i=i||h,e[0]in i||void 0===i.execScript||i.execScript("var "+e[0]);for(var s;e.length&&(s=e.shift());)e.length||void 0===t?i=i[s]&&i[s]!==Object.prototype[s]?i[s]:i[s]={}:i[s]=t}function u(e){var t=typeof e;if("object"==t){if(!e)return"null";if(e instanceof Array)return"array";if(e instanceof Object)return t;var i=Object.prototype.toString.call(e);if("[object Window]"==i)return"object";if("[object Array]"==i||"number"==typeof e.length&&void 0!==e.splice&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("splice"))return"array";if("[object Function]"==i||void 0!==e.call&&void 0!==e.propertyIsEnumerable&&!e.propertyIsEnumerable("call"))return"function"}else if("function"==t&&void 0===e.call)return"object";return t}function d(e){var t=typeof e;return"object"==t&&null!=e||"function"==t}var f="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),y=Array.prototype.forEach?function(e,t){Array.prototype.forEach.call(e,t,void 0)}:function(e,t){for(var i=e.length,s="string"==typeof e?e.split(""):e,r=0;r<i;r++)r in s&&t.call(void 0,s[r],r,e)},m=Array.prototype.map?function(e,t){return Array.prototype.map.call(e,t,void 0)}:function(e,t){for(var i=e.length,s=Array(i),r="string"==typeof e?e.split(""):e,n=0;n<i;n++)n in r&&(s[n]=t.call(void 0,r[n],n,e));return s};function g(e,t,i){return 2>=arguments.length?Array.prototype.slice.call(e,t):Array.prototype.slice.call(e,t,i)}function _(e,t,i,s){var r="Assertion failed";if(i){r+=": "+i;var n=s}else e&&(r+=": "+e,n=t);throw Error(r,n||[])}function w(e,t,i){for(var s=[],r=2;r<arguments.length;++r)s[r-2]=arguments[r];return e||_("",null,t,s),e}function E(e,t){for(var i=[],s=1;s<arguments.length;++s)i[s-1]=arguments[s];throw Error("Failure"+(e?": "+e:""),i)}function v(e,t,i,s){for(var r=[],n=3;n<arguments.length;++n)r[n-3]=arguments[n];e instanceof t||_("Expected instanceof %s but got %s.",[b(t),b(e)],i,r)}function b(e){return e instanceof Function?e.displayName||e.name||"unknown type name":e instanceof Object?e.constructor.displayName||e.constructor.name||Object.prototype.toString.call(e):null===e?"null":typeof e}function M(e,t){if(this.c=e,this.b=t,this.a={},this.arrClean=!0,0<this.c.length){for(e=0;e<this.c.length;e++){var i=(t=this.c[e])[0];this.a[i.toString()]=new O(i,t[1])}this.arrClean=!0}}function T(e){this.a=0,this.b=e}function I(e,t){return e.b?(t.a||(t.a=new e.b(t.value)),t.a):t.value}function A(e){e=e.a;var t,i=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&i.push(t);return i}function O(e,t){this.key=e,this.value=t,this.a=void 0}function S(e){if(8192>=e.length)return String.fromCharCode.apply(null,e);for(var t="",i=0;i<e.length;i+=8192)t+=String.fromCharCode.apply(null,g(e,i,i+8192));return t}p("jspb.Map",M,void 0),M.prototype.g=function(){if(this.arrClean){if(this.b){var e,t=this.a;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var i=t[e].a;i&&i.g()}}}else{for(this.c.length=0,(t=A(this)).sort(),e=0;e<t.length;e++){var s=this.a[t[e]];(i=s.a)&&i.g(),this.c.push([s.key,s.value])}this.arrClean=!0}return this.c},M.prototype.toArray=M.prototype.g,M.prototype.Mc=function(e,t){for(var i=this.g(),s=[],r=0;r<i.length;r++){var n=this.a[i[r][0].toString()];I(this,n);var o=n.a;o?(w(t),s.push([n.key,t(e,o)])):s.push([n.key,n.value])}return s},M.prototype.toObject=M.prototype.Mc,M.fromObject=function(e,t,i){t=new M([],t);for(var s=0;s<e.length;s++){var r=e[s][0],n=i(e[s][1]);t.set(r,n)}return t},T.prototype.next=function(){return this.a<this.b.length?{done:!1,value:this.b[this.a++]}:{done:!0,value:void 0}},"undefined"!=typeof Symbol&&(T.prototype[Symbol.iterator]=function(){return this}),M.prototype.Jb=function(){return A(this).length},M.prototype.getLength=M.prototype.Jb,M.prototype.clear=function(){this.a={},this.arrClean=!1},M.prototype.clear=M.prototype.clear,M.prototype.Cb=function(e){e=e.toString();var t=this.a.hasOwnProperty(e);return delete this.a[e],this.arrClean=!1,t},M.prototype.del=M.prototype.Cb,M.prototype.Eb=function(){var e=[],t=A(this);t.sort();for(var i=0;i<t.length;i++){var s=this.a[t[i]];e.push([s.key,s.value])}return e},M.prototype.getEntryList=M.prototype.Eb,M.prototype.entries=function(){var e=[],t=A(this);t.sort();for(var i=0;i<t.length;i++){var s=this.a[t[i]];e.push([s.key,I(this,s)])}return new T(e)},M.prototype.entries=M.prototype.entries,M.prototype.keys=function(){var e=[],t=A(this);t.sort();for(var i=0;i<t.length;i++)e.push(this.a[t[i]].key);return new T(e)},M.prototype.keys=M.prototype.keys,M.prototype.values=function(){var e=[],t=A(this);t.sort();for(var i=0;i<t.length;i++)e.push(I(this,this.a[t[i]]));return new T(e)},M.prototype.values=M.prototype.values,M.prototype.forEach=function(e,t){var i=A(this);i.sort();for(var s=0;s<i.length;s++){var r=this.a[i[s]];e.call(t,I(this,r),r.key,this)}},M.prototype.forEach=M.prototype.forEach,M.prototype.set=function(e,t){var i=new O(e);return this.b?(i.a=t,i.value=t.g()):i.value=t,this.a[e.toString()]=i,this.arrClean=!1,this},M.prototype.set=M.prototype.set,M.prototype.get=function(e){if(e=this.a[e.toString()])return I(this,e)},M.prototype.get=M.prototype.get,M.prototype.has=function(e){return e.toString()in this.a},M.prototype.has=M.prototype.has,M.prototype.Jc=function(e,t,i,s,r){var n=A(this);n.sort();for(var o=0;o<n.length;o++){var a=this.a[n[o]];t.Va(e),i.call(t,1,a.key),this.b?s.call(t,2,I(this,a),r):s.call(t,2,a.value),t.Ya()}},M.prototype.serializeBinary=M.prototype.Jc,M.deserializeBinary=function(e,t,i,s,r,n,o){for(;t.oa()&&!t.bb();){var a=t.c;1==a?n=i.call(t):2==a&&(e.b?(w(r),o||(o=new e.b),s.call(t,o,r)):o=s.call(t))}w(null!=n),w(null!=o),e.set(n,o)};var F={"\0":"\\0","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\x0B",'"':'\\"',"\\":"\\\\","<":"\\u003C"},C={"'":"\\'"},k={},D=null;function R(e,t){void 0===t&&(t=0),P(),t=k[t];for(var i=[],s=0;s<e.length;s+=3){var r=e[s],n=s+1<e.length,o=n?e[s+1]:0,a=s+2<e.length,l=a?e[s+2]:0,c=r>>2;r=(3&r)<<4|o>>4,o=(15&o)<<2|l>>6,l&=63,a||(l=64,n||(o=64)),i.push(t[c],t[r],t[o]||"",t[l]||"")}return i.join("")}function N(e){var t=e.length,i=3*t/4;i%3?i=Math.floor(i):-1!="=.".indexOf(e[t-1])&&(i=-1!="=.".indexOf(e[t-2])?i-2:i-1);var s=new Uint8Array(i),r=0;return function(e,t){function i(t){for(;s<e.length;){var i=e.charAt(s++),r=D[i];if(null!=r)return r;if(!/^[\s\xa0]*$/.test(i))throw Error("Unknown base64 encoding at char: "+i)}return t}P();for(var s=0;;){var r=i(-1),n=i(0),o=i(64),a=i(64);if(64===a&&-1===r)break;t(r<<2|n>>4),64!=o&&(t(n<<4&240|o>>2),64!=a&&t(o<<6&192|a))}}(e,(function(e){s[r++]=e})),s.subarray(0,r)}function P(){if(!D){D={};for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),t=["+/=","+/","-_=","-_.","-_"],i=0;5>i;i++){var s=e.concat(t[i].split(""));k[i]=s;for(var r=0;r<s.length;r++){var n=s[r];void 0===D[n]&&(D[n]=r)}}}}p("jspb.ConstBinaryMessage",(function(){}),void 0),p("jspb.BinaryMessage",(function(){}),void 0),p("jspb.BinaryConstants.FieldType",{yb:-1,ee:1,FLOAT:2,ke:3,te:4,je:5,xb:6,wb:7,BOOL:8,re:9,ie:10,le:11,ce:12,se:13,ge:14,me:15,ne:16,oe:17,pe:18,he:30,ve:31},void 0),p("jspb.BinaryConstants.WireType",{yb:-1,ue:0,xb:1,de:2,qe:3,fe:4,wb:5},void 0),p("jspb.BinaryConstants.FieldTypeToWireType",(function(e){switch(e){case 5:case 3:case 13:case 4:case 17:case 18:case 8:case 14:case 31:return 0;case 1:case 6:case 16:case 30:return 1;case 9:case 11:case 12:return 2;case 2:case 7:case 15:return 5;default:return-1}}),void 0),p("jspb.BinaryConstants.INVALID_FIELD_NUMBER",-1,void 0),p("jspb.BinaryConstants.FLOAT32_EPS",1401298464324817e-60,void 0),p("jspb.BinaryConstants.FLOAT32_MIN",11754943508222875e-54,void 0),p("jspb.BinaryConstants.FLOAT32_MAX",34028234663852886e22,void 0),p("jspb.BinaryConstants.FLOAT64_EPS",5e-324,void 0),p("jspb.BinaryConstants.FLOAT64_MIN",22250738585072014e-324,void 0),p("jspb.BinaryConstants.FLOAT64_MAX",17976931348623157e292,void 0),p("jspb.BinaryConstants.TWO_TO_20",1048576,void 0),p("jspb.BinaryConstants.TWO_TO_23",8388608,void 0),p("jspb.BinaryConstants.TWO_TO_31",2147483648,void 0),p("jspb.BinaryConstants.TWO_TO_32",4294967296,void 0),p("jspb.BinaryConstants.TWO_TO_52",4503599627370496,void 0),p("jspb.BinaryConstants.TWO_TO_63",0x8000000000000000,void 0),p("jspb.BinaryConstants.TWO_TO_64",0x10000000000000000,void 0),p("jspb.BinaryConstants.ZERO_HASH","\0\0\0\0\0\0\0\0",void 0);var z=0,B=0;function W(e){var t=e>>>0;e=Math.floor((e-t)/4294967296)>>>0,z=t,B=e}function x(e){var t=0>e,i=(e=Math.abs(e))>>>0;e=Math.floor((e-i)/4294967296),e>>>=0,t&&(e=~e>>>0,4294967295<(i=1+(~i>>>0))&&(i=0,4294967295<++e&&(e=0))),z=i,B=e}function L(e){var t=0>e;W(e=2*Math.abs(e)),e=z;var i=B;t&&(0==e?0==i?i=e=4294967295:(i--,e=4294967295):e--),z=e,B=i}function j(e){var t=0>e?1:0;if(0===(e=t?-e:e))0<1/e?z=B=0:(B=0,z=2147483648);else if(isNaN(e))B=0,z=2147483647;else if(34028234663852886e22<e)B=0,z=(t<<31|2139095040)>>>0;else if(11754943508222875e-54>e)e=Math.round(e/Math.pow(2,-149)),B=0,z=(t<<31|e)>>>0;else{var i=Math.floor(Math.log(e)/Math.LN2);e*=Math.pow(2,-i),16777216<=(e=Math.round(8388608*e))&&++i,B=0,z=(t<<31|i+127<<23|8388607&e)>>>0}}function G(e){var t=0>e?1:0;if(0===(e=t?-e:e))B=0<1/e?0:2147483648,z=0;else if(isNaN(e))B=2147483647,z=4294967295;else if(17976931348623157e292<e)B=(t<<31|2146435072)>>>0,z=0;else if(22250738585072014e-324>e)e/=Math.pow(2,-1074),B=(t<<31|e/4294967296)>>>0,z=e>>>0;else{var i=e,s=0;if(2<=i)for(;2<=i&&1023>s;)s++,i/=2;else for(;1>i&&-1022<s;)i*=2,s--;e*=Math.pow(2,-s),B=(t<<31|s+1023<<20|1048576*e&1048575)>>>0,z=4503599627370496*e>>>0}}function V(e){var t=e.charCodeAt(4),i=e.charCodeAt(5),s=e.charCodeAt(6),r=e.charCodeAt(7);z=e.charCodeAt(0)+(e.charCodeAt(1)<<8)+(e.charCodeAt(2)<<16)+(e.charCodeAt(3)<<24)>>>0,B=t+(i<<8)+(s<<16)+(r<<24)>>>0}function U(e,t){return 4294967296*t+(e>>>0)}function Y(e,t){var i=2147483648&t;return i&&(t=~t>>>0,0==(e=1+~e>>>0)&&(t=t+1>>>0)),e=U(e,t),i?-e:e}function H(e,t,i){var s=t>>31;return i(e<<1^s,(t<<1|e>>>31)^s)}function q(e,t){return K(e,t,Y)}function K(e,t,i){var s=-(1&e);return i((e>>>1|t<<31)^s,t>>>1^s)}function X(e){var t=2*(e>>31)+1,i=e>>>23&255;return e&=8388607,255==i?e?NaN:1/0*t:0==i?t*Math.pow(2,-149)*e:t*Math.pow(2,i-150)*(e+Math.pow(2,23))}function J(e,t){var i=2*(t>>31)+1,s=t>>>20&2047;return e=4294967296*(1048575&t)+e,2047==s?e?NaN:1/0*i:0==s?i*Math.pow(2,-1074)*e:i*Math.pow(2,s-1075)*(e+4503599627370496)}function Q(e,t){return String.fromCharCode(e>>>0&255,e>>>8&255,e>>>16&255,e>>>24&255,t>>>0&255,t>>>8&255,t>>>16&255,t>>>24&255)}function Z(e,t){function i(e,t){return e=e?String(e):"",t?"0000000".slice(e.length)+e:e}if(2097151>=t)return""+U(e,t);var s=(e>>>24|t<<8)>>>0&16777215;return e=(16777215&e)+6777216*s+6710656*(t=t>>16&65535),s+=8147497*t,t*=2,1e7<=e&&(s+=Math.floor(e/1e7),e%=1e7),1e7<=s&&(t+=Math.floor(s/1e7),s%=1e7),i(t,0)+i(s,t)+i(e,1)}function $(e,t){var i=2147483648&t;return i&&(t=~t+(0==(e=1+~e>>>0)?1:0)>>>0),e=Z(e,t),i?"-"+e:e}function ee(e,t){V(e),e=z;var i=B;return t?$(e,i):Z(e,i)}function te(e){function t(e,t){for(var i=0;8>i&&(1!==e||0<t);i++)t=e*s[i]+t,s[i]=255&t,t>>>=8}w(0<e.length);var i=!1;"-"===e[0]&&(i=!0,e=e.slice(1));for(var s=[0,0,0,0,0,0,0,0],r=0;r<e.length;r++)t(10,e.charCodeAt(r)-48);return i&&(function(){for(var e=0;8>e;e++)s[e]=255&~s[e]}(),t(1,1)),S(s)}function ie(e){return String.fromCharCode(10>e?48+e:87+e)}function se(e){return 97<=e?e-97+10:e-48}function re(e,t,i,s,r){var n=0;if(128>s)for(;t<i&&e[t++]==s;)n++,t+=r;else for(;t<i;){for(var o=s;128<o;){if(e[t++]!=(127&o|128))return n;o>>=7}if(e[t++]!=o)break;n++,t+=r}return n}function ne(e){return e.constructor===Uint8Array?e:e.constructor===ArrayBuffer||e.constructor===Array?new Uint8Array(e):e.constructor===String?N(e):e instanceof Uint8Array?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):(E("Type not convertible to Uint8Array."),new Uint8Array(0))}function oe(e,t,i){this.b=null,this.a=this.c=this.h=0,this.v=!1,e&&this.H(e,t,i)}p("jspb.utils.getSplit64Low",(function(){return z}),void 0),p("jspb.utils.getSplit64High",(function(){return B}),void 0),p("jspb.utils.splitUint64",W,void 0),p("jspb.utils.splitInt64",x,void 0),p("jspb.utils.splitZigzag64",L,void 0),p("jspb.utils.splitFloat32",j,void 0),p("jspb.utils.splitFloat64",G,void 0),p("jspb.utils.splitHash64",V,void 0),p("jspb.utils.joinUint64",U,void 0),p("jspb.utils.joinInt64",Y,void 0),p("jspb.utils.toZigzag64",H,void 0),p("jspb.utils.joinZigzag64",q,void 0),p("jspb.utils.fromZigzag64",K,void 0),p("jspb.utils.joinFloat32",X,void 0),p("jspb.utils.joinFloat64",J,void 0),p("jspb.utils.joinHash64",Q,void 0),p("jspb.utils.DIGITS","0123456789abcdef".split(""),void 0),p("jspb.utils.joinUnsignedDecimalString",Z,void 0),p("jspb.utils.joinSignedDecimalString",$,void 0),p("jspb.utils.hash64ToDecimalString",ee,void 0),p("jspb.utils.hash64ArrayToDecimalStrings",(function(e,t){for(var i=Array(e.length),s=0;s<e.length;s++)i[s]=ee(e[s],t);return i}),void 0),p("jspb.utils.decimalStringToHash64",te,void 0),p("jspb.utils.splitDecimalString",(function(e){V(te(e))}),void 0),p("jspb.utils.hash64ToHexString",(function(e){var t=Array(18);t[0]="0",t[1]="x";for(var i=0;8>i;i++){var s=e.charCodeAt(7-i);t[2*i+2]=ie(s>>4),t[2*i+3]=ie(15&s)}return t.join("")}),void 0),p("jspb.utils.hexStringToHash64",(function(e){w(18==(e=e.toLowerCase()).length),w("0"==e[0]),w("x"==e[1]);for(var t="",i=0;8>i;i++)t=String.fromCharCode(16*se(e.charCodeAt(2*i+2))+se(e.charCodeAt(2*i+3)))+t;return t}),void 0),p("jspb.utils.hash64ToNumber",(function(e,t){V(e),e=z;var i=B;return t?Y(e,i):U(e,i)}),void 0),p("jspb.utils.numberToHash64",(function(e){return x(e),Q(z,B)}),void 0),p("jspb.utils.countVarints",(function(e,t,i){for(var s=0,r=t;r<i;r++)s+=e[r]>>7;return i-t-s}),void 0),p("jspb.utils.countVarintFields",(function(e,t,i,s){var r=0;if(128>(s*=8))for(;t<i&&e[t++]==s;)for(r++;;){var n=e[t++];if(!(128&n))break}else for(;t<i;){for(n=s;128<n;){if(e[t]!=(127&n|128))return r;t++,n>>=7}if(e[t++]!=n)break;for(r++;128&(n=e[t++]););}return r}),void 0),p("jspb.utils.countFixed32Fields",(function(e,t,i,s){return re(e,t,i,8*s+5,4)}),void 0),p("jspb.utils.countFixed64Fields",(function(e,t,i,s){return re(e,t,i,8*s+1,8)}),void 0),p("jspb.utils.countDelimitedFields",(function(e,t,i,s){var r=0;for(s=8*s+2;t<i;){for(var n=s;128<n;){if(e[t++]!=(127&n|128))return r;n>>=7}if(e[t++]!=n)break;r++;for(var o=0,a=1;o+=(127&(n=e[t++]))*a,a*=128,128&n;);t+=o}return r}),void 0),p("jspb.utils.debugBytesToTextFormat",(function(e){var t='"';if(e){e=ne(e);for(var i=0;i<e.length;i++)t+="\\x",16>e[i]&&(t+="0"),t+=e[i].toString(16)}return t+'"'}),void 0),p("jspb.utils.debugScalarToTextFormat",(function(e){if("string"==typeof e){e=String(e);for(var t=['"'],i=0;i<e.length;i++){var s,r=e.charAt(i),n=r.charCodeAt(0),o=i+1;(s=F[r])||(31<n&&127>n||((n=r)in C?r=C[n]:n in F?r=C[n]=F[n]:(31<(s=n.charCodeAt(0))&&127>s?r=n:(256>s?(r="\\x",(16>s||256<s)&&(r+="0")):(r="\\u",4096>s&&(r+="0")),r+=s.toString(16).toUpperCase()),r=C[n]=r)),s=r),t[o]=s}t.push('"'),e=t.join("")}else e=e.toString();return e}),void 0),p("jspb.utils.stringToByteArray",(function(e){for(var t=new Uint8Array(e.length),i=0;i<e.length;i++){var s=e.charCodeAt(i);if(255<s)throw Error("Conversion error: string contains codepoint outside of byte range");t[i]=s}return t}),void 0),p("jspb.utils.byteSourceToUint8Array",ne,void 0),p("jspb.BinaryDecoder",oe,void 0);var ae=[];function le(e,t,i){if(ae.length){var s=ae.pop();return e&&s.H(e,t,i),s}return new oe(e,t,i)}function ce(e,t,i){this.a=le(e,t,i),this.O=this.a.B(),this.b=this.c=-1,this.h=!1,this.v=null}oe.getInstanceCacheLength=function(){return ae.length},oe.alloc=le,oe.prototype.Ca=function(){this.clear(),100>ae.length&&ae.push(this)},oe.prototype.free=oe.prototype.Ca,oe.prototype.clone=function(){return le(this.b,this.h,this.c-this.h)},oe.prototype.clone=oe.prototype.clone,oe.prototype.clear=function(){this.b=null,this.a=this.c=this.h=0,this.v=!1},oe.prototype.clear=oe.prototype.clear,oe.prototype.Y=function(){return this.b},oe.prototype.getBuffer=oe.prototype.Y,oe.prototype.H=function(e,t,i){this.b=ne(e),this.h=void 0!==t?t:0,this.c=void 0!==i?this.h+i:this.b.length,this.a=this.h},oe.prototype.setBlock=oe.prototype.H,oe.prototype.Db=function(){return this.c},oe.prototype.getEnd=oe.prototype.Db,oe.prototype.setEnd=function(e){this.c=e},oe.prototype.setEnd=oe.prototype.setEnd,oe.prototype.reset=function(){this.a=this.h},oe.prototype.reset=oe.prototype.reset,oe.prototype.B=function(){return this.a},oe.prototype.getCursor=oe.prototype.B,oe.prototype.Ma=function(e){this.a=e},oe.prototype.setCursor=oe.prototype.Ma,oe.prototype.advance=function(e){this.a+=e,w(this.a<=this.c)},oe.prototype.advance=oe.prototype.advance,oe.prototype.ya=function(){return this.a==this.c},oe.prototype.atEnd=oe.prototype.ya,oe.prototype.Qb=function(){return this.a>this.c},oe.prototype.pastEnd=oe.prototype.Qb,oe.prototype.getError=function(){return this.v||0>this.a||this.a>this.c},oe.prototype.getError=oe.prototype.getError,oe.prototype.w=function(e){for(var t=128,i=0,s=0,r=0;4>r&&128<=t;r++)i|=(127&(t=this.b[this.a++]))<<7*r;if(128<=t&&(i|=(127&(t=this.b[this.a++]))<<28,s|=(127&t)>>4),128<=t)for(r=0;5>r&&128<=t;r++)s|=(127&(t=this.b[this.a++]))<<7*r+3;if(128>t)return e(i>>>0,s>>>0);E("Failed to read varint, encoding is invalid."),this.v=!0},oe.prototype.readSplitVarint64=oe.prototype.w,oe.prototype.ea=function(e){return this.w((function(t,i){return K(t,i,e)}))},oe.prototype.readSplitZigzagVarint64=oe.prototype.ea,oe.prototype.ta=function(e){var t=this.b,i=this.a;this.a+=8;for(var s=0,r=0,n=i+7;n>=i;n--)s=s<<8|t[n],r=r<<8|t[n+4];return e(s,r)},oe.prototype.readSplitFixed64=oe.prototype.ta,oe.prototype.kb=function(){for(;128&this.b[this.a];)this.a++;this.a++},oe.prototype.skipVarint=oe.prototype.kb,oe.prototype.mb=function(e){for(;128<e;)this.a--,e>>>=7;this.a--},oe.prototype.unskipVarint=oe.prototype.mb,oe.prototype.o=function(){var e=this.b,t=e[this.a],i=127&t;return 128>t?(this.a+=1,w(this.a<=this.c),i):(i|=(127&(t=e[this.a+1]))<<7,128>t?(this.a+=2,w(this.a<=this.c),i):(i|=(127&(t=e[this.a+2]))<<14,128>t?(this.a+=3,w(this.a<=this.c),i):(i|=(127&(t=e[this.a+3]))<<21,128>t?(this.a+=4,w(this.a<=this.c),i):(i|=(15&(t=e[this.a+4]))<<28,128>t?(this.a+=5,w(this.a<=this.c),i>>>0):(this.a+=5,128<=e[this.a++]&&128<=e[this.a++]&&128<=e[this.a++]&&128<=e[this.a++]&&128<=e[this.a++]&&w(!1),w(this.a<=this.c),i)))))},oe.prototype.readUnsignedVarint32=oe.prototype.o,oe.prototype.da=function(){return~~this.o()},oe.prototype.readSignedVarint32=oe.prototype.da,oe.prototype.O=function(){return this.o().toString()},oe.prototype.Ea=function(){return this.da().toString()},oe.prototype.readSignedVarint32String=oe.prototype.Ea,oe.prototype.Ia=function(){var e=this.o();return e>>>1^-(1&e)},oe.prototype.readZigzagVarint32=oe.prototype.Ia,oe.prototype.Ga=function(){return this.w(U)},oe.prototype.readUnsignedVarint64=oe.prototype.Ga,oe.prototype.Ha=function(){return this.w(Z)},oe.prototype.readUnsignedVarint64String=oe.prototype.Ha,oe.prototype.sa=function(){return this.w(Y)},oe.prototype.readSignedVarint64=oe.prototype.sa,oe.prototype.Fa=function(){return this.w($)},oe.prototype.readSignedVarint64String=oe.prototype.Fa,oe.prototype.Ja=function(){return this.w(q)},oe.prototype.readZigzagVarint64=oe.prototype.Ja,oe.prototype.fb=function(){return this.ea(Q)},oe.prototype.readZigzagVarintHash64=oe.prototype.fb,oe.prototype.Ka=function(){return this.ea($)},oe.prototype.readZigzagVarint64String=oe.prototype.Ka,oe.prototype.Gc=function(){var e=this.b[this.a];return this.a+=1,w(this.a<=this.c),e},oe.prototype.readUint8=oe.prototype.Gc,oe.prototype.Ec=function(){var e=this.b[this.a],t=this.b[this.a+1];return this.a+=2,w(this.a<=this.c),e|t<<8},oe.prototype.readUint16=oe.prototype.Ec,oe.prototype.m=function(){var e=this.b[this.a],t=this.b[this.a+1],i=this.b[this.a+2],s=this.b[this.a+3];return this.a+=4,w(this.a<=this.c),(e|t<<8|i<<16|s<<24)>>>0},oe.prototype.readUint32=oe.prototype.m,oe.prototype.ga=function(){return U(this.m(),this.m())},oe.prototype.readUint64=oe.prototype.ga,oe.prototype.ha=function(){return Z(this.m(),this.m())},oe.prototype.readUint64String=oe.prototype.ha,oe.prototype.Xb=function(){var e=this.b[this.a];return this.a+=1,w(this.a<=this.c),e<<24>>24},oe.prototype.readInt8=oe.prototype.Xb,oe.prototype.Vb=function(){var e=this.b[this.a],t=this.b[this.a+1];return this.a+=2,w(this.a<=this.c),(e|t<<8)<<16>>16},oe.prototype.readInt16=oe.prototype.Vb,oe.prototype.P=function(){var e=this.b[this.a],t=this.b[this.a+1],i=this.b[this.a+2],s=this.b[this.a+3];return this.a+=4,w(this.a<=this.c),e|t<<8|i<<16|s<<24},oe.prototype.readInt32=oe.prototype.P,oe.prototype.ba=function(){return Y(this.m(),this.m())},oe.prototype.readInt64=oe.prototype.ba,oe.prototype.ca=function(){return $(this.m(),this.m())},oe.prototype.readInt64String=oe.prototype.ca,oe.prototype.aa=function(){return X(this.m())},oe.prototype.readFloat=oe.prototype.aa,oe.prototype.Z=function(){return J(this.m(),this.m())},oe.prototype.readDouble=oe.prototype.Z,oe.prototype.pa=function(){return!!this.b[this.a++]},oe.prototype.readBool=oe.prototype.pa,oe.prototype.ra=function(){return this.da()},oe.prototype.readEnum=oe.prototype.ra,oe.prototype.fa=function(e){var t=this.b,i=this.a;e=i+e;for(var s=[],r="";i<e;){var n=t[i++];if(128>n)s.push(n);else{if(192>n)continue;if(224>n){var o=t[i++];s.push((31&n)<<6|63&o)}else if(240>n){o=t[i++];var a=t[i++];s.push((15&n)<<12|(63&o)<<6|63&a)}else 248>n&&(n=(7&n)<<18|(63&(o=t[i++]))<<12|(63&(a=t[i++]))<<6|63&t[i++],n-=65536,s.push(55296+(n>>10&1023),56320+(1023&n)))}8192<=s.length&&(r+=String.fromCharCode.apply(null,s),s.length=0)}return r+=S(s),this.a=i,r},oe.prototype.readString=oe.prototype.fa,oe.prototype.Dc=function(){var e=this.o();return this.fa(e)},oe.prototype.readStringWithLength=oe.prototype.Dc,oe.prototype.qa=function(e){if(0>e||this.a+e>this.b.length)return this.v=!0,E("Invalid byte length!"),new Uint8Array(0);var t=this.b.subarray(this.a,this.a+e);return this.a+=e,w(this.a<=this.c),t},oe.prototype.readBytes=oe.prototype.qa,oe.prototype.ia=function(){return this.w(Q)},oe.prototype.readVarintHash64=oe.prototype.ia,oe.prototype.$=function(){var e=this.b,t=this.a,i=e[t],s=e[t+1],r=e[t+2],n=e[t+3],o=e[t+4],a=e[t+5],l=e[t+6];return e=e[t+7],this.a+=8,String.fromCharCode(i,s,r,n,o,a,l,e)},oe.prototype.readFixedHash64=oe.prototype.$,p("jspb.BinaryReader",ce,void 0);var he=[];function pe(e,t,i){if(he.length){var s=he.pop();return e&&s.a.H(e,t,i),s}return new ce(e,t,i)}function ue(e,t){w(2==e.b);var i=e.a.o();i=e.a.B()+i;for(var s=[];e.a.B()<i;)s.push(t.call(e.a));return s}function de(e,t,i,s,r){this.ma=e,this.Ba=t,this.la=i,this.Na=s,this.na=r}function fe(e,t,i,s,r,n){this.Za=e,this.za=t,this.Aa=i,this.Wa=s,this.Ab=r,this.Nb=n}function ye(){}ce.clearInstanceCache=function(){he=[]},ce.getInstanceCacheLength=function(){return he.length},ce.alloc=pe,ce.prototype.zb=pe,ce.prototype.alloc=ce.prototype.zb,ce.prototype.Ca=function(){this.a.clear(),this.b=this.c=-1,this.h=!1,this.v=null,100>he.length&&he.push(this)},ce.prototype.free=ce.prototype.Ca,ce.prototype.Fb=function(){return this.O},ce.prototype.getFieldCursor=ce.prototype.Fb,ce.prototype.B=function(){return this.a.B()},ce.prototype.getCursor=ce.prototype.B,ce.prototype.Y=function(){return this.a.Y()},ce.prototype.getBuffer=ce.prototype.Y,ce.prototype.Hb=function(){return this.c},ce.prototype.getFieldNumber=ce.prototype.Hb,ce.prototype.Lb=function(){return this.b},ce.prototype.getWireType=ce.prototype.Lb,ce.prototype.Mb=function(){return 2==this.b},ce.prototype.isDelimited=ce.prototype.Mb,ce.prototype.bb=function(){return 4==this.b},ce.prototype.isEndGroup=ce.prototype.bb,ce.prototype.getError=function(){return this.h||this.a.getError()},ce.prototype.getError=ce.prototype.getError,ce.prototype.H=function(e,t,i){this.a.H(e,t,i),this.b=this.c=-1},ce.prototype.setBlock=ce.prototype.H,ce.prototype.reset=function(){this.a.reset(),this.b=this.c=-1},ce.prototype.reset=ce.prototype.reset,ce.prototype.advance=function(e){this.a.advance(e)},ce.prototype.advance=ce.prototype.advance,ce.prototype.oa=function(){if(this.a.ya())return!1;if(this.getError())return E("Decoder hit an error"),!1;this.O=this.a.B();var e=this.a.o(),t=e>>>3;return 0!=(e&=7)&&5!=e&&1!=e&&2!=e&&3!=e&&4!=e?(E("Invalid wire type: %s (at position %s)",e,this.O),this.h=!0,!1):(this.c=t,this.b=e,!0)},ce.prototype.nextField=ce.prototype.oa,ce.prototype.Oa=function(){this.a.mb(this.c<<3|this.b)},ce.prototype.unskipHeader=ce.prototype.Oa,ce.prototype.Lc=function(){var e=this.c;for(this.Oa();this.oa()&&this.c==e;)this.C();this.a.ya()||this.Oa()},ce.prototype.skipMatchingFields=ce.prototype.Lc,ce.prototype.lb=function(){0!=this.b?(E("Invalid wire type for skipVarintField"),this.C()):this.a.kb()},ce.prototype.skipVarintField=ce.prototype.lb,ce.prototype.gb=function(){if(2!=this.b)E("Invalid wire type for skipDelimitedField"),this.C();else{var e=this.a.o();this.a.advance(e)}},ce.prototype.skipDelimitedField=ce.prototype.gb,ce.prototype.hb=function(){5!=this.b?(E("Invalid wire type for skipFixed32Field"),this.C()):this.a.advance(4)},ce.prototype.skipFixed32Field=ce.prototype.hb,ce.prototype.ib=function(){1!=this.b?(E("Invalid wire type for skipFixed64Field"),this.C()):this.a.advance(8)},ce.prototype.skipFixed64Field=ce.prototype.ib,ce.prototype.jb=function(){for(var e=this.c;;){if(!this.oa()){E("Unmatched start-group tag: stream EOF"),this.h=!0;break}if(4==this.b){this.c!=e&&(E("Unmatched end-group tag"),this.h=!0);break}this.C()}},ce.prototype.skipGroup=ce.prototype.jb,ce.prototype.C=function(){switch(this.b){case 0:this.lb();break;case 1:this.ib();break;case 2:this.gb();break;case 5:this.hb();break;case 3:this.jb();break;default:E("Invalid wire encoding for field.")}},ce.prototype.skipField=ce.prototype.C,ce.prototype.Hc=function(e,t){null===this.v&&(this.v={}),w(!this.v[e]),this.v[e]=t},ce.prototype.registerReadCallback=ce.prototype.Hc,ce.prototype.Ic=function(e){return w(null!==this.v),w(e=this.v[e]),e(this)},ce.prototype.runReadCallback=ce.prototype.Ic,ce.prototype.Yb=function(e,t){w(2==this.b);var i=this.a.c,s=this.a.o();s=this.a.B()+s,this.a.setEnd(s),t(e,this),this.a.Ma(s),this.a.setEnd(i)},ce.prototype.readMessage=ce.prototype.Yb,ce.prototype.Ub=function(e,t,i){w(3==this.b),w(this.c==e),i(t,this),this.h||4==this.b||(E("Group submessage did not end with an END_GROUP tag"),this.h=!0)},ce.prototype.readGroup=ce.prototype.Ub,ce.prototype.Gb=function(){w(2==this.b);var e=this.a.o(),t=this.a.B(),i=t+e;return e=le(this.a.Y(),t,e),this.a.Ma(i),e},ce.prototype.getFieldDecoder=ce.prototype.Gb,ce.prototype.P=function(){return w(0==this.b),this.a.da()},ce.prototype.readInt32=ce.prototype.P,ce.prototype.Wb=function(){return w(0==this.b),this.a.Ea()},ce.prototype.readInt32String=ce.prototype.Wb,ce.prototype.ba=function(){return w(0==this.b),this.a.sa()},ce.prototype.readInt64=ce.prototype.ba,ce.prototype.ca=function(){return w(0==this.b),this.a.Fa()},ce.prototype.readInt64String=ce.prototype.ca,ce.prototype.m=function(){return w(0==this.b),this.a.o()},ce.prototype.readUint32=ce.prototype.m,ce.prototype.Fc=function(){return w(0==this.b),this.a.O()},ce.prototype.readUint32String=ce.prototype.Fc,ce.prototype.ga=function(){return w(0==this.b),this.a.Ga()},ce.prototype.readUint64=ce.prototype.ga,ce.prototype.ha=function(){return w(0==this.b),this.a.Ha()},ce.prototype.readUint64String=ce.prototype.ha,ce.prototype.zc=function(){return w(0==this.b),this.a.Ia()},ce.prototype.readSint32=ce.prototype.zc,ce.prototype.Ac=function(){return w(0==this.b),this.a.Ja()},ce.prototype.readSint64=ce.prototype.Ac,ce.prototype.Bc=function(){return w(0==this.b),this.a.Ka()},ce.prototype.readSint64String=ce.prototype.Bc,ce.prototype.Rb=function(){return w(5==this.b),this.a.m()},ce.prototype.readFixed32=ce.prototype.Rb,ce.prototype.Sb=function(){return w(1==this.b),this.a.ga()},ce.prototype.readFixed64=ce.prototype.Sb,ce.prototype.Tb=function(){return w(1==this.b),this.a.ha()},ce.prototype.readFixed64String=ce.prototype.Tb,ce.prototype.vc=function(){return w(5==this.b),this.a.P()},ce.prototype.readSfixed32=ce.prototype.vc,ce.prototype.wc=function(){return w(5==this.b),this.a.P().toString()},ce.prototype.readSfixed32String=ce.prototype.wc,ce.prototype.xc=function(){return w(1==this.b),this.a.ba()},ce.prototype.readSfixed64=ce.prototype.xc,ce.prototype.yc=function(){return w(1==this.b),this.a.ca()},ce.prototype.readSfixed64String=ce.prototype.yc,ce.prototype.aa=function(){return w(5==this.b),this.a.aa()},ce.prototype.readFloat=ce.prototype.aa,ce.prototype.Z=function(){return w(1==this.b),this.a.Z()},ce.prototype.readDouble=ce.prototype.Z,ce.prototype.pa=function(){return w(0==this.b),!!this.a.o()},ce.prototype.readBool=ce.prototype.pa,ce.prototype.ra=function(){return w(0==this.b),this.a.sa()},ce.prototype.readEnum=ce.prototype.ra,ce.prototype.fa=function(){w(2==this.b);var e=this.a.o();return this.a.fa(e)},ce.prototype.readString=ce.prototype.fa,ce.prototype.qa=function(){w(2==this.b);var e=this.a.o();return this.a.qa(e)},ce.prototype.readBytes=ce.prototype.qa,ce.prototype.ia=function(){return w(0==this.b),this.a.ia()},ce.prototype.readVarintHash64=ce.prototype.ia,ce.prototype.Cc=function(){return w(0==this.b),this.a.fb()},ce.prototype.readSintHash64=ce.prototype.Cc,ce.prototype.w=function(e){return w(0==this.b),this.a.w(e)},ce.prototype.readSplitVarint64=ce.prototype.w,ce.prototype.ea=function(e){return w(0==this.b),this.a.w((function(t,i){return K(t,i,e)}))},ce.prototype.readSplitZigzagVarint64=ce.prototype.ea,ce.prototype.$=function(){return w(1==this.b),this.a.$()},ce.prototype.readFixedHash64=ce.prototype.$,ce.prototype.ta=function(e){return w(1==this.b),this.a.ta(e)},ce.prototype.readSplitFixed64=ce.prototype.ta,ce.prototype.gc=function(){return ue(this,this.a.da)},ce.prototype.readPackedInt32=ce.prototype.gc,ce.prototype.hc=function(){return ue(this,this.a.Ea)},ce.prototype.readPackedInt32String=ce.prototype.hc,ce.prototype.ic=function(){return ue(this,this.a.sa)},ce.prototype.readPackedInt64=ce.prototype.ic,ce.prototype.jc=function(){return ue(this,this.a.Fa)},ce.prototype.readPackedInt64String=ce.prototype.jc,ce.prototype.qc=function(){return ue(this,this.a.o)},ce.prototype.readPackedUint32=ce.prototype.qc,ce.prototype.rc=function(){return ue(this,this.a.O)},ce.prototype.readPackedUint32String=ce.prototype.rc,ce.prototype.sc=function(){return ue(this,this.a.Ga)},ce.prototype.readPackedUint64=ce.prototype.sc,ce.prototype.tc=function(){return ue(this,this.a.Ha)},ce.prototype.readPackedUint64String=ce.prototype.tc,ce.prototype.nc=function(){return ue(this,this.a.Ia)},ce.prototype.readPackedSint32=ce.prototype.nc,ce.prototype.oc=function(){return ue(this,this.a.Ja)},ce.prototype.readPackedSint64=ce.prototype.oc,ce.prototype.pc=function(){return ue(this,this.a.Ka)},ce.prototype.readPackedSint64String=ce.prototype.pc,ce.prototype.bc=function(){return ue(this,this.a.m)},ce.prototype.readPackedFixed32=ce.prototype.bc,ce.prototype.cc=function(){return ue(this,this.a.ga)},ce.prototype.readPackedFixed64=ce.prototype.cc,ce.prototype.dc=function(){return ue(this,this.a.ha)},ce.prototype.readPackedFixed64String=ce.prototype.dc,ce.prototype.kc=function(){return ue(this,this.a.P)},ce.prototype.readPackedSfixed32=ce.prototype.kc,ce.prototype.lc=function(){return ue(this,this.a.ba)},ce.prototype.readPackedSfixed64=ce.prototype.lc,ce.prototype.mc=function(){return ue(this,this.a.ca)},ce.prototype.readPackedSfixed64String=ce.prototype.mc,ce.prototype.fc=function(){return ue(this,this.a.aa)},ce.prototype.readPackedFloat=ce.prototype.fc,ce.prototype.$b=function(){return ue(this,this.a.Z)},ce.prototype.readPackedDouble=ce.prototype.$b,ce.prototype.Zb=function(){return ue(this,this.a.pa)},ce.prototype.readPackedBool=ce.prototype.Zb,ce.prototype.ac=function(){return ue(this,this.a.ra)},ce.prototype.readPackedEnum=ce.prototype.ac,ce.prototype.uc=function(){return ue(this,this.a.ia)},ce.prototype.readPackedVarintHash64=ce.prototype.uc,ce.prototype.ec=function(){return ue(this,this.a.$)},ce.prototype.readPackedFixedHash64=ce.prototype.ec,p("jspb.ExtensionFieldInfo",de,void 0),p("jspb.ExtensionFieldBinaryInfo",fe,void 0),de.prototype.F=function(){return!!this.la},de.prototype.isMessageType=de.prototype.F,p("jspb.Message",ye,void 0),ye.GENERATE_TO_OBJECT=!0,ye.GENERATE_FROM_OBJECT=!0;var me="function"==typeof Uint8Array;ye.prototype.Ib=function(){return this.b},ye.prototype.getJsPbMessageId=ye.prototype.Ib,ye.initialize=function(e,t,i,s,r,n){if(e.f=null,t||(t=i?[i]:[]),e.b=i?String(i):void 0,e.D=0===i?-1:0,e.u=t,t=-1,!(i=e.u.length)||(t=i-1,null===(i=e.u[t])||"object"!=typeof i||Array.isArray(i)||me&&i instanceof Uint8Array)?-1<s?(e.G=Math.max(s,t+1-e.D),e.i=null):e.G=Number.MAX_VALUE:(e.G=t-e.D,e.i=i),e.a={},r)for(s=0;s<r.length;s++)(t=r[s])<e.G?(t+=e.D,e.u[t]=e.u[t]||ge):(_e(e),e.i[t]=e.i[t]||ge);if(n&&n.length)for(s=0;s<n.length;s++)Ce(e,n[s])};var ge=Object.freeze?Object.freeze([]):[];function _e(e){var t=e.G+e.D;e.u[t]||(e.i=e.u[t]={})}function we(e,t,i){for(var s=[],r=0;r<e.length;r++)s[r]=t.call(e[r],i,e[r]);return s}function Ee(e,t){if(t<e.G){t+=e.D;var i=e.u[t];return i===ge?e.u[t]=[]:i}if(e.i)return(i=e.i[t])===ge?e.i[t]=[]:i}function ve(e,t){return null==(e=Ee(e,t))?e:+e}function be(e,t){return null==(e=Ee(e,t))?e:!!e}function Me(e){return null==e||"string"==typeof e?e:me&&e instanceof Uint8Array?R(e):(E("Cannot coerce to b64 string: "+u(e)),null)}function Te(e){return null==e||e instanceof Uint8Array?e:"string"==typeof e?N(e):(E("Cannot coerce to Uint8Array: "+u(e)),null)}function Ie(e){if(e&&1<e.length){var t=u(e[0]);y(e,(function(e){u(e)!=t&&E("Inconsistent type in JSPB repeated field array. Got "+u(e)+" expected "+t)}))}}function Ae(e,t,i){return null==(e=Ee(e,t))?i:e}function Oe(e,t,i){return v(e,ye),t<e.G?e.u[t+e.D]=i:(_e(e),e.i[t]=i),e}function Se(e,t,i,s){return v(e,ye),i!==s?Oe(e,t,i):t<e.G?e.u[t+e.D]=null:(_e(e),delete e.i[t]),e}function Fe(e,t,i,s){return v(e,ye),(i=Ce(e,i))&&i!==t&&void 0!==s&&(e.f&&i in e.f&&(e.f[i]=void 0),Oe(e,i,void 0)),Oe(e,t,s)}function Ce(e,t){for(var i,s,r=0;r<t.length;r++){var n=t[r],o=Ee(e,n);null!=o&&(i=n,s=o,Oe(e,n,void 0))}return i?(Oe(e,i,s),i):0}function ke(e,t,i){if(e.f||(e.f={}),!e.f[i]){for(var s=Ee(e,i),r=[],n=0;n<s.length;n++)r[n]=new t(s[n]);e.f[i]=r}}function De(e){if(e.f)for(var t in e.f){var i=e.f[t];if(Array.isArray(i))for(var s=0;s<i.length;s++)i[s]&&i[s].g();else i&&i.g()}}function Re(e,t){e=e||{},t=t||{};var i,s={};for(i in e)s[i]=0;for(i in t)s[i]=0;for(i in s)if(!Ne(e[i],t[i]))return!1;return!0}function Ne(e,t){if(e==t)return!0;if(!d(e)||!d(t))return!!("number"==typeof e&&isNaN(e)||"number"==typeof t&&isNaN(t))&&String(e)==String(t);if(e.constructor!=t.constructor)return!1;if(me&&e.constructor===Uint8Array){if(e.length!=t.length)return!1;for(var i=0;i<e.length;i++)if(e[i]!=t[i])return!1;return!0}if(e.constructor===Array){var s=void 0,r=void 0,n=Math.max(e.length,t.length);for(i=0;i<n;i++){var o=e[i],a=t[i];if(o&&o.constructor==Object&&(w(void 0===s),w(i===e.length-1),s=o,o=void 0),a&&a.constructor==Object&&(w(void 0===r),w(i===t.length-1),r=a,a=void 0),!Ne(o,a))return!1}return!s&&!r||Re(s=s||{},r=r||{})}if(e.constructor===Object)return Re(e,t);throw Error("Invalid type in JSPB array")}function Pe(e){return new e.constructor(ze(e.g()))}function ze(e){if(Array.isArray(e)){for(var t=Array(e.length),i=0;i<e.length;i++){var s=e[i];null!=s&&(t[i]="object"==typeof s?ze(w(s)):s)}return t}if(me&&e instanceof Uint8Array)return new Uint8Array(e);for(i in t={},e)null!=(s=e[i])&&(t[i]="object"==typeof s?ze(w(s)):s);return t}ye.toObjectList=we,ye.toObjectExtension=function(e,t,i,s,r){for(var n in i){var o=i[n],a=s.call(e,o);if(null!=a){for(var l in o.Ba)if(o.Ba.hasOwnProperty(l))break;t[l]=o.Na?o.na?we(a,o.Na,r):o.Na(r,a):a}}},ye.serializeBinaryExtensions=function(e,t,i,s){for(var r in i){var n=i[r],o=n.Za;if(!n.Aa)throw Error("Message extension present that was generated without binary serialization support");var a=s.call(e,o);if(null!=a)if(o.F()){if(!n.Wa)throw Error("Message extension present holding submessage without binary support enabled, and message is being serialized to binary format");n.Aa.call(t,o.ma,a,n.Wa)}else n.Aa.call(t,o.ma,a)}},ye.readBinaryExtension=function(e,t,i,s,r){var n=i[t.c];if(n){if(i=n.Za,!n.za)throw Error("Deserializing extension whose generated code does not support binary format");if(i.F()){var o=new i.la;n.za.call(t,o,n.Ab)}else o=n.za.call(t);i.na&&!n.Nb?(t=s.call(e,i))?t.push(o):r.call(e,i,[o]):r.call(e,i,o)}else t.C()},ye.getField=Ee,ye.getRepeatedField=function(e,t){return Ee(e,t)},ye.getOptionalFloatingPointField=ve,ye.getBooleanField=be,ye.getRepeatedFloatingPointField=function(e,t){var i=Ee(e,t);if(e.a||(e.a={}),!e.a[t]){for(var s=0;s<i.length;s++)i[s]=+i[s];e.a[t]=!0}return i},ye.getRepeatedBooleanField=function(e,t){var i=Ee(e,t);if(e.a||(e.a={}),!e.a[t]){for(var s=0;s<i.length;s++)i[s]=!!i[s];e.a[t]=!0}return i},ye.bytesAsB64=Me,ye.bytesAsU8=Te,ye.bytesListAsB64=function(e){return Ie(e),e.length&&"string"!=typeof e[0]?m(e,Me):e},ye.bytesListAsU8=function(e){return Ie(e),!e.length||e[0]instanceof Uint8Array?e:m(e,Te)},ye.getFieldWithDefault=Ae,ye.getBooleanFieldWithDefault=function(e,t,i){return null==(e=be(e,t))?i:e},ye.getFloatingPointFieldWithDefault=function(e,t,i){return null==(e=ve(e,t))?i:e},ye.getFieldProto3=Ae,ye.getMapField=function(e,t,i,s){if(e.f||(e.f={}),t in e.f)return e.f[t];var r=Ee(e,t);if(!r){if(i)return;Oe(e,t,r=[])}return e.f[t]=new M(r,s)},ye.setField=Oe,ye.setProto3IntField=function(e,t,i){return Se(e,t,i,0)},ye.setProto3FloatField=function(e,t,i){return Se(e,t,i,0)},ye.setProto3BooleanField=function(e,t,i){return Se(e,t,i,!1)},ye.setProto3StringField=function(e,t,i){return Se(e,t,i,"")},ye.setProto3BytesField=function(e,t,i){return Se(e,t,i,"")},ye.setProto3EnumField=function(e,t,i){return Se(e,t,i,0)},ye.setProto3StringIntField=function(e,t,i){return Se(e,t,i,"0")},ye.addToRepeatedField=function(e,t,i,s){return v(e,ye),t=Ee(e,t),null!=s?t.splice(s,0,i):t.push(i),e},ye.setOneofField=Fe,ye.computeOneofCase=Ce,ye.getWrapperField=function(e,t,i,s){if(e.f||(e.f={}),!e.f[i]){var r=Ee(e,i);(s||r)&&(e.f[i]=new t(r))}return e.f[i]},ye.getRepeatedWrapperField=function(e,t,i){return ke(e,t,i),(t=e.f[i])==ge&&(t=e.f[i]=[]),t},ye.setWrapperField=function(e,t,i){v(e,ye),e.f||(e.f={});var s=i?i.g():i;return e.f[t]=i,Oe(e,t,s)},ye.setOneofWrapperField=function(e,t,i,s){v(e,ye),e.f||(e.f={});var r=s?s.g():s;return e.f[t]=s,Fe(e,t,i,r)},ye.setRepeatedWrapperField=function(e,t,i){v(e,ye),e.f||(e.f={}),i=i||[];for(var s=[],r=0;r<i.length;r++)s[r]=i[r].g();return e.f[t]=i,Oe(e,t,s)},ye.addToRepeatedWrapperField=function(e,t,i,s,r){ke(e,s,t);var n=e.f[t];return n||(n=e.f[t]=[]),i=i||new s,e=Ee(e,t),null!=r?(n.splice(r,0,i),e.splice(r,0,i.g())):(n.push(i),e.push(i.g())),i},ye.toMap=function(e,t,i,s){for(var r={},n=0;n<e.length;n++)r[t.call(e[n])]=i?i.call(e[n],s,e[n]):e[n];return r},ye.prototype.g=function(){return De(this),this.u},ye.prototype.toArray=ye.prototype.g,ye.prototype.toString=function(){return De(this),this.u.toString()},ye.prototype.getExtension=function(e){if(this.i){this.f||(this.f={});var t=e.ma;if(e.na){if(e.F())return this.f[t]||(this.f[t]=m(this.i[t]||[],(function(t){return new e.la(t)}))),this.f[t]}else if(e.F())return!this.f[t]&&this.i[t]&&(this.f[t]=new e.la(this.i[t])),this.f[t];return this.i[t]}},ye.prototype.getExtension=ye.prototype.getExtension,ye.prototype.Kc=function(e,t){this.f||(this.f={}),_e(this);var i=e.ma;return e.na?(t=t||[],e.F()?(this.f[i]=t,this.i[i]=m(t,(function(e){return e.g()}))):this.i[i]=t):e.F()?(this.f[i]=t,this.i[i]=t?t.g():t):this.i[i]=t,this},ye.prototype.setExtension=ye.prototype.Kc,ye.difference=function(e,t){if(!(e instanceof t.constructor))throw Error("Messages have different types.");var i=e.g();t=t.g();var s=[],r=0,n=i.length>t.length?i.length:t.length;for(e.b&&(s[0]=e.b,r=1);r<n;r++)Ne(i[r],t[r])||(s[r]=t[r]);return new e.constructor(s)},ye.equals=function(e,t){return e==t||!(!e||!t)&&e instanceof t.constructor&&Ne(e.g(),t.g())},ye.compareExtensions=Re,ye.compareFields=Ne,ye.prototype.Bb=function(){return Pe(this)},ye.prototype.cloneMessage=ye.prototype.Bb,ye.prototype.clone=function(){return Pe(this)},ye.prototype.clone=ye.prototype.clone,ye.clone=function(e){return Pe(e)},ye.copyInto=function(e,t){v(e,ye),v(t,ye),w(e.constructor==t.constructor,"Copy source and target message should have the same type."),e=Pe(e);for(var i=t.g(),s=e.g(),r=i.length=0;r<s.length;r++)i[r]=s[r];t.f=e.f,t.i=e.i},ye.registerMessageType=function(e,t){t.we=e};var Be={dump:function(e){return v(e,ye,"jspb.Message instance expected"),w(e.getExtension,"Only unobfuscated and unoptimized compilation modes supported."),Be.X(e)}};function We(){this.a=[]}function xe(e,t){this.lo=e,this.hi=t}function Le(e,t){var i=65535&e,s=65535&t,r=t>>>16;for(t=i*s+65536*(i*r&65535)+65536*((e>>>=16)*s&65535),i=e*r+(i*r>>>16)+(e*s>>>16);4294967296<=t;)t-=4294967296,i+=1;return new xe(t>>>0,i>>>0)}function je(e){for(var t=new xe(0,0),i=new xe(0,0),s=0;s<e.length;s++){if("0">e[s]||"9"<e[s])return null;i.lo=parseInt(e[s],10),t=t.eb(10).add(i)}return t}function Ge(e,t){this.lo=e,this.hi=t}function Ve(e){var t=0<e.length&&"-"==e[0];return t&&(e=e.substring(1)),null===(e=je(e))?null:(t&&(e=new xe(0,0).sub(e)),new Ge(e.lo,e.hi))}function Ue(){this.c=[],this.b=0,this.a=new We,this.h=[]}function Ye(e,t){var i=e.a.end();e.c.push(i),e.c.push(t),e.b+=i.length+t.length}function He(e,t){return Ke(e,t,2),t=e.a.end(),e.c.push(t),e.b+=t.length,t.push(e.b),t}function qe(e,t){var i=t.pop();for(w(0<=(i=e.b+e.a.length()-i));127<i;)t.push(127&i|128),i>>>=7,e.b++;t.push(i),e.b++}function Ke(e,t,i){w(1<=t&&t==Math.floor(t)),e.a.j(8*t+i)}function Xe(e,t,i){null!=i&&(Ke(e,t,0),e.a.j(i))}function Je(e,t,i){null!=i&&(Ke(e,t,0),e.a.M(i))}p("jspb.debug.dump",Be.dump,void 0),Be.X=function(e){var t=u(e);if("number"==t||"string"==t||"boolean"==t||"null"==t||"undefined"==t||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return e;if("array"==t)return function(e,t,i){for(var s=[],r=2;r<arguments.length;++r)s[r-2]=arguments[r];Array.isArray(e)||_("Expected array but got %s: %s.",[u(e),e],t,s)}(e),m(e,Be.X);if(e instanceof M){for(var i={},s=(e=e.entries()).next();!s.done;s=e.next())i[s.value[0]]=Be.X(s.value[1]);return i}v(e,ye,"Only messages expected: "+e);var r={$name:(t=e.constructor).name||t.displayName};for(a in t.prototype){var n=/^get([A-Z]\w*)/.exec(a);if(n&&"getExtension"!=a&&"getJsPbMessageId"!=a){var o="has"+n[1];e[o]&&!e[o]()||(o=e[a](),r[Be.$a(n[1])]=Be.X(o))}}if(e.extensionObject_)return r.$extensions="Recursive dumping of extensions not supported in compiled code. Switch to uncompiled or dump extension object directly",r;for(s in t.extensions)if(/^\d+$/.test(s)){o=t.extensions[s];var a=e.getExtension(o);n=void 0,o=o.Ba;var l=[],c=0;for(n in o)l[c++]=n;n=l[0],null!=a&&(i||(i=r.$extensions={}),i[Be.$a(n)]=Be.X(a))}return r},Be.$a=function(e){return e.replace(/^[A-Z]/,(function(e){return e.toLowerCase()}))},p("jspb.BinaryEncoder",We,void 0),We.prototype.length=function(){return this.a.length},We.prototype.length=We.prototype.length,We.prototype.end=function(){var e=this.a;return this.a=[],e},We.prototype.end=We.prototype.end,We.prototype.l=function(e,t){for(w(e==Math.floor(e)),w(t==Math.floor(t)),w(0<=e&&4294967296>e),w(0<=t&&4294967296>t);0<t||127<e;)this.a.push(127&e|128),e=(e>>>7|t<<25)>>>0,t>>>=7;this.a.push(e)},We.prototype.writeSplitVarint64=We.prototype.l,We.prototype.A=function(e,t){w(e==Math.floor(e)),w(t==Math.floor(t)),w(0<=e&&4294967296>e),w(0<=t&&4294967296>t),this.s(e),this.s(t)},We.prototype.writeSplitFixed64=We.prototype.A,We.prototype.j=function(e){for(w(e==Math.floor(e)),w(0<=e&&4294967296>e);127<e;)this.a.push(127&e|128),e>>>=7;this.a.push(e)},We.prototype.writeUnsignedVarint32=We.prototype.j,We.prototype.M=function(e){if(w(e==Math.floor(e)),w(-2147483648<=e&&2147483648>e),0<=e)this.j(e);else{for(var t=0;9>t;t++)this.a.push(127&e|128),e>>=7;this.a.push(1)}},We.prototype.writeSignedVarint32=We.prototype.M,We.prototype.va=function(e){w(e==Math.floor(e)),w(0<=e&&0x10000000000000000>e),x(e),this.l(z,B)},We.prototype.writeUnsignedVarint64=We.prototype.va,We.prototype.ua=function(e){w(e==Math.floor(e)),w(-0x8000000000000000<=e&&0x8000000000000000>e),x(e),this.l(z,B)},We.prototype.writeSignedVarint64=We.prototype.ua,We.prototype.wa=function(e){w(e==Math.floor(e)),w(-2147483648<=e&&2147483648>e),this.j((e<<1^e>>31)>>>0)},We.prototype.writeZigzagVarint32=We.prototype.wa,We.prototype.xa=function(e){w(e==Math.floor(e)),w(-0x8000000000000000<=e&&0x8000000000000000>e),L(e),this.l(z,B)},We.prototype.writeZigzagVarint64=We.prototype.xa,We.prototype.Ta=function(e){this.W(te(e))},We.prototype.writeZigzagVarint64String=We.prototype.Ta,We.prototype.W=function(e){var t=this;V(e),H(z,B,(function(e,i){t.l(e>>>0,i>>>0)}))},We.prototype.writeZigzagVarintHash64=We.prototype.W,We.prototype.be=function(e){w(e==Math.floor(e)),w(0<=e&&256>e),this.a.push(e>>>0&255)},We.prototype.writeUint8=We.prototype.be,We.prototype.ae=function(e){w(e==Math.floor(e)),w(0<=e&&65536>e),this.a.push(e>>>0&255),this.a.push(e>>>8&255)},We.prototype.writeUint16=We.prototype.ae,We.prototype.s=function(e){w(e==Math.floor(e)),w(0<=e&&4294967296>e),this.a.push(e>>>0&255),this.a.push(e>>>8&255),this.a.push(e>>>16&255),this.a.push(e>>>24&255)},We.prototype.writeUint32=We.prototype.s,We.prototype.V=function(e){w(e==Math.floor(e)),w(0<=e&&0x10000000000000000>e),W(e),this.s(z),this.s(B)},We.prototype.writeUint64=We.prototype.V,We.prototype.Qc=function(e){w(e==Math.floor(e)),w(-128<=e&&128>e),this.a.push(e>>>0&255)},We.prototype.writeInt8=We.prototype.Qc,We.prototype.Pc=function(e){w(e==Math.floor(e)),w(-32768<=e&&32768>e),this.a.push(e>>>0&255),this.a.push(e>>>8&255)},We.prototype.writeInt16=We.prototype.Pc,We.prototype.S=function(e){w(e==Math.floor(e)),w(-2147483648<=e&&2147483648>e),this.a.push(e>>>0&255),this.a.push(e>>>8&255),this.a.push(e>>>16&255),this.a.push(e>>>24&255)},We.prototype.writeInt32=We.prototype.S,We.prototype.T=function(e){w(e==Math.floor(e)),w(-0x8000000000000000<=e&&0x8000000000000000>e),x(e),this.A(z,B)},We.prototype.writeInt64=We.prototype.T,We.prototype.ka=function(e){w(e==Math.floor(e)),w(-0x8000000000000000<=+e&&0x8000000000000000>+e),V(te(e)),this.A(z,B)},We.prototype.writeInt64String=We.prototype.ka,We.prototype.L=function(e){w(1/0===e||-1/0===e||isNaN(e)||-34028234663852886e22<=e&&34028234663852886e22>=e),j(e),this.s(z)},We.prototype.writeFloat=We.prototype.L,We.prototype.J=function(e){w(1/0===e||-1/0===e||isNaN(e)||-17976931348623157e292<=e&&17976931348623157e292>=e),G(e),this.s(z),this.s(B)},We.prototype.writeDouble=We.prototype.J,We.prototype.I=function(e){w("boolean"==typeof e||"number"==typeof e),this.a.push(e?1:0)},We.prototype.writeBool=We.prototype.I,We.prototype.R=function(e){w(e==Math.floor(e)),w(-2147483648<=e&&2147483648>e),this.M(e)},We.prototype.writeEnum=We.prototype.R,We.prototype.ja=function(e){this.a.push.apply(this.a,e)},We.prototype.writeBytes=We.prototype.ja,We.prototype.N=function(e){V(e),this.l(z,B)},We.prototype.writeVarintHash64=We.prototype.N,We.prototype.K=function(e){V(e),this.s(z),this.s(B)},We.prototype.writeFixedHash64=We.prototype.K,We.prototype.U=function(e){var t=this.a.length;!function(e,t,i){for(var s=[],r=2;r<arguments.length;++r)s[r-2]=arguments[r];"string"!=typeof e&&_("Expected string but got %s: %s.",[u(e),e],t,s)}(e);for(var i=0;i<e.length;i++){var s=e.charCodeAt(i);if(128>s)this.a.push(s);else if(2048>s)this.a.push(s>>6|192),this.a.push(63&s|128);else if(65536>s)if(55296<=s&&56319>=s&&i+1<e.length){var r=e.charCodeAt(i+1);56320<=r&&57343>=r&&(s=1024*(s-55296)+r-56320+65536,this.a.push(s>>18|240),this.a.push(s>>12&63|128),this.a.push(s>>6&63|128),this.a.push(63&s|128),i++)}else this.a.push(s>>12|224),this.a.push(s>>6&63|128),this.a.push(63&s|128)}return this.a.length-t},We.prototype.writeString=We.prototype.U,p("jspb.arith.UInt64",xe,void 0),xe.prototype.cmp=function(e){return this.hi<e.hi||this.hi==e.hi&&this.lo<e.lo?-1:this.hi==e.hi&&this.lo==e.lo?0:1},xe.prototype.cmp=xe.prototype.cmp,xe.prototype.La=function(){return new xe((this.lo>>>1|(1&this.hi)<<31)>>>0,this.hi>>>1>>>0)},xe.prototype.rightShift=xe.prototype.La,xe.prototype.Da=function(){return new xe(this.lo<<1>>>0,(this.hi<<1|this.lo>>>31)>>>0)},xe.prototype.leftShift=xe.prototype.Da,xe.prototype.cb=function(){return!!(2147483648&this.hi)},xe.prototype.msb=xe.prototype.cb,xe.prototype.Ob=function(){return!!(1&this.lo)},xe.prototype.lsb=xe.prototype.Ob,xe.prototype.Ua=function(){return 0==this.lo&&0==this.hi},xe.prototype.zero=xe.prototype.Ua,xe.prototype.add=function(e){return new xe((this.lo+e.lo&4294967295)>>>0>>>0,((this.hi+e.hi&4294967295)>>>0)+(4294967296<=this.lo+e.lo?1:0)>>>0)},xe.prototype.add=xe.prototype.add,xe.prototype.sub=function(e){return new xe((this.lo-e.lo&4294967295)>>>0>>>0,((this.hi-e.hi&4294967295)>>>0)-(0>this.lo-e.lo?1:0)>>>0)},xe.prototype.sub=xe.prototype.sub,xe.mul32x32=Le,xe.prototype.eb=function(e){var t=Le(this.lo,e);return(e=Le(this.hi,e)).hi=e.lo,e.lo=0,t.add(e)},xe.prototype.mul=xe.prototype.eb,xe.prototype.Xa=function(e){if(0==e)return[];var t=new xe(0,0),i=new xe(this.lo,this.hi);e=new xe(e,0);for(var s=new xe(1,0);!e.cb();)e=e.Da(),s=s.Da();for(;!s.Ua();)0>=e.cmp(i)&&(t=t.add(s),i=i.sub(e)),e=e.La(),s=s.La();return[t,i]},xe.prototype.div=xe.prototype.Xa,xe.prototype.toString=function(){for(var e="",t=this;!t.Ua();){var i=(t=t.Xa(10))[0];e=t[1].lo+e,t=i}return""==e&&(e="0"),e},xe.prototype.toString=xe.prototype.toString,xe.fromString=je,xe.prototype.clone=function(){return new xe(this.lo,this.hi)},xe.prototype.clone=xe.prototype.clone,p("jspb.arith.Int64",Ge,void 0),Ge.prototype.add=function(e){return new Ge((this.lo+e.lo&4294967295)>>>0>>>0,((this.hi+e.hi&4294967295)>>>0)+(4294967296<=this.lo+e.lo?1:0)>>>0)},Ge.prototype.add=Ge.prototype.add,Ge.prototype.sub=function(e){return new Ge((this.lo-e.lo&4294967295)>>>0>>>0,((this.hi-e.hi&4294967295)>>>0)-(0>this.lo-e.lo?1:0)>>>0)},Ge.prototype.sub=Ge.prototype.sub,Ge.prototype.clone=function(){return new Ge(this.lo,this.hi)},Ge.prototype.clone=Ge.prototype.clone,Ge.prototype.toString=function(){var e=!!(2147483648&this.hi),t=new xe(this.lo,this.hi);return e&&(t=new xe(0,0).sub(t)),(e?"-":"")+t.toString()},Ge.prototype.toString=Ge.prototype.toString,Ge.fromString=Ve,p("jspb.BinaryWriter",Ue,void 0),Ue.prototype.pb=function(e,t,i){Ye(this,e.subarray(t,i))},Ue.prototype.writeSerializedMessage=Ue.prototype.pb,Ue.prototype.Pb=function(e,t,i){null!=e&&null!=t&&null!=i&&this.pb(e,t,i)},Ue.prototype.maybeWriteSerializedMessage=Ue.prototype.Pb,Ue.prototype.reset=function(){this.c=[],this.a.end(),this.b=0,this.h=[]},Ue.prototype.reset=Ue.prototype.reset,Ue.prototype.ab=function(){w(0==this.h.length);for(var e=new Uint8Array(this.b+this.a.length()),t=this.c,i=t.length,s=0,r=0;r<i;r++){var n=t[r];e.set(n,s),s+=n.length}return t=this.a.end(),e.set(t,s),w((s+=t.length)==e.length),this.c=[e],e},Ue.prototype.getResultBuffer=Ue.prototype.ab,Ue.prototype.Kb=function(e){return R(this.ab(),e)},Ue.prototype.getResultBase64String=Ue.prototype.Kb,Ue.prototype.Va=function(e){this.h.push(He(this,e))},Ue.prototype.beginSubMessage=Ue.prototype.Va,Ue.prototype.Ya=function(){w(0<=this.h.length),qe(this,this.h.pop())},Ue.prototype.endSubMessage=Ue.prototype.Ya,Ue.prototype.Nc=function(e,t,i){switch(e){case 1:this.J(t,i);break;case 2:this.L(t,i);break;case 3:this.T(t,i);break;case 4:this.V(t,i);break;case 5:this.S(t,i);break;case 6:this.Qa(t,i);break;case 7:this.Pa(t,i);break;case 8:this.I(t,i);break;case 9:this.U(t,i);break;case 10:E("Group field type not supported in writeAny()");break;case 11:E("Message field type not supported in writeAny()");break;case 12:this.ja(t,i);break;case 13:this.s(t,i);break;case 14:this.R(t,i);break;case 15:this.Ra(t,i);break;case 16:this.Sa(t,i);break;case 17:this.rb(t,i);break;case 18:this.sb(t,i);break;case 30:this.K(t,i);break;case 31:this.N(t,i);break;default:E("Invalid field type in writeAny()")}},Ue.prototype.writeAny=Ue.prototype.Nc,Ue.prototype.S=function(e,t){null!=t&&(w(-2147483648<=t&&2147483648>t),Je(this,e,t))},Ue.prototype.writeInt32=Ue.prototype.S,Ue.prototype.ob=function(e,t){null!=t&&(w(-2147483648<=(t=parseInt(t,10))&&2147483648>t),Je(this,e,t))},Ue.prototype.writeInt32String=Ue.prototype.ob,Ue.prototype.T=function(e,t){null!=t&&(w(-0x8000000000000000<=t&&0x8000000000000000>t),null!=t&&(Ke(this,e,0),this.a.ua(t)))},Ue.prototype.writeInt64=Ue.prototype.T,Ue.prototype.ka=function(e,t){null!=t&&(t=Ve(t),Ke(this,e,0),this.a.l(t.lo,t.hi))},Ue.prototype.writeInt64String=Ue.prototype.ka,Ue.prototype.s=function(e,t){null!=t&&(w(0<=t&&4294967296>t),Xe(this,e,t))},Ue.prototype.writeUint32=Ue.prototype.s,Ue.prototype.ub=function(e,t){null!=t&&(w(0<=(t=parseInt(t,10))&&4294967296>t),Xe(this,e,t))},Ue.prototype.writeUint32String=Ue.prototype.ub,Ue.prototype.V=function(e,t){null!=t&&(w(0<=t&&0x10000000000000000>t),null!=t&&(Ke(this,e,0),this.a.va(t)))},Ue.prototype.writeUint64=Ue.prototype.V,Ue.prototype.vb=function(e,t){null!=t&&(t=je(t),Ke(this,e,0),this.a.l(t.lo,t.hi))},Ue.prototype.writeUint64String=Ue.prototype.vb,Ue.prototype.rb=function(e,t){null!=t&&(w(-2147483648<=t&&2147483648>t),null!=t&&(Ke(this,e,0),this.a.wa(t)))},Ue.prototype.writeSint32=Ue.prototype.rb,Ue.prototype.sb=function(e,t){null!=t&&(w(-0x8000000000000000<=t&&0x8000000000000000>t),null!=t&&(Ke(this,e,0),this.a.xa(t)))},Ue.prototype.writeSint64=Ue.prototype.sb,Ue.prototype.$d=function(e,t){null!=t&&null!=t&&(Ke(this,e,0),this.a.W(t))},Ue.prototype.writeSintHash64=Ue.prototype.$d,Ue.prototype.Zd=function(e,t){null!=t&&null!=t&&(Ke(this,e,0),this.a.Ta(t))},Ue.prototype.writeSint64String=Ue.prototype.Zd,Ue.prototype.Pa=function(e,t){null!=t&&(w(0<=t&&4294967296>t),Ke(this,e,5),this.a.s(t))},Ue.prototype.writeFixed32=Ue.prototype.Pa,Ue.prototype.Qa=function(e,t){null!=t&&(w(0<=t&&0x10000000000000000>t),Ke(this,e,1),this.a.V(t))},Ue.prototype.writeFixed64=Ue.prototype.Qa,Ue.prototype.nb=function(e,t){null!=t&&(t=je(t),Ke(this,e,1),this.a.A(t.lo,t.hi))},Ue.prototype.writeFixed64String=Ue.prototype.nb,Ue.prototype.Ra=function(e,t){null!=t&&(w(-2147483648<=t&&2147483648>t),Ke(this,e,5),this.a.S(t))},Ue.prototype.writeSfixed32=Ue.prototype.Ra,Ue.prototype.Sa=function(e,t){null!=t&&(w(-0x8000000000000000<=t&&0x8000000000000000>t),Ke(this,e,1),this.a.T(t))},Ue.prototype.writeSfixed64=Ue.prototype.Sa,Ue.prototype.qb=function(e,t){null!=t&&(t=Ve(t),Ke(this,e,1),this.a.A(t.lo,t.hi))},Ue.prototype.writeSfixed64String=Ue.prototype.qb,Ue.prototype.L=function(e,t){null!=t&&(Ke(this,e,5),this.a.L(t))},Ue.prototype.writeFloat=Ue.prototype.L,Ue.prototype.J=function(e,t){null!=t&&(Ke(this,e,1),this.a.J(t))},Ue.prototype.writeDouble=Ue.prototype.J,Ue.prototype.I=function(e,t){null!=t&&(w("boolean"==typeof t||"number"==typeof t),Ke(this,e,0),this.a.I(t))},Ue.prototype.writeBool=Ue.prototype.I,Ue.prototype.R=function(e,t){null!=t&&(w(-2147483648<=t&&2147483648>t),Ke(this,e,0),this.a.M(t))},Ue.prototype.writeEnum=Ue.prototype.R,Ue.prototype.U=function(e,t){null!=t&&(e=He(this,e),this.a.U(t),qe(this,e))},Ue.prototype.writeString=Ue.prototype.U,Ue.prototype.ja=function(e,t){null!=t&&(t=ne(t),Ke(this,e,2),this.a.j(t.length),Ye(this,t))},Ue.prototype.writeBytes=Ue.prototype.ja,Ue.prototype.Rc=function(e,t,i){null!=t&&(e=He(this,e),i(t,this),qe(this,e))},Ue.prototype.writeMessage=Ue.prototype.Rc,Ue.prototype.Sc=function(e,t,i){null!=t&&(Ke(this,1,3),Ke(this,2,0),this.a.M(e),e=He(this,3),i(t,this),qe(this,e),Ke(this,1,4))},Ue.prototype.writeMessageSet=Ue.prototype.Sc,Ue.prototype.Oc=function(e,t,i){null!=t&&(Ke(this,e,3),i(t,this),Ke(this,e,4))},Ue.prototype.writeGroup=Ue.prototype.Oc,Ue.prototype.K=function(e,t){null!=t&&(w(8==t.length),Ke(this,e,1),this.a.K(t))},Ue.prototype.writeFixedHash64=Ue.prototype.K,Ue.prototype.N=function(e,t){null!=t&&(w(8==t.length),Ke(this,e,0),this.a.N(t))},Ue.prototype.writeVarintHash64=Ue.prototype.N,Ue.prototype.A=function(e,t,i){Ke(this,e,1),this.a.A(t,i)},Ue.prototype.writeSplitFixed64=Ue.prototype.A,Ue.prototype.l=function(e,t,i){Ke(this,e,0),this.a.l(t,i)},Ue.prototype.writeSplitVarint64=Ue.prototype.l,Ue.prototype.tb=function(e,t,i){Ke(this,e,0);var s=this.a;H(t,i,(function(e,t){s.l(e>>>0,t>>>0)}))},Ue.prototype.writeSplitZigzagVarint64=Ue.prototype.tb,Ue.prototype.Ed=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)Je(this,e,t[i])},Ue.prototype.writeRepeatedInt32=Ue.prototype.Ed,Ue.prototype.Fd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.ob(e,t[i])},Ue.prototype.writeRepeatedInt32String=Ue.prototype.Fd,Ue.prototype.Gd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++){var s=t[i];null!=s&&(Ke(this,e,0),this.a.ua(s))}},Ue.prototype.writeRepeatedInt64=Ue.prototype.Gd,Ue.prototype.Qd=function(e,t,i,s){if(null!=t)for(var r=0;r<t.length;r++)this.A(e,i(t[r]),s(t[r]))},Ue.prototype.writeRepeatedSplitFixed64=Ue.prototype.Qd,Ue.prototype.Rd=function(e,t,i,s){if(null!=t)for(var r=0;r<t.length;r++)this.l(e,i(t[r]),s(t[r]))},Ue.prototype.writeRepeatedSplitVarint64=Ue.prototype.Rd,Ue.prototype.Sd=function(e,t,i,s){if(null!=t)for(var r=0;r<t.length;r++)this.tb(e,i(t[r]),s(t[r]))},Ue.prototype.writeRepeatedSplitZigzagVarint64=Ue.prototype.Sd,Ue.prototype.Hd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.ka(e,t[i])},Ue.prototype.writeRepeatedInt64String=Ue.prototype.Hd,Ue.prototype.Ud=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)Xe(this,e,t[i])},Ue.prototype.writeRepeatedUint32=Ue.prototype.Ud,Ue.prototype.Vd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.ub(e,t[i])},Ue.prototype.writeRepeatedUint32String=Ue.prototype.Vd,Ue.prototype.Wd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++){var s=t[i];null!=s&&(Ke(this,e,0),this.a.va(s))}},Ue.prototype.writeRepeatedUint64=Ue.prototype.Wd,Ue.prototype.Xd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.vb(e,t[i])},Ue.prototype.writeRepeatedUint64String=Ue.prototype.Xd,Ue.prototype.Md=function(e,t){if(null!=t)for(var i=0;i<t.length;i++){var s=t[i];null!=s&&(Ke(this,e,0),this.a.wa(s))}},Ue.prototype.writeRepeatedSint32=Ue.prototype.Md,Ue.prototype.Nd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++){var s=t[i];null!=s&&(Ke(this,e,0),this.a.xa(s))}},Ue.prototype.writeRepeatedSint64=Ue.prototype.Nd,Ue.prototype.Od=function(e,t){if(null!=t)for(var i=0;i<t.length;i++){var s=t[i];null!=s&&(Ke(this,e,0),this.a.Ta(s))}},Ue.prototype.writeRepeatedSint64String=Ue.prototype.Od,Ue.prototype.Pd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++){var s=t[i];null!=s&&(Ke(this,e,0),this.a.W(s))}},Ue.prototype.writeRepeatedSintHash64=Ue.prototype.Pd,Ue.prototype.yd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.Pa(e,t[i])},Ue.prototype.writeRepeatedFixed32=Ue.prototype.yd,Ue.prototype.zd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.Qa(e,t[i])},Ue.prototype.writeRepeatedFixed64=Ue.prototype.zd,Ue.prototype.Ad=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.nb(e,t[i])},Ue.prototype.writeRepeatedFixed64String=Ue.prototype.Ad,Ue.prototype.Jd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.Ra(e,t[i])},Ue.prototype.writeRepeatedSfixed32=Ue.prototype.Jd,Ue.prototype.Kd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.Sa(e,t[i])},Ue.prototype.writeRepeatedSfixed64=Ue.prototype.Kd,Ue.prototype.Ld=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.qb(e,t[i])},Ue.prototype.writeRepeatedSfixed64String=Ue.prototype.Ld,Ue.prototype.Cd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.L(e,t[i])},Ue.prototype.writeRepeatedFloat=Ue.prototype.Cd,Ue.prototype.wd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.J(e,t[i])},Ue.prototype.writeRepeatedDouble=Ue.prototype.wd,Ue.prototype.ud=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.I(e,t[i])},Ue.prototype.writeRepeatedBool=Ue.prototype.ud,Ue.prototype.xd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.R(e,t[i])},Ue.prototype.writeRepeatedEnum=Ue.prototype.xd,Ue.prototype.Td=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.U(e,t[i])},Ue.prototype.writeRepeatedString=Ue.prototype.Td,Ue.prototype.vd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.ja(e,t[i])},Ue.prototype.writeRepeatedBytes=Ue.prototype.vd,Ue.prototype.Id=function(e,t,i){if(null!=t)for(var s=0;s<t.length;s++){var r=He(this,e);i(t[s],this),qe(this,r)}},Ue.prototype.writeRepeatedMessage=Ue.prototype.Id,Ue.prototype.Dd=function(e,t,i){if(null!=t)for(var s=0;s<t.length;s++)Ke(this,e,3),i(t[s],this),Ke(this,e,4)},Ue.prototype.writeRepeatedGroup=Ue.prototype.Dd,Ue.prototype.Bd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.K(e,t[i])},Ue.prototype.writeRepeatedFixedHash64=Ue.prototype.Bd,Ue.prototype.Yd=function(e,t){if(null!=t)for(var i=0;i<t.length;i++)this.N(e,t[i])},Ue.prototype.writeRepeatedVarintHash64=Ue.prototype.Yd,Ue.prototype.ad=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.M(t[i]);qe(this,e)}},Ue.prototype.writePackedInt32=Ue.prototype.ad,Ue.prototype.bd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.M(parseInt(t[i],10));qe(this,e)}},Ue.prototype.writePackedInt32String=Ue.prototype.bd,Ue.prototype.cd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.ua(t[i]);qe(this,e)}},Ue.prototype.writePackedInt64=Ue.prototype.cd,Ue.prototype.md=function(e,t,i,s){if(null!=t){e=He(this,e);for(var r=0;r<t.length;r++)this.a.A(i(t[r]),s(t[r]));qe(this,e)}},Ue.prototype.writePackedSplitFixed64=Ue.prototype.md,Ue.prototype.nd=function(e,t,i,s){if(null!=t){e=He(this,e);for(var r=0;r<t.length;r++)this.a.l(i(t[r]),s(t[r]));qe(this,e)}},Ue.prototype.writePackedSplitVarint64=Ue.prototype.nd,Ue.prototype.od=function(e,t,i,s){if(null!=t){e=He(this,e);for(var r=this.a,n=0;n<t.length;n++)H(i(t[n]),s(t[n]),(function(e,t){r.l(e>>>0,t>>>0)}));qe(this,e)}},Ue.prototype.writePackedSplitZigzagVarint64=Ue.prototype.od,Ue.prototype.dd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++){var s=Ve(t[i]);this.a.l(s.lo,s.hi)}qe(this,e)}},Ue.prototype.writePackedInt64String=Ue.prototype.dd,Ue.prototype.pd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.j(t[i]);qe(this,e)}},Ue.prototype.writePackedUint32=Ue.prototype.pd,Ue.prototype.qd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.j(parseInt(t[i],10));qe(this,e)}},Ue.prototype.writePackedUint32String=Ue.prototype.qd,Ue.prototype.rd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.va(t[i]);qe(this,e)}},Ue.prototype.writePackedUint64=Ue.prototype.rd,Ue.prototype.sd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++){var s=je(t[i]);this.a.l(s.lo,s.hi)}qe(this,e)}},Ue.prototype.writePackedUint64String=Ue.prototype.sd,Ue.prototype.hd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.wa(t[i]);qe(this,e)}},Ue.prototype.writePackedSint32=Ue.prototype.hd,Ue.prototype.jd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.xa(t[i]);qe(this,e)}},Ue.prototype.writePackedSint64=Ue.prototype.jd,Ue.prototype.kd=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.W(te(t[i]));qe(this,e)}},Ue.prototype.writePackedSint64String=Ue.prototype.kd,Ue.prototype.ld=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.W(t[i]);qe(this,e)}},Ue.prototype.writePackedSintHash64=Ue.prototype.ld,Ue.prototype.Wc=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(4*t.length),e=0;e<t.length;e++)this.a.s(t[e])},Ue.prototype.writePackedFixed32=Ue.prototype.Wc,Ue.prototype.Xc=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(8*t.length),e=0;e<t.length;e++)this.a.V(t[e])},Ue.prototype.writePackedFixed64=Ue.prototype.Xc,Ue.prototype.Yc=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(8*t.length),e=0;e<t.length;e++){var i=je(t[e]);this.a.A(i.lo,i.hi)}},Ue.prototype.writePackedFixed64String=Ue.prototype.Yc,Ue.prototype.ed=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(4*t.length),e=0;e<t.length;e++)this.a.S(t[e])},Ue.prototype.writePackedSfixed32=Ue.prototype.ed,Ue.prototype.fd=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(8*t.length),e=0;e<t.length;e++)this.a.T(t[e])},Ue.prototype.writePackedSfixed64=Ue.prototype.fd,Ue.prototype.gd=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(8*t.length),e=0;e<t.length;e++)this.a.ka(t[e])},Ue.prototype.writePackedSfixed64String=Ue.prototype.gd,Ue.prototype.$c=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(4*t.length),e=0;e<t.length;e++)this.a.L(t[e])},Ue.prototype.writePackedFloat=Ue.prototype.$c,Ue.prototype.Uc=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(8*t.length),e=0;e<t.length;e++)this.a.J(t[e])},Ue.prototype.writePackedDouble=Ue.prototype.Uc,Ue.prototype.Tc=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(t.length),e=0;e<t.length;e++)this.a.I(t[e])},Ue.prototype.writePackedBool=Ue.prototype.Tc,Ue.prototype.Vc=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.R(t[i]);qe(this,e)}},Ue.prototype.writePackedEnum=Ue.prototype.Vc,Ue.prototype.Zc=function(e,t){if(null!=t&&t.length)for(Ke(this,e,2),this.a.j(8*t.length),e=0;e<t.length;e++)this.a.K(t[e])},Ue.prototype.writePackedFixedHash64=Ue.prototype.Zc,Ue.prototype.td=function(e,t){if(null!=t&&t.length){e=He(this,e);for(var i=0;i<t.length;i++)this.a.N(t[i]);qe(this,e)}},Ue.prototype.writePackedVarintHash64=Ue.prototype.td,t.debug=Be,t.Map=M,t.Message=ye,t.BinaryReader=ce,t.BinaryWriter=Ue,t.ExtensionFieldInfo=de,t.ExtensionFieldBinaryInfo=fe,t.exportSymbol=function(e,t,i){p(e,t,i)},t.inherits=function(e,t){function i(){}i.prototype=t.prototype,e.prototype=new i,e.prototype.constructor=e},t.object={extend:function(e,t){for(var i,s,r=1;r<arguments.length;r++){for(i in s=arguments[r])e[i]=s[i];for(var n=0;n<f.length;n++)i=f[n],Object.prototype.hasOwnProperty.call(s,i)&&(e[i]=s[i])}}},t.typeOf=u}},t={};function i(s){var r=t[s];if(void 0!==r)return r.exports;var n=t[s]={exports:{}};return e[s].call(n.exports,n,n.exports,i),n.exports}i.d=(e,t)=>{for(var s in t)i.o(t,s)&&!i.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};return(()=>{"use strict";i.r(s),i.d(s,{CreateDevice:()=>$i,GetMediaCapacity:()=>Zi,InitSdk:()=>Qi,LDDebug:()=>g,LDDevice:()=>v,LDDeviceKeyEvent:()=>Bi,LDDeviceMoveEvent:()=>Wi,LDDeviceNotify:()=>E,LDDeviceOperation:()=>Ci,LDDeviceType:()=>b,LDError:()=>_,LDErrorCode:()=>p,LDFunctionKeyType:()=>Fi,LDGameArchive:()=>qi,LDGameArchiveType:()=>Ui,LDGameInfo:()=>Hi,LDIMEAction:()=>d,LDIMEInputType:()=>u,LDIMEStatus:()=>w,LDIMEType:()=>ki,LDImageType:()=>Pi,LDLoginInfo:()=>T,LDMediaCapacity:()=>Ji,LDMediaStats:()=>Ai,LDPackageType:()=>Ni,LDPreview:()=>ji,LDResourceDetail:()=>Li,LDRotation:()=>h,LDSaveGameArchive:()=>Ki,LDSensorData:()=>xi,LDSensorType:()=>Di,LDTrasnsferFileExecType:()=>Ri,LDVideoQuality:()=>Ei,LDVideoType:()=>Ii});var e,t=i(339),r=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(e){var i,s,n;let o,a;!function(e){e[e.OK=0]="OK",e[e.Unknown=1]="Unknown",e[e.InvalidArgument=2]="InvalidArgument",e[e.PermissionDenied=3]="PermissionDenied",e[e.NotFound=4]="NotFound",e[e.InternalError=5]="InternalError",e[e.Timeout=6]="Timeout",e[e.NetworkError=7]="NetworkError",e[e.FileError=8]="FileError",e[e.WebrtcError=9]="WebrtcError",e[e.ProtocolError=10]="ProtocolError"}(o=e.ErrorCode||(e.ErrorCode={})),function(e){e[e.Rotation_0=0]="Rotation_0",e[e.Rotation_90=1]="Rotation_90",e[e.Rotation_180=2]="Rotation_180",e[e.Rotation_270=3]="Rotation_270"}(a=e.DeviceRotation||(e.DeviceRotation={}));class l extends t.Message{constructor(e){super(),i.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],r(this,i,"f")),Array.isArray(e)}static fromObject(e){return new l({})}toObject(){return{}}serialize(e){const i=e||new t.BinaryWriter;if(!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new l;for(;i.nextField()&&!i.isEndGroup();)i.getFieldNumber(),i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return l.deserialize(e)}}i=new WeakMap,e.Empty=l;class c extends t.Message{constructor(e){super(),s.set(this,[[4],[5]]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],r(this,s,"f")),Array.isArray(e)||"object"!=typeof e||("seq"in e&&null!=e.seq&&(this.seq=e.seq),"type"in e&&null!=e.type&&(this.type=e.type),"sub_type"in e&&null!=e.sub_type&&(this.sub_type=e.sub_type),"name"in e&&null!=e.name&&(this.name=e.name),"payload"in e&&null!=e.payload&&(this.payload=e.payload))}get seq(){return t.Message.getFieldWithDefault(this,1,0)}set seq(e){t.Message.setField(this,1,e)}get type(){return t.Message.getFieldWithDefault(this,2,0)}set type(e){t.Message.setField(this,2,e)}get sub_type(){return t.Message.getFieldWithDefault(this,3,0)}set sub_type(e){t.Message.setField(this,3,e)}get name(){return t.Message.getFieldWithDefault(this,4,"")}set name(e){t.Message.setOneofField(this,4,r(this,s,"f")[0],e)}get has_name(){return null!=t.Message.getField(this,4)}get payload(){return t.Message.getFieldWithDefault(this,5,new Uint8Array(0))}set payload(e){t.Message.setOneofField(this,5,r(this,s,"f")[1],e)}get has_payload(){return null!=t.Message.getField(this,5)}get _name(){return{0:"none",4:"name"}[t.Message.computeOneofCase(this,[4])]}get _payload(){return{0:"none",5:"payload"}[t.Message.computeOneofCase(this,[5])]}static fromObject(e){const t=new c({});return null!=e.seq&&(t.seq=e.seq),null!=e.type&&(t.type=e.type),null!=e.sub_type&&(t.sub_type=e.sub_type),null!=e.name&&(t.name=e.name),null!=e.payload&&(t.payload=e.payload),t}toObject(){const e={};return null!=this.seq&&(e.seq=this.seq),null!=this.type&&(e.type=this.type),null!=this.sub_type&&(e.sub_type=this.sub_type),null!=this.name&&(e.name=this.name),null!=this.payload&&(e.payload=this.payload),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.seq&&i.writeUint32(1,this.seq),0!=this.type&&i.writeUint32(2,this.type),0!=this.sub_type&&i.writeUint64(3,this.sub_type),this.has_name&&i.writeString(4,this.name),this.has_payload&&i.writeBytes(5,this.payload),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new c;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.seq=i.readUint32();break;case 2:s.type=i.readUint32();break;case 3:s.sub_type=i.readUint64();break;case 4:s.name=i.readString();break;case 5:s.payload=i.readBytes();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return c.deserialize(e)}}s=new WeakMap,e.CommonMessage=c,function(e){let t;!function(e){e[e.Request=0]="Request",e[e.Response=1]="Response",e[e.Notification=2]="Notification"}(t=e.Type||(e.Type={}))}(c=e.CommonMessage||(e.CommonMessage={}));class h extends t.Message{constructor(e){super(),n.set(this,[[2]]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],r(this,n,"f")),Array.isArray(e)||"object"!=typeof e||("code"in e&&null!=e.code&&(this.code=e.code),"result"in e&&null!=e.result&&(this.result=e.result))}get code(){return t.Message.getFieldWithDefault(this,1,o.OK)}set code(e){t.Message.setField(this,1,e)}get result(){return t.Message.getFieldWithDefault(this,2,new Uint8Array(0))}set result(e){t.Message.setOneofField(this,2,r(this,n,"f")[0],e)}get has_result(){return null!=t.Message.getField(this,2)}get _result(){return{0:"none",2:"result"}[t.Message.computeOneofCase(this,[2])]}static fromObject(e){const t=new h({});return null!=e.code&&(t.code=e.code),null!=e.result&&(t.result=e.result),t}toObject(){const e={};return null!=this.code&&(e.code=this.code),null!=this.result&&(e.result=this.result),e}serialize(e){const i=e||new t.BinaryWriter;if(this.code!=o.OK&&i.writeEnum(1,this.code),this.has_result&&i.writeBytes(2,this.result),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new h;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.code=i.readEnum();break;case 2:s.result=i.readBytes();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return h.deserialize(e)}}n=new WeakMap,e.CommonRes=h}(e||(e={}));var n,o=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(i){var s,r,n,a;let l,c,h,p,u;!function(e){e[e.LOGIN_TYPE_MIN=0]="LOGIN_TYPE_MIN",e[e.LOGIN_TYPE_LOGIN=1]="LOGIN_TYPE_LOGIN",e[e.LOGIN_TYPE_QUIT=2]="LOGIN_TYPE_QUIT",e[e.LOGIN_TYPE_REFRESH_AUTHORIZE=3]="LOGIN_TYPE_REFRESH_AUTHORIZE",e[e.LOGIN_TYPE_KICKOUT_OTHERS=4]="LOGIN_TYPE_KICKOUT_OTHERS",e[e.LOGIN_TYPE_REPORT_INFO=9999]="LOGIN_TYPE_REPORT_INFO",e[e.LOGIN_TYPE_MAX=1e4]="LOGIN_TYPE_MAX"}(l=i.LoginType||(i.LoginType={})),function(e){e[e.LOGIN_NOTIFICATION_TYPE_MIN=0]="LOGIN_NOTIFICATION_TYPE_MIN",e[e.LOGIN_NOTIFICATION_TYPE_KICKOUT=1]="LOGIN_NOTIFICATION_TYPE_KICKOUT",e[e.LOGIN_NOTIFICATION_TYPE_MAX=1e4]="LOGIN_NOTIFICATION_TYPE_MAX"}(c=i.LoginNotificationType||(i.LoginNotificationType={})),function(e){e[e.kUnknown=0]="kUnknown",e[e.kAndroid=1]="kAndroid",e[e.kPC=2]="kPC",e[e.kIOS=3]="kIOS",e[e.kSimulator=4]="kSimulator",e[e.kPCWeb=5]="kPCWeb",e[e.kWeb=6]="kWeb",e[e.kHarmonyOS=7]="kHarmonyOS"}(h=i.ClientType||(i.ClientType={})),function(e){e[e.ForLog=0]="ForLog"}(p=i.ReportInfoType||(i.ReportInfoType={})),function(e){e[e.KickoutUnknown=0]="KickoutUnknown",e[e.MultiMeida=1]="MultiMeida",e[e.RevokeAuthorize=2]="RevokeAuthorize",e[e.Reboot=3]="Reboot",e[e.GameKickout=4]="GameKickout",e[e.WebrtcConnectError=5]="WebrtcConnectError"}(u=i.KickOutType||(i.KickOutType={}));class d extends t.Message{constructor(e){super(),s.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],o(this,s,"f")),Array.isArray(e)||"object"!=typeof e||("app_key"in e&&null!=e.app_key&&(this.app_key=e.app_key),"user_id"in e&&null!=e.user_id&&(this.user_id=e.user_id),"token"in e&&null!=e.token&&(this.token=e.token),"device"in e&&null!=e.device&&(this.device=e.device))}get app_key(){return t.Message.getFieldWithDefault(this,1,"")}set app_key(e){t.Message.setField(this,1,e)}get user_id(){return t.Message.getFieldWithDefault(this,2,"")}set user_id(e){t.Message.setField(this,2,e)}get token(){return t.Message.getFieldWithDefault(this,3,"")}set token(e){t.Message.setField(this,3,e)}get device(){return t.Message.getFieldWithDefault(this,4,h.kUnknown)}set device(e){t.Message.setField(this,4,e)}static fromObject(e){const t=new d({});return null!=e.app_key&&(t.app_key=e.app_key),null!=e.user_id&&(t.user_id=e.user_id),null!=e.token&&(t.token=e.token),null!=e.device&&(t.device=e.device),t}toObject(){const e={};return null!=this.app_key&&(e.app_key=this.app_key),null!=this.user_id&&(e.user_id=this.user_id),null!=this.token&&(e.token=this.token),null!=this.device&&(e.device=this.device),e}serialize(e){const i=e||new t.BinaryWriter;if(this.app_key.length&&i.writeString(1,this.app_key),this.user_id.length&&i.writeString(2,this.user_id),this.token.length&&i.writeString(3,this.token),this.device!=h.kUnknown&&i.writeEnum(4,this.device),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new d;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.app_key=i.readString();break;case 2:s.user_id=i.readString();break;case 3:s.token=i.readString();break;case 4:s.device=i.readEnum();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return d.deserialize(e)}}s=new WeakMap,i.Login=d;class f extends t.Message{constructor(e){super(),r.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],o(this,r,"f")),Array.isArray(e)||"object"!=typeof e||"common"in e&&null!=e.common&&(this.common=e.common)}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}static fromObject(t){const i=new f({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new f;for(;s.nextField()&&!s.isEndGroup();)1===s.getFieldNumber()?s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s))):s.skipField();return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return f.deserialize(e)}}r=new WeakMap,i.LoginRes=f;class y extends t.Message{constructor(e){super(),n.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],o(this,n,"f")),Array.isArray(e)||"object"!=typeof e||("type"in e&&null!=e.type&&(this.type=e.type),"message"in e&&null!=e.message&&(this.message=e.message))}get type(){return t.Message.getFieldWithDefault(this,1,p.ForLog)}set type(e){t.Message.setField(this,1,e)}get message(){return t.Message.getFieldWithDefault(this,2,"")}set message(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new y({});return null!=e.type&&(t.type=e.type),null!=e.message&&(t.message=e.message),t}toObject(){const e={};return null!=this.type&&(e.type=this.type),null!=this.message&&(e.message=this.message),e}serialize(e){const i=e||new t.BinaryWriter;if(this.type!=p.ForLog&&i.writeEnum(1,this.type),this.message.length&&i.writeString(2,this.message),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new y;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.type=i.readEnum();break;case 2:s.message=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return y.deserialize(e)}}n=new WeakMap,i.ReportInfo=y;class m extends t.Message{constructor(e){super(),a.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],o(this,a,"f")),Array.isArray(e)||"object"!=typeof e||"type"in e&&null!=e.type&&(this.type=e.type)}get type(){return t.Message.getFieldWithDefault(this,1,u.KickoutUnknown)}set type(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new m({});return null!=e.type&&(t.type=e.type),t}toObject(){const e={};return null!=this.type&&(e.type=this.type),e}serialize(e){const i=e||new t.BinaryWriter;if(this.type!=u.KickoutUnknown&&i.writeEnum(1,this.type),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new m;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.type=i.readEnum():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return m.deserialize(e)}}a=new WeakMap,i.KickOut=m}(n||(n={}));var a,l=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(i){var s,r,n,o,a,c,h,p,u,d,f,y,m,g,_,w;let E,v,b,M;!function(e){e[e.Opus=0]="Opus",e[e.PCM=1]="PCM"}(E=i.AudioType||(i.AudioType={})),function(e){e[e.H264=0]="H264",e[e.H265=1]="H265"}(v=i.VideoType||(i.VideoType={})),function(e){e[e.MEDIA_TYPE_NONE=0]="MEDIA_TYPE_NONE",e[e.MEDIA_TYPE_MIN=2e4]="MEDIA_TYPE_MIN",e[e.MEDIA_TYPE_SWITCH_VIDEO_QUALITY=20001]="MEDIA_TYPE_SWITCH_VIDEO_QUALITY",e[e.MEDIA_TYPE_IDR=20002]="MEDIA_TYPE_IDR",e[e.MEDIA_TYPE_MUTE=20003]="MEDIA_TYPE_MUTE",e[e.MEDIA_TYPE_CONNECT=20004]="MEDIA_TYPE_CONNECT",e[e.MEDIA_TYPE_VIDEO_STREAM_REPORT=20005]="MEDIA_TYPE_VIDEO_STREAM_REPORT",e[e.MEDIA_TYPE_CHANGE_VIDEO_SIZE=20006]="MEDIA_TYPE_CHANGE_VIDEO_SIZE",e[e.MEDIA_TYPE_DISCONNECT=20007]="MEDIA_TYPE_DISCONNECT",e[e.MEDIA_TYPE_MAX=3e4]="MEDIA_TYPE_MAX"}(b=i.MediaType||(i.MediaType={})),function(e){e[e.MEDIA_NOTIFICATION_TYPE_NONE=0]="MEDIA_NOTIFICATION_TYPE_NONE",e[e.MEDIA_NOTIFICATION_TYPE_MIN=2e4]="MEDIA_NOTIFICATION_TYPE_MIN",e[e.MEDIA_NOTIFICATION_TYPE_STREAM_INFO=20001]="MEDIA_NOTIFICATION_TYPE_STREAM_INFO",e[e.MEDIA_NOTIFICATION_TYPE_VIDEO_STREAM=20002]="MEDIA_NOTIFICATION_TYPE_VIDEO_STREAM",e[e.MEDIA_NOTIFICATION_TYPE_AUDIO_STREAM=20003]="MEDIA_NOTIFICATION_TYPE_AUDIO_STREAM",e[e.MEDIA_NOTIFICATION_TYPE_NETWORK_LAG=20004]="MEDIA_NOTIFICATION_TYPE_NETWORK_LAG",e[e.MEDIA_NOTIFICATION_TYPE_ICECANDIDATE=20005]="MEDIA_NOTIFICATION_TYPE_ICECANDIDATE",e[e.MEDIA_NOTIFICATION_TYPE_STREAM_REPORT=20006]="MEDIA_NOTIFICATION_TYPE_STREAM_REPORT",e[e.MEDIA_NOTIFICATION_TYPE_MAX=3e4]="MEDIA_NOTIFICATION_TYPE_MAX"}(M=i.MediaNotificationType||(i.MediaNotificationType={}));class T extends t.Message{constructor(e){super(),s.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,s,"f")),Array.isArray(e)||"object"!=typeof e||("width"in e&&null!=e.width&&(this.width=e.width),"height"in e&&null!=e.height&&(this.height=e.height))}get width(){return t.Message.getFieldWithDefault(this,1,0)}set width(e){t.Message.setField(this,1,e)}get height(){return t.Message.getFieldWithDefault(this,2,0)}set height(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new T({});return null!=e.width&&(t.width=e.width),null!=e.height&&(t.height=e.height),t}toObject(){const e={};return null!=this.width&&(e.width=this.width),null!=this.height&&(e.height=this.height),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.width&&i.writeSint32(1,this.width),0!=this.height&&i.writeSint32(2,this.height),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new T;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.width=i.readSint32();break;case 2:s.height=i.readSint32();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return T.deserialize(e)}}s=new WeakMap,i.Size=T;class I extends t.Message{constructor(e){super(),r.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[6],l(this,r,"f")),Array.isArray(e)||"object"!=typeof e||("audio_type"in e&&null!=e.audio_type&&(this.audio_type=e.audio_type),"video_type"in e&&null!=e.video_type&&(this.video_type=e.video_type),"capture_size"in e&&null!=e.capture_size&&(this.capture_size=e.capture_size),"client_size"in e&&null!=e.client_size&&(this.client_size=e.client_size),"fps"in e&&null!=e.fps&&(this.fps=e.fps),"video_support"in e&&null!=e.video_support&&(this.video_support=e.video_support),"silence"in e&&null!=e.silence&&(this.silence=e.silence),"bitrate"in e&&null!=e.bitrate&&(this.bitrate=e.bitrate))}get audio_type(){return t.Message.getFieldWithDefault(this,1,E.Opus)}set audio_type(e){t.Message.setField(this,1,e)}get video_type(){return t.Message.getFieldWithDefault(this,2,v.H264)}set video_type(e){t.Message.setField(this,2,e)}get capture_size(){return t.Message.getWrapperField(this,T,3)}set capture_size(e){t.Message.setWrapperField(this,3,e)}get has_capture_size(){return null!=t.Message.getField(this,3)}get client_size(){return t.Message.getWrapperField(this,T,4)}set client_size(e){t.Message.setWrapperField(this,4,e)}get has_client_size(){return null!=t.Message.getField(this,4)}get fps(){return t.Message.getFieldWithDefault(this,5,0)}set fps(e){t.Message.setField(this,5,e)}get video_support(){return t.Message.getFieldWithDefault(this,6,[])}set video_support(e){t.Message.setField(this,6,e)}get silence(){return t.Message.getFieldWithDefault(this,7,!1)}set silence(e){t.Message.setField(this,7,e)}get bitrate(){return t.Message.getFieldWithDefault(this,8,0)}set bitrate(e){t.Message.setField(this,8,e)}static fromObject(e){const t=new I({});return null!=e.audio_type&&(t.audio_type=e.audio_type),null!=e.video_type&&(t.video_type=e.video_type),null!=e.capture_size&&(t.capture_size=T.fromObject(e.capture_size)),null!=e.client_size&&(t.client_size=T.fromObject(e.client_size)),null!=e.fps&&(t.fps=e.fps),null!=e.video_support&&(t.video_support=e.video_support),null!=e.silence&&(t.silence=e.silence),null!=e.bitrate&&(t.bitrate=e.bitrate),t}toObject(){const e={};return null!=this.audio_type&&(e.audio_type=this.audio_type),null!=this.video_type&&(e.video_type=this.video_type),null!=this.capture_size&&(e.capture_size=this.capture_size.toObject()),null!=this.client_size&&(e.client_size=this.client_size.toObject()),null!=this.fps&&(e.fps=this.fps),null!=this.video_support&&(e.video_support=this.video_support),null!=this.silence&&(e.silence=this.silence),null!=this.bitrate&&(e.bitrate=this.bitrate),e}serialize(e){const i=e||new t.BinaryWriter;if(this.audio_type!=E.Opus&&i.writeEnum(1,this.audio_type),this.video_type!=v.H264&&i.writeEnum(2,this.video_type),this.has_capture_size&&i.writeMessage(3,this.capture_size,(()=>this.capture_size.serialize(i))),this.has_client_size&&i.writeMessage(4,this.client_size,(()=>this.client_size.serialize(i))),0!=this.fps&&i.writeSint32(5,this.fps),this.video_support.length&&i.writeRepeatedString(6,this.video_support),0!=this.silence&&i.writeBool(7,this.silence),0!=this.bitrate&&i.writeSint32(8,this.bitrate),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new I;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.audio_type=i.readEnum();break;case 2:s.video_type=i.readEnum();break;case 3:i.readMessage(s.capture_size,(()=>s.capture_size=T.deserialize(i)));break;case 4:i.readMessage(s.client_size,(()=>s.client_size=T.deserialize(i)));break;case 5:s.fps=i.readSint32();break;case 6:t.Message.addToRepeatedField(s,6,i.readString());break;case 7:s.silence=i.readBool();break;case 8:s.bitrate=i.readSint32();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return I.deserialize(e)}}r=new WeakMap,i.MediaInfo=I;class A extends t.Message{constructor(e){super(),n.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,n,"f")),Array.isArray(e)||"object"!=typeof e||("sdp"in e&&null!=e.sdp&&(this.sdp=e.sdp),"auto_rotate"in e&&null!=e.auto_rotate&&(this.auto_rotate=e.auto_rotate))}get sdp(){return t.Message.getFieldWithDefault(this,1,"")}set sdp(e){t.Message.setField(this,1,e)}get auto_rotate(){return t.Message.getFieldWithDefault(this,2,!1)}set auto_rotate(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new A({});return null!=e.sdp&&(t.sdp=e.sdp),null!=e.auto_rotate&&(t.auto_rotate=e.auto_rotate),t}toObject(){const e={};return null!=this.sdp&&(e.sdp=this.sdp),null!=this.auto_rotate&&(e.auto_rotate=this.auto_rotate),e}serialize(e){const i=e||new t.BinaryWriter;if(this.sdp.length&&i.writeString(1,this.sdp),0!=this.auto_rotate&&i.writeBool(2,this.auto_rotate),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new A;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.sdp=i.readString();break;case 2:s.auto_rotate=i.readBool();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return A.deserialize(e)}}n=new WeakMap,i.WebrtcInfo=A;class O extends t.Message{constructor(e){super(),o.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,o,"f")),Array.isArray(e)||"object"!=typeof e||("media_info"in e&&null!=e.media_info&&(this.media_info=e.media_info),"webrtc_info"in e&&null!=e.webrtc_info&&(this.webrtc_info=e.webrtc_info))}get media_info(){return t.Message.getWrapperField(this,I,1)}set media_info(e){t.Message.setWrapperField(this,1,e)}get has_media_info(){return null!=t.Message.getField(this,1)}get webrtc_info(){return t.Message.getWrapperField(this,A,2)}set webrtc_info(e){t.Message.setWrapperField(this,2,e)}get has_webrtc_info(){return null!=t.Message.getField(this,2)}static fromObject(e){const t=new O({});return null!=e.media_info&&(t.media_info=I.fromObject(e.media_info)),null!=e.webrtc_info&&(t.webrtc_info=A.fromObject(e.webrtc_info)),t}toObject(){const e={};return null!=this.media_info&&(e.media_info=this.media_info.toObject()),null!=this.webrtc_info&&(e.webrtc_info=this.webrtc_info.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_media_info&&i.writeMessage(1,this.media_info,(()=>this.media_info.serialize(i))),this.has_webrtc_info&&i.writeMessage(2,this.webrtc_info,(()=>this.webrtc_info.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new O;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.media_info,(()=>s.media_info=I.deserialize(i)));break;case 2:i.readMessage(s.webrtc_info,(()=>s.webrtc_info=A.deserialize(i)));break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return O.deserialize(e)}}o=new WeakMap,i.ConnectInfo=O;class S extends t.Message{constructor(e){super(),a.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,a,"f")),Array.isArray(e)||"object"!=typeof e||"bitrate"in e&&null!=e.bitrate&&(this.bitrate=e.bitrate)}get bitrate(){return t.Message.getFieldWithDefault(this,1,0)}set bitrate(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new S({});return null!=e.bitrate&&(t.bitrate=e.bitrate),t}toObject(){const e={};return null!=this.bitrate&&(e.bitrate=this.bitrate),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.bitrate&&i.writeSint32(1,this.bitrate),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new S;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.bitrate=i.readSint32():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return S.deserialize(e)}}a=new WeakMap,i.SwitchVideoQuality=S;class F extends t.Message{constructor(e){super(),c.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,c,"f")),Array.isArray(e)||"object"!=typeof e||"candidate"in e&&null!=e.candidate&&(this.candidate=e.candidate)}get candidate(){return t.Message.getFieldWithDefault(this,1,"")}set candidate(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new F({});return null!=e.candidate&&(t.candidate=e.candidate),t}toObject(){const e={};return null!=this.candidate&&(e.candidate=this.candidate),e}serialize(e){const i=e||new t.BinaryWriter;if(this.candidate.length&&i.writeString(1,this.candidate),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new F;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.candidate=i.readString():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return F.deserialize(e)}}c=new WeakMap,i.WebrtcCandidiate=F;class C extends t.Message{constructor(e){super(),h.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,h,"f")),Array.isArray(e)||"object"!=typeof e||("primaries"in e&&null!=e.primaries&&(this.primaries=e.primaries),"transfer"in e&&null!=e.transfer&&(this.transfer=e.transfer),"matrix"in e&&null!=e.matrix&&(this.matrix=e.matrix))}get primaries(){return t.Message.getFieldWithDefault(this,1,"")}set primaries(e){t.Message.setField(this,1,e)}get transfer(){return t.Message.getFieldWithDefault(this,2,"")}set transfer(e){t.Message.setField(this,2,e)}get matrix(){return t.Message.getFieldWithDefault(this,3,"")}set matrix(e){t.Message.setField(this,3,e)}static fromObject(e){const t=new C({});return null!=e.primaries&&(t.primaries=e.primaries),null!=e.transfer&&(t.transfer=e.transfer),null!=e.matrix&&(t.matrix=e.matrix),t}toObject(){const e={};return null!=this.primaries&&(e.primaries=this.primaries),null!=this.transfer&&(e.transfer=this.transfer),null!=this.matrix&&(e.matrix=this.matrix),e}serialize(e){const i=e||new t.BinaryWriter;if(this.primaries.length&&i.writeString(1,this.primaries),this.transfer.length&&i.writeString(2,this.transfer),this.matrix.length&&i.writeString(3,this.matrix),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new C;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.primaries=i.readString();break;case 2:s.transfer=i.readString();break;case 3:s.matrix=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return C.deserialize(e)}}h=new WeakMap,i.VideoColorSpace=C;class k extends t.Message{constructor(e){super(),p.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,p,"f")),Array.isArray(e)||"object"!=typeof e||("size"in e&&null!=e.size&&(this.size=e.size),"type"in e&&null!=e.type&&(this.type=e.type),"fps"in e&&null!=e.fps&&(this.fps=e.fps),"codec"in e&&null!=e.codec&&(this.codec=e.codec),"colorspace"in e&&null!=e.colorspace&&(this.colorspace=e.colorspace),"original_size"in e&&null!=e.original_size&&(this.original_size=e.original_size))}get size(){return t.Message.getWrapperField(this,T,1)}set size(e){t.Message.setWrapperField(this,1,e)}get has_size(){return null!=t.Message.getField(this,1)}get type(){return t.Message.getFieldWithDefault(this,2,v.H264)}set type(e){t.Message.setField(this,2,e)}get fps(){return t.Message.getFieldWithDefault(this,3,0)}set fps(e){t.Message.setField(this,3,e)}get codec(){return t.Message.getFieldWithDefault(this,4,"")}set codec(e){t.Message.setField(this,4,e)}get colorspace(){return t.Message.getWrapperField(this,C,5)}set colorspace(e){t.Message.setWrapperField(this,5,e)}get has_colorspace(){return null!=t.Message.getField(this,5)}get original_size(){return t.Message.getWrapperField(this,T,6)}set original_size(e){t.Message.setWrapperField(this,6,e)}get has_original_size(){return null!=t.Message.getField(this,6)}static fromObject(e){const t=new k({});return null!=e.size&&(t.size=T.fromObject(e.size)),null!=e.type&&(t.type=e.type),null!=e.fps&&(t.fps=e.fps),null!=e.codec&&(t.codec=e.codec),null!=e.colorspace&&(t.colorspace=C.fromObject(e.colorspace)),null!=e.original_size&&(t.original_size=T.fromObject(e.original_size)),t}toObject(){const e={};return null!=this.size&&(e.size=this.size.toObject()),null!=this.type&&(e.type=this.type),null!=this.fps&&(e.fps=this.fps),null!=this.codec&&(e.codec=this.codec),null!=this.colorspace&&(e.colorspace=this.colorspace.toObject()),null!=this.original_size&&(e.original_size=this.original_size.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_size&&i.writeMessage(1,this.size,(()=>this.size.serialize(i))),this.type!=v.H264&&i.writeEnum(2,this.type),0!=this.fps&&i.writeSint32(3,this.fps),this.codec.length&&i.writeString(4,this.codec),this.has_colorspace&&i.writeMessage(5,this.colorspace,(()=>this.colorspace.serialize(i))),this.has_original_size&&i.writeMessage(6,this.original_size,(()=>this.original_size.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new k;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.size,(()=>s.size=T.deserialize(i)));break;case 2:s.type=i.readEnum();break;case 3:s.fps=i.readSint32();break;case 4:s.codec=i.readString();break;case 5:i.readMessage(s.colorspace,(()=>s.colorspace=C.deserialize(i)));break;case 6:i.readMessage(s.original_size,(()=>s.original_size=T.deserialize(i)));break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return k.deserialize(e)}}p=new WeakMap,i.StreamVideoInfo=k;class D extends t.Message{constructor(e){super(),u.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,u,"f")),Array.isArray(e)||"object"!=typeof e||("channels"in e&&null!=e.channels&&(this.channels=e.channels),"sample_rate"in e&&null!=e.sample_rate&&(this.sample_rate=e.sample_rate),"samples"in e&&null!=e.samples&&(this.samples=e.samples),"type"in e&&null!=e.type&&(this.type=e.type),"bits_per_sample"in e&&null!=e.bits_per_sample&&(this.bits_per_sample=e.bits_per_sample),"codec"in e&&null!=e.codec&&(this.codec=e.codec),"channel_layout"in e&&null!=e.channel_layout&&(this.channel_layout=e.channel_layout))}get channels(){return t.Message.getFieldWithDefault(this,1,0)}set channels(e){t.Message.setField(this,1,e)}get sample_rate(){return t.Message.getFieldWithDefault(this,2,0)}set sample_rate(e){t.Message.setField(this,2,e)}get samples(){return t.Message.getFieldWithDefault(this,3,0)}set samples(e){t.Message.setField(this,3,e)}get type(){return t.Message.getFieldWithDefault(this,4,E.Opus)}set type(e){t.Message.setField(this,4,e)}get bits_per_sample(){return t.Message.getFieldWithDefault(this,5,0)}set bits_per_sample(e){t.Message.setField(this,5,e)}get codec(){return t.Message.getFieldWithDefault(this,6,"")}set codec(e){t.Message.setField(this,6,e)}get channel_layout(){return t.Message.getFieldWithDefault(this,7,"")}set channel_layout(e){t.Message.setField(this,7,e)}static fromObject(e){const t=new D({});return null!=e.channels&&(t.channels=e.channels),null!=e.sample_rate&&(t.sample_rate=e.sample_rate),null!=e.samples&&(t.samples=e.samples),null!=e.type&&(t.type=e.type),null!=e.bits_per_sample&&(t.bits_per_sample=e.bits_per_sample),null!=e.codec&&(t.codec=e.codec),null!=e.channel_layout&&(t.channel_layout=e.channel_layout),t}toObject(){const e={};return null!=this.channels&&(e.channels=this.channels),null!=this.sample_rate&&(e.sample_rate=this.sample_rate),null!=this.samples&&(e.samples=this.samples),null!=this.type&&(e.type=this.type),null!=this.bits_per_sample&&(e.bits_per_sample=this.bits_per_sample),null!=this.codec&&(e.codec=this.codec),null!=this.channel_layout&&(e.channel_layout=this.channel_layout),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.channels&&i.writeSint32(1,this.channels),0!=this.sample_rate&&i.writeSint32(2,this.sample_rate),0!=this.samples&&i.writeSint32(3,this.samples),this.type!=E.Opus&&i.writeEnum(4,this.type),0!=this.bits_per_sample&&i.writeSint32(5,this.bits_per_sample),this.codec.length&&i.writeString(6,this.codec),this.channel_layout.length&&i.writeString(7,this.channel_layout),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new D;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.channels=i.readSint32();break;case 2:s.sample_rate=i.readSint32();break;case 3:s.samples=i.readSint32();break;case 4:s.type=i.readEnum();break;case 5:s.bits_per_sample=i.readSint32();break;case 6:s.codec=i.readString();break;case 7:s.channel_layout=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return D.deserialize(e)}}u=new WeakMap,i.StreamAudioInfo=D;class R extends t.Message{constructor(e){super(),d.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,d,"f")),Array.isArray(e)||"object"!=typeof e||"mute"in e&&null!=e.mute&&(this.mute=e.mute)}get mute(){return t.Message.getFieldWithDefault(this,1,!1)}set mute(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new R({});return null!=e.mute&&(t.mute=e.mute),t}toObject(){const e={};return null!=this.mute&&(e.mute=this.mute),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.mute&&i.writeBool(1,this.mute),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new R;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.mute=i.readBool():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return R.deserialize(e)}}d=new WeakMap,i.StreamMute=R;class N extends t.Message{constructor(e){super(),f.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,f,"f")),Array.isArray(e)||"object"!=typeof e||"size"in e&&null!=e.size&&(this.size=e.size)}get size(){return t.Message.getWrapperField(this,T,1)}set size(e){t.Message.setWrapperField(this,1,e)}get has_size(){return null!=t.Message.getField(this,1)}static fromObject(e){const t=new N({});return null!=e.size&&(t.size=T.fromObject(e.size)),t}toObject(){const e={};return null!=this.size&&(e.size=this.size.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_size&&i.writeMessage(1,this.size,(()=>this.size.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new N;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?i.readMessage(s.size,(()=>s.size=T.deserialize(i))):i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return N.deserialize(e)}}f=new WeakMap,i.ChangeVideoSize=N;class P extends t.Message{constructor(e){super(),y.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,y,"f")),Array.isArray(e)||"object"!=typeof e||("video"in e&&null!=e.video&&(this.video=e.video),"audio"in e&&null!=e.audio&&(this.audio=e.audio))}get video(){return t.Message.getWrapperField(this,k,1)}set video(e){t.Message.setWrapperField(this,1,e)}get has_video(){return null!=t.Message.getField(this,1)}get audio(){return t.Message.getWrapperField(this,D,2)}set audio(e){t.Message.setWrapperField(this,2,e)}get has_audio(){return null!=t.Message.getField(this,2)}static fromObject(e){const t=new P({});return null!=e.video&&(t.video=k.fromObject(e.video)),null!=e.audio&&(t.audio=D.fromObject(e.audio)),t}toObject(){const e={};return null!=this.video&&(e.video=this.video.toObject()),null!=this.audio&&(e.audio=this.audio.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_video&&i.writeMessage(1,this.video,(()=>this.video.serialize(i))),this.has_audio&&i.writeMessage(2,this.audio,(()=>this.audio.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new P;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.video,(()=>s.video=k.deserialize(i)));break;case 2:i.readMessage(s.audio,(()=>s.audio=D.deserialize(i)));break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return P.deserialize(e)}}y=new WeakMap,i.StreamInfo=P;class z extends t.Message{constructor(e){super(),m.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,m,"f")),Array.isArray(e)||"object"!=typeof e||("timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp),"during"in e&&null!=e.during&&(this.during=e.during),"idr"in e&&null!=e.idr&&(this.idr=e.idr),"rotation"in e&&null!=e.rotation&&(this.rotation=e.rotation),"len"in e&&null!=e.len&&(this.len=e.len))}get timestamp(){return t.Message.getFieldWithDefault(this,1,0)}set timestamp(e){t.Message.setField(this,1,e)}get during(){return t.Message.getFieldWithDefault(this,2,0)}set during(e){t.Message.setField(this,2,e)}get idr(){return t.Message.getFieldWithDefault(this,3,!1)}set idr(e){t.Message.setField(this,3,e)}get rotation(){return t.Message.getFieldWithDefault(this,4,e.DeviceRotation.Rotation_0)}set rotation(e){t.Message.setField(this,4,e)}get len(){return t.Message.getFieldWithDefault(this,5,0)}set len(e){t.Message.setField(this,5,e)}static fromObject(e){const t=new z({});return null!=e.timestamp&&(t.timestamp=e.timestamp),null!=e.during&&(t.during=e.during),null!=e.idr&&(t.idr=e.idr),null!=e.rotation&&(t.rotation=e.rotation),null!=e.len&&(t.len=e.len),t}toObject(){const e={};return null!=this.timestamp&&(e.timestamp=this.timestamp),null!=this.during&&(e.during=this.during),null!=this.idr&&(e.idr=this.idr),null!=this.rotation&&(e.rotation=this.rotation),null!=this.len&&(e.len=this.len),e}serialize(i){const s=i||new t.BinaryWriter;if(0!=this.timestamp&&s.writeSint64(1,this.timestamp),0!=this.during&&s.writeSint32(2,this.during),0!=this.idr&&s.writeBool(3,this.idr),this.rotation!=e.DeviceRotation.Rotation_0&&s.writeEnum(4,this.rotation),0!=this.len&&s.writeSint64(5,this.len),!i)return s.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new z;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.timestamp=i.readSint64();break;case 2:s.during=i.readSint32();break;case 3:s.idr=i.readBool();break;case 4:s.rotation=i.readEnum();break;case 5:s.len=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return z.deserialize(e)}}m=new WeakMap,i.StreamVideoHead=z;class B extends t.Message{constructor(e){super(),g.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,g,"f")),Array.isArray(e)||"object"!=typeof e||("timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp),"during"in e&&null!=e.during&&(this.during=e.during),"len"in e&&null!=e.len&&(this.len=e.len))}get timestamp(){return t.Message.getFieldWithDefault(this,1,0)}set timestamp(e){t.Message.setField(this,1,e)}get during(){return t.Message.getFieldWithDefault(this,2,0)}set during(e){t.Message.setField(this,2,e)}get len(){return t.Message.getFieldWithDefault(this,3,0)}set len(e){t.Message.setField(this,3,e)}static fromObject(e){const t=new B({});return null!=e.timestamp&&(t.timestamp=e.timestamp),null!=e.during&&(t.during=e.during),null!=e.len&&(t.len=e.len),t}toObject(){const e={};return null!=this.timestamp&&(e.timestamp=this.timestamp),null!=this.during&&(e.during=this.during),null!=this.len&&(e.len=this.len),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.timestamp&&i.writeSint64(1,this.timestamp),0!=this.during&&i.writeSint32(2,this.during),0!=this.len&&i.writeSint64(3,this.len),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new B;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.timestamp=i.readSint64();break;case 2:s.during=i.readSint32();break;case 3:s.len=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return B.deserialize(e)}}g=new WeakMap,i.StreamAudioHead=B;class W extends t.Message{constructor(e){super(),_.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,_,"f")),Array.isArray(e)||"object"!=typeof e||("timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp),"video_timestamp"in e&&null!=e.video_timestamp&&(this.video_timestamp=e.video_timestamp))}get timestamp(){return t.Message.getFieldWithDefault(this,1,0)}set timestamp(e){t.Message.setField(this,1,e)}get video_timestamp(){return t.Message.getFieldWithDefault(this,2,0)}set video_timestamp(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new W({});return null!=e.timestamp&&(t.timestamp=e.timestamp),null!=e.video_timestamp&&(t.video_timestamp=e.video_timestamp),t}toObject(){const e={};return null!=this.timestamp&&(e.timestamp=this.timestamp),null!=this.video_timestamp&&(e.video_timestamp=this.video_timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.timestamp&&i.writeSint64(1,this.timestamp),0!=this.video_timestamp&&i.writeSint64(2,this.video_timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new W;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.timestamp=i.readSint64();break;case 2:s.video_timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return W.deserialize(e)}}_=new WeakMap,i.StreamVideoReport=W;class x extends t.Message{constructor(e){super(),w.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],l(this,w,"f")),Array.isArray(e)||"object"!=typeof e||("report"in e&&null!=e.report&&(this.report=e.report),"video_timestamp"in e&&null!=e.video_timestamp&&(this.video_timestamp=e.video_timestamp))}get report(){return t.Message.getWrapperField(this,W,1)}set report(e){t.Message.setWrapperField(this,1,e)}get has_report(){return null!=t.Message.getField(this,1)}get video_timestamp(){return t.Message.getFieldWithDefault(this,2,0)}set video_timestamp(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new x({});return null!=e.report&&(t.report=W.fromObject(e.report)),null!=e.video_timestamp&&(t.video_timestamp=e.video_timestamp),t}toObject(){const e={};return null!=this.report&&(e.report=this.report.toObject()),null!=this.video_timestamp&&(e.video_timestamp=this.video_timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_report&&i.writeMessage(1,this.report,(()=>this.report.serialize(i))),0!=this.video_timestamp&&i.writeSint64(2,this.video_timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new x;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.report,(()=>s.report=W.deserialize(i)));break;case 2:s.video_timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return x.deserialize(e)}}w=new WeakMap,i.StreamVideoReportNotify=x}(a||(a={}));var c,h,p,u,d,f=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))},y=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},m=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};!function(e){e[e.ROTATION_0=0]="ROTATION_0",e[e.ROTATION_90=90]="ROTATION_90",e[e.ROTATION_180=180]="ROTATION_180",e[e.ROTATION_270=270]="ROTATION_270"}(h||(h={}));class g{constructor(){this.log_input=!1,this.log_login=!1,this.log_device=!1,this.log_game=!1,this.log_webrtc=!1,this.log_media=!1,this.log_media_status=!1}}!function(e){e[e.OK=0]="OK",e[e.NotConnected=1]="NotConnected",e[e.NotLogin=2]="NotLogin",e[e.ErrorParams=3]="ErrorParams",e[e.ErrorLoadResource=4]="ErrorLoadResource",e[e.ErrorMedia=5]="ErrorMedia",e[e.ErrorWebRTC=6]="ErrorWebRTC",e[e.ErrorKickout=7]="ErrorKickout",e[e.ErrorTimeOut=8]="ErrorTimeOut"}(p||(p={}));class _{constructor(e,t){this.LDErrorCode=e,this.message=t}static Error(e,t){return new _(e,t)}IsSuccess(){return this.LDErrorCode==p.OK}}_.Success=new _(p.OK,""),_.Timeout=new _(p.ErrorTimeOut,"Message Timeout"),_.NotLogin=new _(p.NotLogin,"Not login"),function(e){e[e.IMEInputTypeUnknown=0]="IMEInputTypeUnknown",e[e.IMEInputTypeNumber=1]="IMEInputTypeNumber",e[e.IMEInputTypeNumberSign=2]="IMEInputTypeNumberSign",e[e.IMEInputTypeNumberDecimal=3]="IMEInputTypeNumberDecimal",e[e.IMEInputTypePhone=4]="IMEInputTypePhone",e[e.IMEInputTypePassword=5]="IMEInputTypePassword",e[e.IMEInputTypeText=6]="IMEInputTypeText"}(u||(u={})),function(e){e[e.IMEActonUnknown=0]="IMEActonUnknown",e[e.IMEActionGo=1]="IMEActionGo",e[e.IMEActionDone=2]="IMEActionDone",e[e.IMEActionNext=3]="IMEActionNext",e[e.IMEActionPrevious=5]="IMEActionPrevious",e[e.IMEActionSearch=6]="IMEActionSearch",e[e.IMEActionNone=7]="IMEActionNone",e[e.IMEActionSend=8]="IMEActionSend"}(d||(d={}));class w{}class E{}class v{sucessLogined(){this.logined=!0,this.QueryScreenSize()}GetSeq(){return this.seq||(this.seq=0),this.seq++}AddWaitForResponse(e,t,i,s){var r;this.waitForResponseMap.get(e)||this.waitForResponseMap.set(e,new Map),null===(r=this.waitForResponseMap.get(e))||void 0===r||r.set(t,{resolve:i,reject:s})}RemoveWaitForResponse(e,t){var i;null===(i=this.waitForResponseMap.get(e))||void 0===i||i.delete(t)}GetWaitForResponse(e,t){var i;const s=null===(i=this.waitForResponseMap.get(e))||void 0===i?void 0:i.get(t);return this.RemoveWaitForResponse(e,t),s}reloadLog(){["device","game","input","login","media","webrtc"].forEach((e=>{const t=`log_${e}`;this[`Log${e.charAt(0).toUpperCase()+e.slice(1)}`]=this.debug[t]?console.log:()=>{}})),this.LogMediaStatus=this.debug.log_media_status?console.log:()=>{}}constructor(e,t){this.ws=null,this.logined=!1,this.debug=new g,this.peerConnection=null,this.dataChannel=null,this.waitForResponseMap=new Map,this.responseHandlers=new Map,this.notificationHandles=new Map,c.set(this,0),this.indexs=new Map,this.user_indexs=new Set,this.notify=t,this.url=e,this.SetOnScreenSize(null==t?void 0:t.onScreenSize),this.onClose=null==t?void 0:t.onClose,this.reloadLog()}observeObject(e){Object.keys(e).forEach((t=>{let i=e[t];Object.defineProperty(e,t,{get:()=>i,set(e){i!==e&&(i=e,this.reloadLog())}})}))}hasConnected(){return null!=this.ws&&this.ws.readyState==WebSocket.OPEN}hasLogined(){return this.logined&&this.hasConnected()}SendCommonMessage(e){var t;null!=this.dataChannel&&"open"==this.dataChannel.readyState?this.dataChannel.send(e):null===(t=this.ws)||void 0===t||t.send(e)}OnPbCommonMessage(t){var i,s;if(0!=y(this,c,"f"))return this.meidaHandle(y(this,c,"f"),t),void m(this,c,0,"f");const r=e.CommonMessage.deserialize(t);r.type==e.CommonMessage.Type.Response?null===(i=this.responseHandlers.get(r.sub_type))||void 0===i||i(r):r.type==e.CommonMessage.Type.Notification&&(null===(s=this.notificationHandles.get(r.sub_type))||void 0===s||s(r),r.sub_type==a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_STREAM_INFO&&m(this,c,0,"f"),r.sub_type!=a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_VIDEO_STREAM&&r.sub_type!=a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_AUDIO_STREAM||m(this,c,r.sub_type,"f"))}Connect(){return f(this,void 0,void 0,(function*(){return new Promise(((e,t)=>{this.ws=new WebSocket(this.url);var i=t;this.ws.onopen=()=>{console.log(this.ws),console.log("Connected to server"),i=void 0,e(_.Success)},this.ws.onmessage=e=>f(this,void 0,void 0,(function*(){if(e.data instanceof Blob){const t=e.data,i=new FileReader;i.onload=()=>{const e=i.result,t=new Uint8Array(e);this.OnPbCommonMessage(t)},i.readAsArrayBuffer(t)}})),this.ws.onclose=e=>{if(console.log(e),console.log("websocket 断开: "+e.code+" "+e.reason+" "+e.wasClean),this.CloseMedia(),this.onClose)if(0!=e.reason.length){const t=(new TextEncoder).encode(e.reason);let i=n.KickOut.deserialize(t);this.onClose(_.Error(p.ErrorKickout,JSON.stringify(i.toObject())))}else this.onClose(_.Error(p.NotConnected,"websocket 连接已经断开!"))},this.ws.onerror=e=>{i&&(i(_.Error(p.NotConnected,"Connect to server failed")),i=void 0)}}))}))}Close(){var e;this.logined=!1,null===(e=this.ws)||void 0===e||e.close(),this.ws=null,this.notify=void 0,this.CloseMedia()}LogReport(t){if(!this.hasLogined())return Promise.reject(_.Error(p.NotLogin,"Not login"));const i=new n.ReportInfo({message:t,type:n.ReportInfoType.ForLog}),s=new e.CommonMessage({name:"report",type:e.CommonMessage.Type.Request,sub_type:n.LoginType.LOGIN_TYPE_REPORT_INFO,payload:i.serializeBinary()});return this.SendCommonMessage(s.serializeBinary()),Promise.resolve(_.Success)}}c=new WeakMap;var b,M=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};!function(e){e[e.CLIENT_TYPE_WEB=0]="CLIENT_TYPE_WEB",e[e.CLIENT_TYPE_WEB_H5=1]="CLIENT_TYPE_WEB_H5"}(b||(b={}));class T{}v.prototype.Login=function(e){return M(this,void 0,void 0,(function*(){return e.app_key&&0!=e.app_key.length?this.Login():new Promise(((e,t)=>{t(_.Error(p.ErrorParams,"for B log, authparams can't null"))}))}))},v.prototype.Login=function(t){return M(this,void 0,void 0,(function*(){return this.hasLogined()?new Promise(((e,t)=>{t(_.Error(p.ErrorParams,"Already login"))})):(this.uid=t.uid,this.token=t.token,this.app_key=t.app_key,this.app_key&&0!=this.app_key.length||this.token&&0!=this.token.length?(null==this.responseHandlers.get(n.LoginType.LOGIN_TYPE_LOGIN)&&this.responseHandlers.set(n.LoginType.LOGIN_TYPE_LOGIN,(e=>{const t=n.LoginRes.deserializeBinary(e.payload),i=this.GetWaitForResponse(e.sub_type,e.seq);i&&(0==t.common.code?(this.sucessLogined(),i.resolve(_.Success)):i.resolve(_.Error(p.NotLogin,new TextDecoder("utf-8").decode(t.common.result))))})),new Promise(((i,s)=>{if(!this.hasConnected())return void s(_.Error(p.NotConnected,"Not connected to server"));const r=n.LoginType.LOGIN_TYPE_LOGIN;if(this.waitForResponseMap.get(r))return void s(_.Error(p.ErrorParams,"Already login"));var o=new n.Login;if(o.user_id=this.uid,o.token=this.token,o.app_key=this.app_key,o.device=t.client_type,0==o.device){const e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);o.device=e?n.ClientType.kWeb:n.ClientType.kPCWeb}const a=this.GetSeq();let l=new e.CommonMessage({seq:a,name:"login",type:e.CommonMessage.Type.Request,sub_type:r,payload:o.serializeBinary()});this.ws.send(l.serializeBinary()),this.AddWaitForResponse(r,a,i,s),setTimeout((()=>{const e=this.GetWaitForResponse(r,a);e&&e.resolve(_.Error(p.ErrorTimeOut,"Login timeout"))}),2e3)}))):new Promise(((e,t)=>{t(_.Error(p.ErrorParams,"for login, app_key or token can't null"))})))}))},v.prototype.KickoutOthers=function(){return M(this,void 0,void 0,(function*(){if(!this.hasLogined())return Promise.resolve(_.NotLogin);const t=this.GetSeq();let i=new e.CommonMessage({seq:t,name:"kickout_others",type:e.CommonMessage.Type.Request,sub_type:n.LoginType.LOGIN_TYPE_KICKOUT_OTHERS});return this.ws.send(i.serializeBinary()),Promise.resolve(_.Success)}))};var I,A=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(i){var s,r,n,o,l,c,h,p,u,d,f,y,m,g,_,w,E,v,b,M,T;let I,O,S,F,C;!function(e){e[e.DEVICE_TYPE_NONE=0]="DEVICE_TYPE_NONE",e[e.DEVICE_TYPE_MIN=4e4]="DEVICE_TYPE_MIN",e[e.DEVICE_TYPE_SENSOR_DATA=40001]="DEVICE_TYPE_SENSOR_DATA",e[e.DEVICE_TYPE_TRANSFER_FILE=40002]="DEVICE_TYPE_TRANSFER_FILE",e[e.DEVICE_TYPE_ADB_COMMAND=40003]="DEVICE_TYPE_ADB_COMMAND",e[e.DEVICE_TYPE_GET_INSTALLED_PACKAGE=40005]="DEVICE_TYPE_GET_INSTALLED_PACKAGE",e[e.DEVICE_TYPE_SCREEN_CAP=40006]="DEVICE_TYPE_SCREEN_CAP",e[e.DEVICE_TYPE_CAMERA_STREAM_INFO=40007]="DEVICE_TYPE_CAMERA_STREAM_INFO",e[e.DEVICE_TYPE_RECORD_STREAM_INFO=40008]="DEVICE_TYPE_RECORD_STREAM_INFO",e[e.DEVICE_TYPE_ENABLE_SENSOR=40009]="DEVICE_TYPE_ENABLE_SENSOR",e[e.DEVICE_TYPE_RESOURCE_DETAIL=40010]="DEVICE_TYPE_RESOURCE_DETAIL",e[e.DEVICE_TYPE_TOMBSTONES_OBSERVATION=40011]="DEVICE_TYPE_TOMBSTONES_OBSERVATION",e[e.DEVICE_TYPE_QUERY_DISK=40012]="DEVICE_TYPE_QUERY_DISK",e[e.DEVICE_TYPE_SWITCH_SYSTEM_LOCALE=40013]="DEVICE_TYPE_SWITCH_SYSTEM_LOCALE",e[e.DEVICE_TYPE_SHAKE=40014]="DEVICE_TYPE_SHAKE",e[e.DEVICE_TYPE_QUEUE_SCREEN=40015]="DEVICE_TYPE_QUEUE_SCREEN",e[e.DEVICE_TYPE_CHANGE_SCREEN_SIZE=40016]="DEVICE_TYPE_CHANGE_SCREEN_SIZE",e[e.DEVICE_TYPE_GET_ACTIVE_PACKAGE=40017]="DEVICE_TYPE_GET_ACTIVE_PACKAGE",e[e.DEVICE_TYPE_MAX=5e4]="DEVICE_TYPE_MAX"}(I=i.DeviceType||(i.DeviceType={})),function(e){e[e.DEVICE_NOTIFICATION_NONE=0]="DEVICE_NOTIFICATION_NONE",e[e.DEVICE_NOTIFICATION_TYPE_MIN=4e4]="DEVICE_NOTIFICATION_TYPE_MIN",e[e.DEVICE_NOTIFICATION_RESTART=40001]="DEVICE_NOTIFICATION_RESTART",e[e.DEVICE_NOTIFICATION_START_CAMERA=40002]="DEVICE_NOTIFICATION_START_CAMERA",e[e.DEVICE_NOTIFICATION_STOP_CAMERA=40003]="DEVICE_NOTIFICATION_STOP_CAMERA",e[e.DEVICE_NOTIFICATION_RESTART_SELF=40004]="DEVICE_NOTIFICATION_RESTART_SELF",e[e.DEVICE_NOTIFICATION_NOT_ENOUGH_DISK_SPACE=40007]="DEVICE_NOTIFICATION_NOT_ENOUGH_DISK_SPACE",e[e.DEVICE_NOTIFICATION_DEVICE_INITIALIZING=4008]="DEVICE_NOTIFICATION_DEVICE_INITIALIZING",e[e.DEVICE_NOTIFICATION_START_RECORD=4009]="DEVICE_NOTIFICATION_START_RECORD",e[e.DEVICE_NOTIFICATION_STOP_RECORD=40010]="DEVICE_NOTIFICATION_STOP_RECORD",e[e.DEVICE_NOTIFICATION_START_SENSORS=40011]="DEVICE_NOTIFICATION_START_SENSORS",e[e.DEVICE_NOTIFICATION_STOP_SENSORS=40012]="DEVICE_NOTIFICATION_STOP_SENSORS",e[e.DEVICE_NOTIFICATION_ORIENTATION=40013]="DEVICE_NOTIFICATION_ORIENTATION",e[e.DEVICE_NOTIFICATION_SCREEN=40014]="DEVICE_NOTIFICATION_SCREEN",e[e.DEVICE_NOTIFICATION_PUSH_MSG=40015]="DEVICE_NOTIFICATION_PUSH_MSG",e[e.DEVICE_NOTIFICATION_ACTIVE_PACKAGE=40016]="DEVICE_NOTIFICATION_ACTIVE_PACKAGE",e[e.DEVICE_NOTIFICATION_TYPE_MAX=5e4]="DEVICE_NOTIFICATION_TYPE_MAX"}(O=i.DeviceNotificationType||(i.DeviceNotificationType={})),function(e){e[e.CAP_JPEG=0]="CAP_JPEG",e[e.CAP_WEBP=1]="CAP_WEBP",e[e.CAP_PNG=2]="CAP_PNG"}(S=i.CaptureFormat||(i.CaptureFormat={})),function(e){e[e.NULL_INFO=0]="NULL_INFO",e[e.PACKAGE=1]="PACKAGE",e[e.ACTIVITY=2]="ACTIVITY"}(F=i.PacketType||(i.PacketType={})),function(e){e[e.Front=0]="Front",e[e.Back=1]="Back"}(C=i.CameraType||(i.CameraType={}));class k extends t.Message{constructor(e){super(),s.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,s,"f")),Array.isArray(e)||"object"!=typeof e||("type"in e&&null!=e.type&&(this.type=e.type),"x"in e&&null!=e.x&&(this.x=e.x),"y"in e&&null!=e.y&&(this.y=e.y),"z"in e&&null!=e.z&&(this.z=e.z),"accuracy"in e&&null!=e.accuracy&&(this.accuracy=e.accuracy))}get type(){return t.Message.getFieldWithDefault(this,1,k.SensorType.GRAVITY)}set type(e){t.Message.setField(this,1,e)}get x(){return t.Message.getFieldWithDefault(this,2,0)}set x(e){t.Message.setField(this,2,e)}get y(){return t.Message.getFieldWithDefault(this,3,0)}set y(e){t.Message.setField(this,3,e)}get z(){return t.Message.getFieldWithDefault(this,4,0)}set z(e){t.Message.setField(this,4,e)}get accuracy(){return t.Message.getFieldWithDefault(this,5,0)}set accuracy(e){t.Message.setField(this,5,e)}static fromObject(e){const t=new k({});return null!=e.type&&(t.type=e.type),null!=e.x&&(t.x=e.x),null!=e.y&&(t.y=e.y),null!=e.z&&(t.z=e.z),null!=e.accuracy&&(t.accuracy=e.accuracy),t}toObject(){const e={};return null!=this.type&&(e.type=this.type),null!=this.x&&(e.x=this.x),null!=this.y&&(e.y=this.y),null!=this.z&&(e.z=this.z),null!=this.accuracy&&(e.accuracy=this.accuracy),e}serialize(e){const i=e||new t.BinaryWriter;if(this.type!=k.SensorType.GRAVITY&&i.writeEnum(1,this.type),0!=this.x&&i.writeFloat(2,this.x),0!=this.y&&i.writeFloat(3,this.y),0!=this.z&&i.writeFloat(4,this.z),0!=this.accuracy&&i.writeFloat(5,this.accuracy),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new k;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.type=i.readEnum();break;case 2:s.x=i.readFloat();break;case 3:s.y=i.readFloat();break;case 4:s.z=i.readFloat();break;case 5:s.accuracy=i.readFloat();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return k.deserialize(e)}}s=new WeakMap,i.SensorData=k,function(e){let t;!function(e){e[e.GRAVITY=0]="GRAVITY",e[e.GYRO=1]="GYRO"}(t=e.SensorType||(e.SensorType={}))}(k=i.SensorData||(i.SensorData={}));class D extends t.Message{constructor(e){super(),r.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,r,"f")),Array.isArray(e)||"object"!=typeof e||"command"in e&&null!=e.command&&(this.command=e.command)}get command(){return t.Message.getFieldWithDefault(this,1,"")}set command(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new D({});return null!=e.command&&(t.command=e.command),t}toObject(){const e={};return null!=this.command&&(e.command=this.command),e}serialize(e){const i=e||new t.BinaryWriter;if(this.command.length&&i.writeString(1,this.command),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new D;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.command=i.readString():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return D.deserialize(e)}}r=new WeakMap,i.AdbCommand=D;class R extends t.Message{constructor(e){super(),n.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,n,"f")),Array.isArray(e)||"object"!=typeof e||("type"in e&&null!=e.type&&(this.type=e.type),"url"in e&&null!=e.url&&(this.url=e.url))}get type(){return t.Message.getFieldWithDefault(this,1,R.TrasnsferFileExecType.ET_NONE)}set type(e){t.Message.setField(this,1,e)}get url(){return t.Message.getFieldWithDefault(this,2,"")}set url(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new R({});return null!=e.type&&(t.type=e.type),null!=e.url&&(t.url=e.url),t}toObject(){const e={};return null!=this.type&&(e.type=this.type),null!=this.url&&(e.url=this.url),e}serialize(e){const i=e||new t.BinaryWriter;if(this.type!=R.TrasnsferFileExecType.ET_NONE&&i.writeEnum(1,this.type),this.url.length&&i.writeString(2,this.url),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new R;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.type=i.readEnum();break;case 2:s.url=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return R.deserialize(e)}}n=new WeakMap,i.TransferFile=R,function(e){let t;!function(e){e[e.ET_NONE=0]="ET_NONE",e[e.ET_INSTALL=1]="ET_INSTALL",e[e.ET_QRCODE=2]="ET_QRCODE"}(t=e.TrasnsferFileExecType||(e.TrasnsferFileExecType={}))}(R=i.TransferFile||(i.TransferFile={}));class N extends t.Message{constructor(e){super(),o.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,o,"f")),Array.isArray(e)||"object"!=typeof e||("scale"in e&&null!=e.scale&&(this.scale=e.scale),"quality"in e&&null!=e.quality&&(this.quality=e.quality),"format"in e&&null!=e.format&&(this.format=e.format),"size"in e&&null!=e.size&&(this.size=e.size))}get scale(){return t.Message.getFieldWithDefault(this,1,0)}set scale(e){t.Message.setField(this,1,e)}get quality(){return t.Message.getFieldWithDefault(this,2,0)}set quality(e){t.Message.setField(this,2,e)}get format(){return t.Message.getFieldWithDefault(this,3,S.CAP_JPEG)}set format(e){t.Message.setField(this,3,e)}get size(){return t.Message.getWrapperField(this,N.Size,4)}set size(e){t.Message.setWrapperField(this,4,e)}get has_size(){return null!=t.Message.getField(this,4)}static fromObject(e){const t=new N({});return null!=e.scale&&(t.scale=e.scale),null!=e.quality&&(t.quality=e.quality),null!=e.format&&(t.format=e.format),null!=e.size&&(t.size=N.Size.fromObject(e.size)),t}toObject(){const e={};return null!=this.scale&&(e.scale=this.scale),null!=this.quality&&(e.quality=this.quality),null!=this.format&&(e.format=this.format),null!=this.size&&(e.size=this.size.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.scale&&i.writeSint32(1,this.scale),0!=this.quality&&i.writeSint32(2,this.quality),this.format!=S.CAP_JPEG&&i.writeEnum(3,this.format),this.has_size&&i.writeMessage(4,this.size,(()=>this.size.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new N;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.scale=i.readSint32();break;case 2:s.quality=i.readSint32();break;case 3:s.format=i.readEnum();break;case 4:i.readMessage(s.size,(()=>s.size=N.Size.deserialize(i)));break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return N.deserialize(e)}}o=new WeakMap,i.ScreenCap=N,function(e){var i;class s extends t.Message{constructor(e){super(),i.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,i,"f")),Array.isArray(e)||"object"!=typeof e||("width"in e&&null!=e.width&&(this.width=e.width),"height"in e&&null!=e.height&&(this.height=e.height))}get width(){return t.Message.getFieldWithDefault(this,1,0)}set width(e){t.Message.setField(this,1,e)}get height(){return t.Message.getFieldWithDefault(this,2,0)}set height(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new s({});return null!=e.width&&(t.width=e.width),null!=e.height&&(t.height=e.height),t}toObject(){const e={};return null!=this.width&&(e.width=this.width),null!=this.height&&(e.height=this.height),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.width&&i.writeSint32(1,this.width),0!=this.height&&i.writeSint32(2,this.height),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),r=new s;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:r.width=i.readSint32();break;case 2:r.height=i.readSint32();break;default:i.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return s.deserialize(e)}}i=new WeakMap,e.Size=s}(N=i.ScreenCap||(i.ScreenCap={}));class P extends t.Message{constructor(e){super(),l.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,l,"f")),Array.isArray(e)||"object"!=typeof e||"rotation"in e&&null!=e.rotation&&(this.rotation=e.rotation)}get rotation(){return t.Message.getFieldWithDefault(this,1,e.DeviceRotation.Rotation_0)}set rotation(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new P({});return null!=e.rotation&&(t.rotation=e.rotation),t}toObject(){const e={};return null!=this.rotation&&(e.rotation=this.rotation),e}serialize(i){const s=i||new t.BinaryWriter;if(this.rotation!=e.DeviceRotation.Rotation_0&&s.writeEnum(1,this.rotation),!i)return s.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new P;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.rotation=i.readEnum():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return P.deserialize(e)}}l=new WeakMap,i.RotationChange=P;class z extends t.Message{constructor(e){super(),c.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,c,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"format"in e&&null!=e.format&&(this.format=e.format),"content"in e&&null!=e.content&&(this.content=e.content),"rotation"in e&&null!=e.rotation&&(this.rotation=e.rotation),"pic_rotation"in e&&null!=e.pic_rotation&&(this.pic_rotation=e.pic_rotation))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get format(){return t.Message.getFieldWithDefault(this,2,S.CAP_JPEG)}set format(e){t.Message.setField(this,2,e)}get content(){return t.Message.getFieldWithDefault(this,3,new Uint8Array(0))}set content(e){t.Message.setField(this,3,e)}get rotation(){return t.Message.getFieldWithDefault(this,4,e.DeviceRotation.Rotation_0)}set rotation(e){t.Message.setField(this,4,e)}get pic_rotation(){return t.Message.getFieldWithDefault(this,5,e.DeviceRotation.Rotation_0)}set pic_rotation(e){t.Message.setField(this,5,e)}static fromObject(t){const i=new z({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.format&&(i.format=t.format),null!=t.content&&(i.content=t.content),null!=t.rotation&&(i.rotation=t.rotation),null!=t.pic_rotation&&(i.pic_rotation=t.pic_rotation),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.format&&(e.format=this.format),null!=this.content&&(e.content=this.content),null!=this.rotation&&(e.rotation=this.rotation),null!=this.pic_rotation&&(e.pic_rotation=this.pic_rotation),e}serialize(i){const s=i||new t.BinaryWriter;if(this.has_common&&s.writeMessage(1,this.common,(()=>this.common.serialize(s))),this.format!=S.CAP_JPEG&&s.writeEnum(2,this.format),this.content.length&&s.writeBytes(3,this.content),this.rotation!=e.DeviceRotation.Rotation_0&&s.writeEnum(4,this.rotation),this.pic_rotation!=e.DeviceRotation.Rotation_0&&s.writeEnum(5,this.pic_rotation),!i)return s.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new z;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:r.format=s.readEnum();break;case 3:r.content=s.readBytes();break;case 4:r.rotation=s.readEnum();break;case 5:r.pic_rotation=s.readEnum();break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return z.deserialize(e)}}c=new WeakMap,i.ScreenCapRes=z;class B extends t.Message{constructor(e){super(),h.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,h,"f")),Array.isArray(e)||"object"!=typeof e||"type"in e&&null!=e.type&&(this.type=e.type)}get type(){return t.Message.getFieldWithDefault(this,1,F.NULL_INFO)}set type(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new B({});return null!=e.type&&(t.type=e.type),t}toObject(){const e={};return null!=this.type&&(e.type=this.type),e}serialize(e){const i=e||new t.BinaryWriter;if(this.type!=F.NULL_INFO&&i.writeEnum(1,this.type),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new B;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.type=i.readEnum():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return B.deserialize(e)}}h=new WeakMap,i.GetInstalledPackage=B;class W extends t.Message{constructor(e){super(),p.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,p,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"type"in e&&null!=e.type&&(this.type=e.type),"content"in e&&null!=e.content&&(this.content=e.content))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get type(){return t.Message.getFieldWithDefault(this,2,F.NULL_INFO)}set type(e){t.Message.setField(this,2,e)}get content(){return t.Message.getFieldWithDefault(this,3,"")}set content(e){t.Message.setField(this,3,e)}static fromObject(t){const i=new W({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.type&&(i.type=t.type),null!=t.content&&(i.content=t.content),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.type&&(e.type=this.type),null!=this.content&&(e.content=this.content),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),this.type!=F.NULL_INFO&&i.writeEnum(2,this.type),this.content.length&&i.writeString(3,this.content),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new W;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:r.type=s.readEnum();break;case 3:r.content=s.readString();break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return W.deserialize(e)}}p=new WeakMap,i.GetInstalledPackageRes=W;class x extends t.Message{constructor(e){super(),u.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,u,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"memory_total"in e&&null!=e.memory_total&&(this.memory_total=e.memory_total),"memory_used"in e&&null!=e.memory_used&&(this.memory_used=e.memory_used),"disk_total"in e&&null!=e.disk_total&&(this.disk_total=e.disk_total),"disk_used"in e&&null!=e.disk_used&&(this.disk_used=e.disk_used),"disk_iowait"in e&&null!=e.disk_iowait&&(this.disk_iowait=e.disk_iowait))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get memory_total(){return t.Message.getFieldWithDefault(this,2,0)}set memory_total(e){t.Message.setField(this,2,e)}get memory_used(){return t.Message.getFieldWithDefault(this,3,0)}set memory_used(e){t.Message.setField(this,3,e)}get disk_total(){return t.Message.getFieldWithDefault(this,4,0)}set disk_total(e){t.Message.setField(this,4,e)}get disk_used(){return t.Message.getFieldWithDefault(this,5,0)}set disk_used(e){t.Message.setField(this,5,e)}get disk_iowait(){return t.Message.getFieldWithDefault(this,6,0)}set disk_iowait(e){t.Message.setField(this,6,e)}static fromObject(t){const i=new x({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.memory_total&&(i.memory_total=t.memory_total),null!=t.memory_used&&(i.memory_used=t.memory_used),null!=t.disk_total&&(i.disk_total=t.disk_total),null!=t.disk_used&&(i.disk_used=t.disk_used),null!=t.disk_iowait&&(i.disk_iowait=t.disk_iowait),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.memory_total&&(e.memory_total=this.memory_total),null!=this.memory_used&&(e.memory_used=this.memory_used),null!=this.disk_total&&(e.disk_total=this.disk_total),null!=this.disk_used&&(e.disk_used=this.disk_used),null!=this.disk_iowait&&(e.disk_iowait=this.disk_iowait),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),0!=this.memory_total&&i.writeUint32(2,this.memory_total),0!=this.memory_used&&i.writeUint32(3,this.memory_used),0!=this.disk_total&&i.writeUint32(4,this.disk_total),0!=this.disk_used&&i.writeUint32(5,this.disk_used),0!=this.disk_iowait&&i.writeFloat(6,this.disk_iowait),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new x;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:r.memory_total=s.readUint32();break;case 3:r.memory_used=s.readUint32();break;case 4:r.disk_total=s.readUint32();break;case 5:r.disk_used=s.readUint32();break;case 6:r.disk_iowait=s.readFloat();break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return x.deserialize(e)}}u=new WeakMap,i.ResourceDetailRes=x;class L extends t.Message{constructor(e){super(),d.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,d,"f")),Array.isArray(e)||"object"!=typeof e||"level"in e&&null!=e.level&&(this.level=e.level)}get level(){return t.Message.getFieldWithDefault(this,1,0)}set level(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new L({});return null!=e.level&&(t.level=e.level),t}toObject(){const e={};return null!=this.level&&(e.level=this.level),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.level&&i.writeUint32(1,this.level),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new L;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.level=i.readUint32():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return L.deserialize(e)}}d=new WeakMap,i.TombstonesObservation=L;class j extends t.Message{constructor(e){super(),f.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,f,"f")),Array.isArray(e)||"object"!=typeof e||"sys_locale"in e&&null!=e.sys_locale&&(this.sys_locale=e.sys_locale)}get sys_locale(){return t.Message.getFieldWithDefault(this,1,"")}set sys_locale(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new j({});return null!=e.sys_locale&&(t.sys_locale=e.sys_locale),t}toObject(){const e={};return null!=this.sys_locale&&(e.sys_locale=this.sys_locale),e}serialize(e){const i=e||new t.BinaryWriter;if(this.sys_locale.length&&i.writeString(1,this.sys_locale),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new j;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.sys_locale=i.readString():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return j.deserialize(e)}}f=new WeakMap,i.SystemLocale=j;class G extends t.Message{constructor(e){super(),y.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,y,"f")),Array.isArray(e)||"object"!=typeof e||("screen_width"in e&&null!=e.screen_width&&(this.screen_width=e.screen_width),"screen_height"in e&&null!=e.screen_height&&(this.screen_height=e.screen_height))}get screen_width(){return t.Message.getFieldWithDefault(this,1,0)}set screen_width(e){t.Message.setField(this,1,e)}get screen_height(){return t.Message.getFieldWithDefault(this,2,0)}set screen_height(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new G({});return null!=e.screen_width&&(t.screen_width=e.screen_width),null!=e.screen_height&&(t.screen_height=e.screen_height),t}toObject(){const e={};return null!=this.screen_width&&(e.screen_width=this.screen_width),null!=this.screen_height&&(e.screen_height=this.screen_height),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.screen_width&&i.writeUint32(1,this.screen_width),0!=this.screen_height&&i.writeUint32(2,this.screen_height),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new G;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.screen_width=i.readUint32();break;case 2:s.screen_height=i.readUint32();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return G.deserialize(e)}}y=new WeakMap,i.Screen=G;class V extends t.Message{constructor(e){super(),m.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,m,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"screen"in e&&null!=e.screen&&(this.screen=e.screen))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get screen(){return t.Message.getWrapperField(this,G,2)}set screen(e){t.Message.setWrapperField(this,2,e)}get has_screen(){return null!=t.Message.getField(this,2)}static fromObject(t){const i=new V({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.screen&&(i.screen=G.fromObject(t.screen)),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.screen&&(e.screen=this.screen.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),this.has_screen&&i.writeMessage(2,this.screen,(()=>this.screen.serialize(i))),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new V;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:s.readMessage(r.screen,(()=>r.screen=G.deserialize(s)));break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return V.deserialize(e)}}m=new WeakMap,i.QueryScreenRes=V;class U extends t.Message{constructor(e){super(),g.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,g,"f")),Array.isArray(e)||"object"!=typeof e||"screen"in e&&null!=e.screen&&(this.screen=e.screen)}get screen(){return t.Message.getWrapperField(this,G,2)}set screen(e){t.Message.setWrapperField(this,2,e)}get has_screen(){return null!=t.Message.getField(this,2)}static fromObject(e){const t=new U({});return null!=e.screen&&(t.screen=G.fromObject(e.screen)),t}toObject(){const e={};return null!=this.screen&&(e.screen=this.screen.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_screen&&i.writeMessage(2,this.screen,(()=>this.screen.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new U;for(;i.nextField()&&!i.isEndGroup();)2===i.getFieldNumber()?i.readMessage(s.screen,(()=>s.screen=G.deserialize(i))):i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return U.deserialize(e)}}g=new WeakMap,i.ChangeScreenSize=U;class Y extends t.Message{constructor(e){super(),_.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,_,"f")),Array.isArray(e)||"object"!=typeof e||("int_msg"in e&&null!=e.int_msg&&(this.int_msg=e.int_msg),"str_msg"in e&&null!=e.str_msg&&(this.str_msg=e.str_msg))}get int_msg(){return t.Message.getFieldWithDefault(this,1,0)}set int_msg(e){t.Message.setField(this,1,e)}get str_msg(){return t.Message.getFieldWithDefault(this,2,"")}set str_msg(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new Y({});return null!=e.int_msg&&(t.int_msg=e.int_msg),null!=e.str_msg&&(t.str_msg=e.str_msg),t}toObject(){const e={};return null!=this.int_msg&&(e.int_msg=this.int_msg),null!=this.str_msg&&(e.str_msg=this.str_msg),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.int_msg&&i.writeUint32(1,this.int_msg),this.str_msg.length&&i.writeString(2,this.str_msg),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new Y;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.int_msg=i.readUint32();break;case 2:s.str_msg=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return Y.deserialize(e)}}_=new WeakMap,i.PushMsg=Y;class H extends t.Message{constructor(e){super(),w.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,w,"f")),Array.isArray(e)||"object"!=typeof e||("info"in e&&null!=e.info&&(this.info=e.info),"desc"in e&&null!=e.desc&&(this.desc=e.desc))}get info(){return t.Message.getWrapperField(this,a.StreamAudioInfo,1)}set info(e){t.Message.setWrapperField(this,1,e)}get has_info(){return null!=t.Message.getField(this,1)}get desc(){return t.Message.getFieldWithDefault(this,2,"")}set desc(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new H({});return null!=e.info&&(t.info=a.StreamAudioInfo.fromObject(e.info)),null!=e.desc&&(t.desc=e.desc),t}toObject(){const e={};return null!=this.info&&(e.info=this.info.toObject()),null!=this.desc&&(e.desc=this.desc),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_info&&i.writeMessage(1,this.info,(()=>this.info.serialize(i))),this.desc.length&&i.writeString(2,this.desc),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new H;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.info,(()=>s.info=a.StreamAudioInfo.deserialize(i)));break;case 2:s.desc=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return H.deserialize(e)}}w=new WeakMap,i.RecordStart=H;class q extends t.Message{constructor(e){super(),E.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,E,"f")),Array.isArray(e)||"object"!=typeof e||("info"in e&&null!=e.info&&(this.info=e.info),"type"in e&&null!=e.type&&(this.type=e.type))}get info(){return t.Message.getWrapperField(this,a.StreamVideoInfo,1)}set info(e){t.Message.setWrapperField(this,1,e)}get has_info(){return null!=t.Message.getField(this,1)}get type(){return t.Message.getFieldWithDefault(this,2,C.Front)}set type(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new q({});return null!=e.info&&(t.info=a.StreamVideoInfo.fromObject(e.info)),null!=e.type&&(t.type=e.type),t}toObject(){const e={};return null!=this.info&&(e.info=this.info.toObject()),null!=this.type&&(e.type=this.type),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_info&&i.writeMessage(1,this.info,(()=>this.info.serialize(i))),this.type!=C.Front&&i.writeEnum(2,this.type),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new q;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.info,(()=>s.info=a.StreamVideoInfo.deserialize(i)));break;case 2:s.type=i.readEnum();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return q.deserialize(e)}}E=new WeakMap,i.CameraStart=q;class K extends t.Message{constructor(e){super(),v.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,v,"f")),Array.isArray(e)}static fromObject(e){return new K({})}toObject(){return{}}serialize(e){const i=e||new t.BinaryWriter;if(!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new K;for(;i.nextField()&&!i.isEndGroup();)i.getFieldNumber(),i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return K.deserialize(e)}}v=new WeakMap,i.RecordStop=K;class X extends t.Message{constructor(e){super(),b.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,b,"f")),Array.isArray(e)}static fromObject(e){return new X({})}toObject(){return{}}serialize(e){const i=e||new t.BinaryWriter;if(!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new X;for(;i.nextField()&&!i.isEndGroup();)i.getFieldNumber(),i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return X.deserialize(e)}}b=new WeakMap,i.CameraStop=X;class J extends t.Message{constructor(e){super(),M.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,M,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"alarm"in e&&null!=e.alarm&&(this.alarm=e.alarm),"available"in e&&null!=e.available&&(this.available=e.available))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get alarm(){return t.Message.getFieldWithDefault(this,2,!1)}set alarm(e){t.Message.setField(this,2,e)}get available(){return t.Message.getFieldWithDefault(this,3,0)}set available(e){t.Message.setField(this,3,e)}static fromObject(t){const i=new J({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.alarm&&(i.alarm=t.alarm),null!=t.available&&(i.available=t.available),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.alarm&&(e.alarm=this.alarm),null!=this.available&&(e.available=this.available),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),0!=this.alarm&&i.writeBool(2,this.alarm),0!=this.available&&i.writeFloat(3,this.available),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new J;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:r.alarm=s.readBool();break;case 3:r.available=s.readFloat();break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return J.deserialize(e)}}M=new WeakMap,i.QueryDiskRes=J;class Q extends t.Message{constructor(e){super(),T.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],A(this,T,"f")),Array.isArray(e)||"object"!=typeof e||"package_name"in e&&null!=e.package_name&&(this.package_name=e.package_name)}get package_name(){return t.Message.getFieldWithDefault(this,1,"")}set package_name(e){t.Message.setField(this,1,e)}static fromObject(e){const t=new Q({});return null!=e.package_name&&(t.package_name=e.package_name),t}toObject(){const e={};return null!=this.package_name&&(e.package_name=this.package_name),e}serialize(e){const i=e||new t.BinaryWriter;if(this.package_name.length&&i.writeString(1,this.package_name),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new Q;for(;i.nextField()&&!i.isEndGroup();)1===i.getFieldNumber()?s.package_name=i.readString():i.skipField();return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return Q.deserialize(e)}}T=new WeakMap,i.ActivePackage=Q}(I||(I={})),new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap;Error,new WeakMap,new WeakMap,new WeakMap,new WeakMap;class O{constructor(e,t,i){this.type=e,this.timestamp=t,this.data=i,this.video=!1}static decode(e){return t=this,i=void 0,r=function*(){e.index;const t=yield e.readFrame();t&&t.byteLength},new((s=void 0)||(s=Promise))((function(e,n){function o(e){try{l(r.next(e))}catch(e){n(e)}}function a(e){try{l(r.throw(e))}catch(e){n(e)}}function l(t){var i;t.done?e(t.value):(i=t.value,i instanceof s?i:new s((function(e){e(i)}))).then(o,a)}l((r=r.apply(t,i||[])).next())}));var t,i,s,r}}var S,F,C;!function(e){e[e.READ_POS=0]="READ_POS",e[e.WRITE_POS=1]="WRITE_POS",e[e.LENGTH=2]="LENGTH"}(C||(C={}));class k{constructor(e,t){this.state=new SharedArrayBuffer(C.LENGTH*Int32Array.BYTES_PER_ELEMENT),this.channels=[];for(let i=0;i<e;i+=1){const e=new SharedArrayBuffer(t*Float32Array.BYTES_PER_ELEMENT);this.channels.push(e)}this.capacity=t}}class D{constructor(e,t,i){if(S.add(this),this.startCache=9600,e){this.state=new Int32Array(e.state),this.channels=[];for(const t of e.channels)this.channels.push(new Float32Array(t));this.capacity=e.capacity}else{this.state=new Int32Array(C.LENGTH),this.channels=[];for(let e=0;e<t;e+=1){const e=new Float32Array(i);this.channels.push(e)}this.capacity=i}for(let e=0;e<C.LENGTH;e+=1)this.state[e]=0;this.start=!0,this.startCache=12e3}write(e){const t=Atomics.load(this.state,C.READ_POS),i=Atomics.load(this.state,C.WRITE_POS),s=this.capacity-(i-t);if(s<=0)return 0;const r=Math.min(e.numberOfFrames,s),n=i+r,o=i%this.capacity,a=n%this.capacity;return function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)}(this,S,"m",F).call(this,e,o,a),Atomics.store(this.state,C.WRITE_POS,n),r}read(e){const t=Atomics.load(this.state,C.READ_POS),i=Atomics.load(this.state,C.WRITE_POS);if(this.start){if(i-t<this.startCache)return 0;this.start=!1}const s=t;let r=s+e[0].length;if(r>i&&(r=i,r<=s))return this.start=!0,this.startCache=2400,0;const n=s%this.capacity,o=r%this.capacity;for(let t=0;t<1;t+=1){this.channels.length;const i=this.channels[t],s=e[t];if(n<o){const e=i.subarray(n,o);s.set(e)}else{const e=i.subarray(n),t=i.subarray(0,o);s.set(e),s.set(t,e.length)}}return Atomics.store(this.state,C.READ_POS,r),r-s}clear(){const e=Atomics.load(this.state,C.WRITE_POS);Atomics.store(this.state,C.READ_POS,e)}size(){const e=Atomics.load(this.state,C.READ_POS);return Atomics.load(this.state,C.WRITE_POS)-e}}S=new WeakSet,F=function(e,t,i){for(let s=0;s<this.channels.length;s+=1){const r=this.channels[s],n=Math.min(s,e.numberOfChannels-1);if(t<i){const s=r.subarray(t,i);e.copyTo(s,{planeIndex:n,frameCount:i-t,format:"f32-planar"})}else{const s=r.subarray(t),o=r.subarray(0,i);e.copyTo(s,{planeIndex:n,frameCount:s.length,format:"f32-planar"}),o.length&&e.copyTo(o,{planeIndex:n,frameOffset:s.length,frameCount:o.length,format:"f32-planar"})}}};var R,N,P,z,B,W,x,L,j,G,V,U,Y,H,q=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))},K=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},X=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class J{constructor(e,t){R.add(this),N.set(this,void 0),P.set(this,void 0),z.set(this,void 0),B.set(this,void 0),W.set(this,void 0),x.set(this,void 0),L.set(this,void 0),j.set(this,void 0),G.set(this,void 0),V.set(this,void 0),K(this,V,void 0!==globalThis.SharedArrayBuffer,"f"),K(this,L,e,"f"),K(this,N,new AudioContext({latencyHint:"interactive",sampleRate:e.sample_rate}),"f"),K(this,P,this.load(e),"f"),K(this,x,t,"f"),X(this,V,"f")&&(K(this,W,new k(2,e.sample_rate),"f"),null!=X(this,W,"f")&&K(this,B,new D(X(this,W,"f")),"f")),K(this,G,new TransformStream({start:X(this,R,"m",U).bind(this),transform:X(this,R,"m",Y).bind(this)}),"f"),X(this,R,"m",H).call(this).catch((e=>console.error("failed to run audio renderer: ",e)))}load(e){return q(this,void 0,void 0,(function*(){const t=new Blob([`/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/render/ring_audio.ts":
/*!**********************************!*\
  !*** ./src/render/ring_audio.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PCMFrame: () => (/* binding */ PCMFrame),
/* harmony export */   Ring: () => (/* binding */ Ring),
/* harmony export */   RingShared: () => (/* binding */ RingShared)
/* harmony export */ });
// Ring buffer with audio samples.
var __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _Ring_instances, _Ring_copyToPlanar;
var STATE;
(function (STATE) {
    STATE[STATE["READ_POS"] = 0] = "READ_POS";
    STATE[STATE["WRITE_POS"] = 1] = "WRITE_POS";
    STATE[STATE["LENGTH"] = 2] = "LENGTH";
})(STATE || (STATE = {}));
class PCMFrame {
    constructor(numberOfFrames, numberOfChannels, buff) {
        this.numberOfFrames = numberOfFrames;
        this.numberOfChannels = numberOfChannels;
        this.format = "float";
        this.buff = buff;
    }
    copyTo(dst, options) {
        const frameCount = options.frameCount || this.numberOfFrames;
        const frameOffset = options.frameOffset || 0;
        const planeIndex = options.planeIndex;
        const startIndex = frameOffset + this.numberOfChannels * planeIndex;
        const endIndex = startIndex + frameCount;
        dst.set(this.buff.slice(startIndex, endIndex));
    }
}
// No prototype to make this easier to send via postMessage
class RingShared {
    constructor(channels, capacity) {
        // Store the current state in a separate ring buffer.
        this.state = new SharedArrayBuffer(STATE.LENGTH * Int32Array.BYTES_PER_ELEMENT);
        // Create a buffer for each audio channel
        this.channels = [];
        for (let i = 0; i < channels; i += 1) {
            const buffer = new SharedArrayBuffer(capacity * Float32Array.BYTES_PER_ELEMENT);
            this.channels.push(buffer);
        }
        this.capacity = capacity;
    }
}
class Ring {
    constructor(shared, channels, capacity) {
        _Ring_instances.add(this);
        this.startCache = 9600; //开始的时候先有这么多数据再播放,减少卡顿
        if (shared) {
            this.state = new Int32Array(shared.state);
            this.channels = [];
            for (const channel of shared.channels) {
                this.channels.push(new Float32Array(channel));
            }
            this.capacity = shared.capacity;
        }
        else {
            this.state = new Int32Array(STATE.LENGTH);
            this.channels = [];
            for (let i = 0; i < channels; i += 1) {
                const buffer = new Float32Array(capacity);
                this.channels.push(buffer);
            }
            this.capacity = capacity;
        }
        for (let i = 0; i < STATE.LENGTH; i += 1) {
            this.state[i] = 0;
        }
        this.start = true;
        this.startCache = 12000; //250ms, 开始的时候先有这么多数据再播放,减少卡顿
        //this.startCache = 480000; //250ms, 开始的时候先有这么多数据再播放,减少卡顿
    }
    write(frame) {
        const readPos = Atomics.load(this.state, STATE.READ_POS);
        const writePos = Atomics.load(this.state, STATE.WRITE_POS);
        //console.log("write " + Date.now() + '---' + readPos + " --- " + writePos)
        const available = this.capacity - (writePos - readPos);
        if (available <= 0)
            return 0;
        const toWrite = Math.min(frame.numberOfFrames, available);
        const startPos = writePos;
        const endPos = writePos + toWrite;
        const startIndex = startPos % this.capacity;
        const endIndex = endPos % this.capacity;
        __classPrivateFieldGet(this, _Ring_instances, "m", _Ring_copyToPlanar).call(this, frame, startIndex, endIndex);
        Atomics.store(this.state, STATE.WRITE_POS, endPos);
        return toWrite;
    }
    read(dst) {
        const readPos = Atomics.load(this.state, STATE.READ_POS);
        const writePos = Atomics.load(this.state, STATE.WRITE_POS);
        if (this.start) {
            //console.log("start " + Date.now() + '---' + (writePos - readPos))
            if (writePos - readPos < this.startCache) { //开始的时候先有这么多数据再播放,减少卡顿
                return 0;
            }
            else {
                this.start = false;
            }
        }
        const startPos = readPos;
        let endPos = startPos + dst[0].length;
        //console.log("read____  " + readPos + " --- " + writePos)
        if (endPos > writePos) {
            endPos = writePos;
            if (endPos <= startPos) {
                this.start = true;
                this.startCache = 2400; //50ms
                return 0;
            }
        }
        const startIndex = startPos % this.capacity;
        const endIndex = endPos % this.capacity;
        // Loop over each channel
        for (let i = 0; i < 1; i += 1) {
            if (i >= this.channels.length) {
                // ignore excess channels
            }
            const input = this.channels[i];
            const output = dst[i];
            if (startIndex < endIndex) {
                const full = input.subarray(startIndex, endIndex);
                output.set(full);
            }
            else {
                const first = input.subarray(startIndex);
                const second = input.subarray(0, endIndex);
                output.set(first);
                output.set(second, first.length);
            }
        }
        Atomics.store(this.state, STATE.READ_POS, endPos);
        return endPos - startPos;
    }
    clear() {
        const pos = Atomics.load(this.state, STATE.WRITE_POS);
        Atomics.store(this.state, STATE.READ_POS, pos);
    }
    size() {
        // TODO is this thread safe?
        const readPos = Atomics.load(this.state, STATE.READ_POS);
        const writePos = Atomics.load(this.state, STATE.WRITE_POS);
        return writePos - readPos;
    }
}
_Ring_instances = new WeakSet(), _Ring_copyToPlanar = function _Ring_copyToPlanar(frame, startIndex, endIndex) {
    // Loop over each channel
    for (let i = 0; i < this.channels.length; i += 1) {
        const channel = this.channels[i];
        // If the AudioData doesn't have enough channels, duplicate it.
        const planeIndex = Math.min(i, frame.numberOfChannels - 1);
        if (startIndex < endIndex) {
            const full = channel.subarray(startIndex, endIndex);
            frame.copyTo(full, {
                planeIndex,
                frameCount: endIndex - startIndex,
                format: "f32-planar"
            });
        }
        else {
            const first = channel.subarray(startIndex);
            const second = channel.subarray(0, endIndex);
            frame.copyTo(first, {
                planeIndex,
                frameCount: first.length,
                format: "f32-planar"
            });
            if (second.length) {
                frame.copyTo(second, {
                    planeIndex,
                    frameOffset: first.length,
                    frameCount: second.length,
                    format: "f32-planar"
                });
            }
        }
    }
};


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!********************************************************************************!*\
  !*** ./node_modules/ts-loader/index.js!./src/render/audio_render_workerlet.ts ***!
  \********************************************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ring_audio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ring_audio */ "./src/render/ring_audio.ts");

class AudioRenderWorklet extends AudioWorkletProcessor {
    constructor() {
        super();
        this.base = 0;
        this.port.onmessage = this.onMessage.bind(this);
    }
    onMessage(e) {
        if (!("config" in e.data)) {
            const chunk = e.data;
            const frame = new _ring_audio__WEBPACK_IMPORTED_MODULE_0__.PCMFrame(chunk.numberOfFrames, chunk.numberOfChannels, chunk.buff);
            this.ring.write(frame);
        }
        else {
            const msg = e.data;
            if (msg.config) {
                this.onConfig(msg.config);
            }
        }
    }
    onConfig(config) {
        this.ring = new _ring_audio__WEBPACK_IMPORTED_MODULE_0__.Ring(config.ring, config.channels, config.sampleRate); //1s
        //this.ring = new Ring(config.ring, config.channels, config.sampleRate * 100) //100s
    }
    // Inputs and outputs in groups of 128 samples.
    process(inputs, outputs, _parameters) {
        if (!this.ring) {
            // Paused
            return true;
        }
        if (inputs.length != 1 && outputs.length != 1) {
            throw new Error("only a single track is supported");
        }
        if (this.ring.size() == this.ring.capacity) {
            // 目前先清空队列，后面再优化
            console.warn("resyncing ring buffer");
            this.ring.clear();
            return true;
        }
        const output = outputs[0];
        const size = this.ring.read(output);
        //console.log(" " + output.length + " " + size)
        if (size < output.length) {
            //TODO trigger rebuffering event
        }
        return true;
    }
}
try {
    registerProcessor("audio_renderer", AudioRenderWorklet);
}
catch (error) {
    console.error("failed to register audio renderer: ", error);
}

})();

/******/ })()
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiTERTREsuYXVkaW9fd29ya2VyLndvcmtlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQSw4QkFBOEIsU0FBSSxJQUFJLFNBQUk7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNCQUFzQjtBQUNoQjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGNBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsY0FBYztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGtCQUFrQjtBQUMxQztBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixPQUFPO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsMEJBQTBCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTs7Ozs7OztVQ2hMQTtVQUNBOztVQUVBO1VBQ0E7VUFDQTtVQUNBO1VBQ0E7VUFDQTtVQUNBO1VBQ0E7VUFDQTtVQUNBO1VBQ0E7VUFDQTtVQUNBOztVQUVBO1VBQ0E7O1VBRUE7VUFDQTtVQUNBOzs7OztXQ3RCQTtXQUNBO1dBQ0E7V0FDQTtXQUNBLHlDQUF5Qyx3Q0FBd0M7V0FDakY7V0FDQTtXQUNBOzs7OztXQ1BBOzs7OztXQ0FBO1dBQ0E7V0FDQTtXQUNBLHVEQUF1RCxpQkFBaUI7V0FDeEU7V0FDQSxnREFBZ0QsYUFBYTtXQUM3RDs7Ozs7Ozs7Ozs7O0FDTjhDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixpREFBUTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw2Q0FBSSxtREFBbUQ7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL0xEU0RLLmF1ZGlvX3dvcmtlci8uL3NyYy9yZW5kZXIvcmluZ19hdWRpby50cyIsIndlYnBhY2s6Ly9MRFNESy5hdWRpb193b3JrZXIvd2VicGFjay9ib290c3RyYXAiLCJ3ZWJwYWNrOi8vTERTREsuYXVkaW9fd29ya2VyL3dlYnBhY2svcnVudGltZS9kZWZpbmUgcHJvcGVydHkgZ2V0dGVycyIsIndlYnBhY2s6Ly9MRFNESy5hdWRpb193b3JrZXIvd2VicGFjay9ydW50aW1lL2hhc093blByb3BlcnR5IHNob3J0aGFuZCIsIndlYnBhY2s6Ly9MRFNESy5hdWRpb193b3JrZXIvd2VicGFjay9ydW50aW1lL21ha2UgbmFtZXNwYWNlIG9iamVjdCIsIndlYnBhY2s6Ly9MRFNESy5hdWRpb193b3JrZXIvLi9zcmMvcmVuZGVyL2F1ZGlvX3JlbmRlcl93b3JrZXJsZXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUmluZyBidWZmZXIgd2l0aCBhdWRpbyBzYW1wbGVzLlxudmFyIF9fY2xhc3NQcml2YXRlRmllbGRHZXQgPSAodGhpcyAmJiB0aGlzLl9fY2xhc3NQcml2YXRlRmllbGRHZXQpIHx8IGZ1bmN0aW9uIChyZWNlaXZlciwgc3RhdGUsIGtpbmQsIGYpIHtcbiAgICBpZiAoa2luZCA9PT0gXCJhXCIgJiYgIWYpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJQcml2YXRlIGFjY2Vzc29yIHdhcyBkZWZpbmVkIHdpdGhvdXQgYSBnZXR0ZXJcIik7XG4gICAgaWYgKHR5cGVvZiBzdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gcmVjZWl2ZXIgIT09IHN0YXRlIHx8ICFmIDogIXN0YXRlLmhhcyhyZWNlaXZlcikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgcmVhZCBwcml2YXRlIG1lbWJlciBmcm9tIGFuIG9iamVjdCB3aG9zZSBjbGFzcyBkaWQgbm90IGRlY2xhcmUgaXRcIik7XG4gICAgcmV0dXJuIGtpbmQgPT09IFwibVwiID8gZiA6IGtpbmQgPT09IFwiYVwiID8gZi5jYWxsKHJlY2VpdmVyKSA6IGYgPyBmLnZhbHVlIDogc3RhdGUuZ2V0KHJlY2VpdmVyKTtcbn07XG52YXIgX1JpbmdfaW5zdGFuY2VzLCBfUmluZ19jb3B5VG9QbGFuYXI7XG52YXIgU1RBVEU7XG4oZnVuY3Rpb24gKFNUQVRFKSB7XG4gICAgU1RBVEVbU1RBVEVbXCJSRUFEX1BPU1wiXSA9IDBdID0gXCJSRUFEX1BPU1wiO1xuICAgIFNUQVRFW1NUQVRFW1wiV1JJVEVfUE9TXCJdID0gMV0gPSBcIldSSVRFX1BPU1wiO1xuICAgIFNUQVRFW1NUQVRFW1wiTEVOR1RIXCJdID0gMl0gPSBcIkxFTkdUSFwiO1xufSkoU1RBVEUgfHwgKFNUQVRFID0ge30pKTtcbmV4cG9ydCBjbGFzcyBQQ01GcmFtZSB7XG4gICAgY29uc3RydWN0b3IobnVtYmVyT2ZGcmFtZXMsIG51bWJlck9mQ2hhbm5lbHMsIGJ1ZmYpIHtcbiAgICAgICAgdGhpcy5udW1iZXJPZkZyYW1lcyA9IG51bWJlck9mRnJhbWVzO1xuICAgICAgICB0aGlzLm51bWJlck9mQ2hhbm5lbHMgPSBudW1iZXJPZkNoYW5uZWxzO1xuICAgICAgICB0aGlzLmZvcm1hdCA9IFwiZmxvYXRcIjtcbiAgICAgICAgdGhpcy5idWZmID0gYnVmZjtcbiAgICB9XG4gICAgY29weVRvKGRzdCwgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBmcmFtZUNvdW50ID0gb3B0aW9ucy5mcmFtZUNvdW50IHx8IHRoaXMubnVtYmVyT2ZGcmFtZXM7XG4gICAgICAgIGNvbnN0IGZyYW1lT2Zmc2V0ID0gb3B0aW9ucy5mcmFtZU9mZnNldCB8fCAwO1xuICAgICAgICBjb25zdCBwbGFuZUluZGV4ID0gb3B0aW9ucy5wbGFuZUluZGV4O1xuICAgICAgICBjb25zdCBzdGFydEluZGV4ID0gZnJhbWVPZmZzZXQgKyB0aGlzLm51bWJlck9mQ2hhbm5lbHMgKiBwbGFuZUluZGV4O1xuICAgICAgICBjb25zdCBlbmRJbmRleCA9IHN0YXJ0SW5kZXggKyBmcmFtZUNvdW50O1xuICAgICAgICBkc3Quc2V0KHRoaXMuYnVmZi5zbGljZShzdGFydEluZGV4LCBlbmRJbmRleCkpO1xuICAgIH1cbn1cbi8vIE5vIHByb3RvdHlwZSB0byBtYWtlIHRoaXMgZWFzaWVyIHRvIHNlbmQgdmlhIHBvc3RNZXNzYWdlXG5leHBvcnQgY2xhc3MgUmluZ1NoYXJlZCB7XG4gICAgY29uc3RydWN0b3IoY2hhbm5lbHMsIGNhcGFjaXR5KSB7XG4gICAgICAgIC8vIFN0b3JlIHRoZSBjdXJyZW50IHN0YXRlIGluIGEgc2VwYXJhdGUgcmluZyBidWZmZXIuXG4gICAgICAgIHRoaXMuc3RhdGUgPSBuZXcgU2hhcmVkQXJyYXlCdWZmZXIoU1RBVEUuTEVOR1RIICogSW50MzJBcnJheS5CWVRFU19QRVJfRUxFTUVOVCk7XG4gICAgICAgIC8vIENyZWF0ZSBhIGJ1ZmZlciBmb3IgZWFjaCBhdWRpbyBjaGFubmVsXG4gICAgICAgIHRoaXMuY2hhbm5lbHMgPSBbXTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjaGFubmVsczsgaSArPSAxKSB7XG4gICAgICAgICAgICBjb25zdCBidWZmZXIgPSBuZXcgU2hhcmVkQXJyYXlCdWZmZXIoY2FwYWNpdHkgKiBGbG9hdDMyQXJyYXkuQllURVNfUEVSX0VMRU1FTlQpO1xuICAgICAgICAgICAgdGhpcy5jaGFubmVscy5wdXNoKGJ1ZmZlcik7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jYXBhY2l0eSA9IGNhcGFjaXR5O1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBSaW5nIHtcbiAgICBjb25zdHJ1Y3RvcihzaGFyZWQsIGNoYW5uZWxzLCBjYXBhY2l0eSkge1xuICAgICAgICBfUmluZ19pbnN0YW5jZXMuYWRkKHRoaXMpO1xuICAgICAgICB0aGlzLnN0YXJ0Q2FjaGUgPSA5NjAwOyAvL+W8gOWni+eahOaXtuWAmeWFiOaciei/meS5iOWkmuaVsOaNruWGjeaSreaUvizlh4/lsJHljaHpob9cbiAgICAgICAgaWYgKHNoYXJlZCkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZSA9IG5ldyBJbnQzMkFycmF5KHNoYXJlZC5zdGF0ZSk7XG4gICAgICAgICAgICB0aGlzLmNoYW5uZWxzID0gW107XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGNoYW5uZWwgb2Ygc2hhcmVkLmNoYW5uZWxzKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5jaGFubmVscy5wdXNoKG5ldyBGbG9hdDMyQXJyYXkoY2hhbm5lbCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5jYXBhY2l0eSA9IHNoYXJlZC5jYXBhY2l0eTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuc3RhdGUgPSBuZXcgSW50MzJBcnJheShTVEFURS5MRU5HVEgpO1xuICAgICAgICAgICAgdGhpcy5jaGFubmVscyA9IFtdO1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjaGFubmVsczsgaSArPSAxKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYnVmZmVyID0gbmV3IEZsb2F0MzJBcnJheShjYXBhY2l0eSk7XG4gICAgICAgICAgICAgICAgdGhpcy5jaGFubmVscy5wdXNoKGJ1ZmZlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmNhcGFjaXR5ID0gY2FwYWNpdHk7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBTVEFURS5MRU5HVEg7IGkgKz0gMSkge1xuICAgICAgICAgICAgdGhpcy5zdGF0ZVtpXSA9IDA7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdGFydCA9IHRydWU7XG4gICAgICAgIHRoaXMuc3RhcnRDYWNoZSA9IDEyMDAwOyAvLzI1MG1zLCDlvIDlp4vnmoTml7blgJnlhYjmnInov5nkuYjlpJrmlbDmja7lho3mkq3mlL4s5YeP5bCR5Y2h6aG/XG4gICAgICAgIC8vdGhpcy5zdGFydENhY2hlID0gNDgwMDAwOyAvLzI1MG1zLCDlvIDlp4vnmoTml7blgJnlhYjmnInov5nkuYjlpJrmlbDmja7lho3mkq3mlL4s5YeP5bCR5Y2h6aG/XG4gICAgfVxuICAgIHdyaXRlKGZyYW1lKSB7XG4gICAgICAgIGNvbnN0IHJlYWRQb3MgPSBBdG9taWNzLmxvYWQodGhpcy5zdGF0ZSwgU1RBVEUuUkVBRF9QT1MpO1xuICAgICAgICBjb25zdCB3cml0ZVBvcyA9IEF0b21pY3MubG9hZCh0aGlzLnN0YXRlLCBTVEFURS5XUklURV9QT1MpO1xuICAgICAgICAvL2NvbnNvbGUubG9nKFwid3JpdGUgXCIgKyBEYXRlLm5vdygpICsgJy0tLScgKyByZWFkUG9zICsgXCIgLS0tIFwiICsgd3JpdGVQb3MpXG4gICAgICAgIGNvbnN0IGF2YWlsYWJsZSA9IHRoaXMuY2FwYWNpdHkgLSAod3JpdGVQb3MgLSByZWFkUG9zKTtcbiAgICAgICAgaWYgKGF2YWlsYWJsZSA8PSAwKVxuICAgICAgICAgICAgcmV0dXJuIDA7XG4gICAgICAgIGNvbnN0IHRvV3JpdGUgPSBNYXRoLm1pbihmcmFtZS5udW1iZXJPZkZyYW1lcywgYXZhaWxhYmxlKTtcbiAgICAgICAgY29uc3Qgc3RhcnRQb3MgPSB3cml0ZVBvcztcbiAgICAgICAgY29uc3QgZW5kUG9zID0gd3JpdGVQb3MgKyB0b1dyaXRlO1xuICAgICAgICBjb25zdCBzdGFydEluZGV4ID0gc3RhcnRQb3MgJSB0aGlzLmNhcGFjaXR5O1xuICAgICAgICBjb25zdCBlbmRJbmRleCA9IGVuZFBvcyAlIHRoaXMuY2FwYWNpdHk7XG4gICAgICAgIF9fY2xhc3NQcml2YXRlRmllbGRHZXQodGhpcywgX1JpbmdfaW5zdGFuY2VzLCBcIm1cIiwgX1JpbmdfY29weVRvUGxhbmFyKS5jYWxsKHRoaXMsIGZyYW1lLCBzdGFydEluZGV4LCBlbmRJbmRleCk7XG4gICAgICAgIEF0b21pY3Muc3RvcmUodGhpcy5zdGF0ZSwgU1RBVEUuV1JJVEVfUE9TLCBlbmRQb3MpO1xuICAgICAgICByZXR1cm4gdG9Xcml0ZTtcbiAgICB9XG4gICAgcmVhZChkc3QpIHtcbiAgICAgICAgY29uc3QgcmVhZFBvcyA9IEF0b21pY3MubG9hZCh0aGlzLnN0YXRlLCBTVEFURS5SRUFEX1BPUyk7XG4gICAgICAgIGNvbnN0IHdyaXRlUG9zID0gQXRvbWljcy5sb2FkKHRoaXMuc3RhdGUsIFNUQVRFLldSSVRFX1BPUyk7XG4gICAgICAgIGlmICh0aGlzLnN0YXJ0KSB7XG4gICAgICAgICAgICAvL2NvbnNvbGUubG9nKFwic3RhcnQgXCIgKyBEYXRlLm5vdygpICsgJy0tLScgKyAod3JpdGVQb3MgLSByZWFkUG9zKSlcbiAgICAgICAgICAgIGlmICh3cml0ZVBvcyAtIHJlYWRQb3MgPCB0aGlzLnN0YXJ0Q2FjaGUpIHsgLy/lvIDlp4vnmoTml7blgJnlhYjmnInov5nkuYjlpJrmlbDmja7lho3mkq3mlL4s5YeP5bCR5Y2h6aG/XG4gICAgICAgICAgICAgICAgcmV0dXJuIDA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aGlzLnN0YXJ0ID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY29uc3Qgc3RhcnRQb3MgPSByZWFkUG9zO1xuICAgICAgICBsZXQgZW5kUG9zID0gc3RhcnRQb3MgKyBkc3RbMF0ubGVuZ3RoO1xuICAgICAgICAvL2NvbnNvbGUubG9nKFwicmVhZF9fX18gIFwiICsgcmVhZFBvcyArIFwiIC0tLSBcIiArIHdyaXRlUG9zKVxuICAgICAgICBpZiAoZW5kUG9zID4gd3JpdGVQb3MpIHtcbiAgICAgICAgICAgIGVuZFBvcyA9IHdyaXRlUG9zO1xuICAgICAgICAgICAgaWYgKGVuZFBvcyA8PSBzdGFydFBvcykge1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhcnQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHRoaXMuc3RhcnRDYWNoZSA9IDI0MDA7IC8vNTBtc1xuICAgICAgICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHN0YXJ0SW5kZXggPSBzdGFydFBvcyAlIHRoaXMuY2FwYWNpdHk7XG4gICAgICAgIGNvbnN0IGVuZEluZGV4ID0gZW5kUG9zICUgdGhpcy5jYXBhY2l0eTtcbiAgICAgICAgLy8gTG9vcCBvdmVyIGVhY2ggY2hhbm5lbFxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDE7IGkgKz0gMSkge1xuICAgICAgICAgICAgaWYgKGkgPj0gdGhpcy5jaGFubmVscy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAvLyBpZ25vcmUgZXhjZXNzIGNoYW5uZWxzXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBpbnB1dCA9IHRoaXMuY2hhbm5lbHNbaV07XG4gICAgICAgICAgICBjb25zdCBvdXRwdXQgPSBkc3RbaV07XG4gICAgICAgICAgICBpZiAoc3RhcnRJbmRleCA8IGVuZEluZGV4KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZnVsbCA9IGlucHV0LnN1YmFycmF5KHN0YXJ0SW5kZXgsIGVuZEluZGV4KTtcbiAgICAgICAgICAgICAgICBvdXRwdXQuc2V0KGZ1bGwpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmlyc3QgPSBpbnB1dC5zdWJhcnJheShzdGFydEluZGV4KTtcbiAgICAgICAgICAgICAgICBjb25zdCBzZWNvbmQgPSBpbnB1dC5zdWJhcnJheSgwLCBlbmRJbmRleCk7XG4gICAgICAgICAgICAgICAgb3V0cHV0LnNldChmaXJzdCk7XG4gICAgICAgICAgICAgICAgb3V0cHV0LnNldChzZWNvbmQsIGZpcnN0Lmxlbmd0aCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgQXRvbWljcy5zdG9yZSh0aGlzLnN0YXRlLCBTVEFURS5SRUFEX1BPUywgZW5kUG9zKTtcbiAgICAgICAgcmV0dXJuIGVuZFBvcyAtIHN0YXJ0UG9zO1xuICAgIH1cbiAgICBjbGVhcigpIHtcbiAgICAgICAgY29uc3QgcG9zID0gQXRvbWljcy5sb2FkKHRoaXMuc3RhdGUsIFNUQVRFLldSSVRFX1BPUyk7XG4gICAgICAgIEF0b21pY3Muc3RvcmUodGhpcy5zdGF0ZSwgU1RBVEUuUkVBRF9QT1MsIHBvcyk7XG4gICAgfVxuICAgIHNpemUoKSB7XG4gICAgICAgIC8vIFRPRE8gaXMgdGhpcyB0aHJlYWQgc2FmZT9cbiAgICAgICAgY29uc3QgcmVhZFBvcyA9IEF0b21pY3MubG9hZCh0aGlzLnN0YXRlLCBTVEFURS5SRUFEX1BPUyk7XG4gICAgICAgIGNvbnN0IHdyaXRlUG9zID0gQXRvbWljcy5sb2FkKHRoaXMuc3RhdGUsIFNUQVRFLldSSVRFX1BPUyk7XG4gICAgICAgIHJldHVybiB3cml0ZVBvcyAtIHJlYWRQb3M7XG4gICAgfVxufVxuX1JpbmdfaW5zdGFuY2VzID0gbmV3IFdlYWtTZXQoKSwgX1JpbmdfY29weVRvUGxhbmFyID0gZnVuY3Rpb24gX1JpbmdfY29weVRvUGxhbmFyKGZyYW1lLCBzdGFydEluZGV4LCBlbmRJbmRleCkge1xuICAgIC8vIExvb3Agb3ZlciBlYWNoIGNoYW5uZWxcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuY2hhbm5lbHMubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgY29uc3QgY2hhbm5lbCA9IHRoaXMuY2hhbm5lbHNbaV07XG4gICAgICAgIC8vIElmIHRoZSBBdWRpb0RhdGEgZG9lc24ndCBoYXZlIGVub3VnaCBjaGFubmVscywgZHVwbGljYXRlIGl0LlxuICAgICAgICBjb25zdCBwbGFuZUluZGV4ID0gTWF0aC5taW4oaSwgZnJhbWUubnVtYmVyT2ZDaGFubmVscyAtIDEpO1xuICAgICAgICBpZiAoc3RhcnRJbmRleCA8IGVuZEluZGV4KSB7XG4gICAgICAgICAgICBjb25zdCBmdWxsID0gY2hhbm5lbC5zdWJhcnJheShzdGFydEluZGV4LCBlbmRJbmRleCk7XG4gICAgICAgICAgICBmcmFtZS5jb3B5VG8oZnVsbCwge1xuICAgICAgICAgICAgICAgIHBsYW5lSW5kZXgsXG4gICAgICAgICAgICAgICAgZnJhbWVDb3VudDogZW5kSW5kZXggLSBzdGFydEluZGV4LFxuICAgICAgICAgICAgICAgIGZvcm1hdDogXCJmMzItcGxhbmFyXCJcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgZmlyc3QgPSBjaGFubmVsLnN1YmFycmF5KHN0YXJ0SW5kZXgpO1xuICAgICAgICAgICAgY29uc3Qgc2Vjb25kID0gY2hhbm5lbC5zdWJhcnJheSgwLCBlbmRJbmRleCk7XG4gICAgICAgICAgICBmcmFtZS5jb3B5VG8oZmlyc3QsIHtcbiAgICAgICAgICAgICAgICBwbGFuZUluZGV4LFxuICAgICAgICAgICAgICAgIGZyYW1lQ291bnQ6IGZpcnN0Lmxlbmd0aCxcbiAgICAgICAgICAgICAgICBmb3JtYXQ6IFwiZjMyLXBsYW5hclwiXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChzZWNvbmQubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgZnJhbWUuY29weVRvKHNlY29uZCwge1xuICAgICAgICAgICAgICAgICAgICBwbGFuZUluZGV4LFxuICAgICAgICAgICAgICAgICAgICBmcmFtZU9mZnNldDogZmlyc3QubGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgICBmcmFtZUNvdW50OiBzZWNvbmQubGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgICBmb3JtYXQ6IFwiZjMyLXBsYW5hclwiXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59O1xuIiwiLy8gVGhlIG1vZHVsZSBjYWNoZVxudmFyIF9fd2VicGFja19tb2R1bGVfY2FjaGVfXyA9IHt9O1xuXG4vLyBUaGUgcmVxdWlyZSBmdW5jdGlvblxuZnVuY3Rpb24gX193ZWJwYWNrX3JlcXVpcmVfXyhtb2R1bGVJZCkge1xuXHQvLyBDaGVjayBpZiBtb2R1bGUgaXMgaW4gY2FjaGVcblx0dmFyIGNhY2hlZE1vZHVsZSA9IF9fd2VicGFja19tb2R1bGVfY2FjaGVfX1ttb2R1bGVJZF07XG5cdGlmIChjYWNoZWRNb2R1bGUgIT09IHVuZGVmaW5lZCkge1xuXHRcdHJldHVybiBjYWNoZWRNb2R1bGUuZXhwb3J0cztcblx0fVxuXHQvLyBDcmVhdGUgYSBuZXcgbW9kdWxlIChhbmQgcHV0IGl0IGludG8gdGhlIGNhY2hlKVxuXHR2YXIgbW9kdWxlID0gX193ZWJwYWNrX21vZHVsZV9jYWNoZV9fW21vZHVsZUlkXSA9IHtcblx0XHQvLyBubyBtb2R1bGUuaWQgbmVlZGVkXG5cdFx0Ly8gbm8gbW9kdWxlLmxvYWRlZCBuZWVkZWRcblx0XHRleHBvcnRzOiB7fVxuXHR9O1xuXG5cdC8vIEV4ZWN1dGUgdGhlIG1vZHVsZSBmdW5jdGlvblxuXHRfX3dlYnBhY2tfbW9kdWxlc19fW21vZHVsZUlkXShtb2R1bGUsIG1vZHVsZS5leHBvcnRzLCBfX3dlYnBhY2tfcmVxdWlyZV9fKTtcblxuXHQvLyBSZXR1cm4gdGhlIGV4cG9ydHMgb2YgdGhlIG1vZHVsZVxuXHRyZXR1cm4gbW9kdWxlLmV4cG9ydHM7XG59XG5cbiIsIi8vIGRlZmluZSBnZXR0ZXIgZnVuY3Rpb25zIGZvciBoYXJtb255IGV4cG9ydHNcbl9fd2VicGFja19yZXF1aXJlX18uZCA9IChleHBvcnRzLCBkZWZpbml0aW9uKSA9PiB7XG5cdGZvcih2YXIga2V5IGluIGRlZmluaXRpb24pIHtcblx0XHRpZihfX3dlYnBhY2tfcmVxdWlyZV9fLm8oZGVmaW5pdGlvbiwga2V5KSAmJiAhX193ZWJwYWNrX3JlcXVpcmVfXy5vKGV4cG9ydHMsIGtleSkpIHtcblx0XHRcdE9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBrZXksIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBkZWZpbml0aW9uW2tleV0gfSk7XG5cdFx0fVxuXHR9XG59OyIsIl9fd2VicGFja19yZXF1aXJlX18ubyA9IChvYmosIHByb3ApID0+IChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqLCBwcm9wKSkiLCIvLyBkZWZpbmUgX19lc01vZHVsZSBvbiBleHBvcnRzXG5fX3dlYnBhY2tfcmVxdWlyZV9fLnIgPSAoZXhwb3J0cykgPT4ge1xuXHRpZih0eXBlb2YgU3ltYm9sICE9PSAndW5kZWZpbmVkJyAmJiBTeW1ib2wudG9TdHJpbmdUYWcpIHtcblx0XHRPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgU3ltYm9sLnRvU3RyaW5nVGFnLCB7IHZhbHVlOiAnTW9kdWxlJyB9KTtcblx0fVxuXHRPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xufTsiLCJpbXBvcnQgeyBSaW5nLCBQQ01GcmFtZSB9IGZyb20gJy4vcmluZ19hdWRpbyc7XG5jbGFzcyBBdWRpb1JlbmRlcldvcmtsZXQgZXh0ZW5kcyBBdWRpb1dvcmtsZXRQcm9jZXNzb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlcigpO1xuICAgICAgICB0aGlzLmJhc2UgPSAwO1xuICAgICAgICB0aGlzLnBvcnQub25tZXNzYWdlID0gdGhpcy5vbk1lc3NhZ2UuYmluZCh0aGlzKTtcbiAgICB9XG4gICAgb25NZXNzYWdlKGUpIHtcbiAgICAgICAgaWYgKCEoXCJjb25maWdcIiBpbiBlLmRhdGEpKSB7XG4gICAgICAgICAgICBjb25zdCBjaHVuayA9IGUuZGF0YTtcbiAgICAgICAgICAgIGNvbnN0IGZyYW1lID0gbmV3IFBDTUZyYW1lKGNodW5rLm51bWJlck9mRnJhbWVzLCBjaHVuay5udW1iZXJPZkNoYW5uZWxzLCBjaHVuay5idWZmKTtcbiAgICAgICAgICAgIHRoaXMucmluZy53cml0ZShmcmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCBtc2cgPSBlLmRhdGE7XG4gICAgICAgICAgICBpZiAobXNnLmNvbmZpZykge1xuICAgICAgICAgICAgICAgIHRoaXMub25Db25maWcobXNnLmNvbmZpZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgb25Db25maWcoY29uZmlnKSB7XG4gICAgICAgIHRoaXMucmluZyA9IG5ldyBSaW5nKGNvbmZpZy5yaW5nLCBjb25maWcuY2hhbm5lbHMsIGNvbmZpZy5zYW1wbGVSYXRlKTsgLy8xc1xuICAgICAgICAvL3RoaXMucmluZyA9IG5ldyBSaW5nKGNvbmZpZy5yaW5nLCBjb25maWcuY2hhbm5lbHMsIGNvbmZpZy5zYW1wbGVSYXRlICogMTAwKSAvLzEwMHNcbiAgICB9XG4gICAgLy8gSW5wdXRzIGFuZCBvdXRwdXRzIGluIGdyb3VwcyBvZiAxMjggc2FtcGxlcy5cbiAgICBwcm9jZXNzKGlucHV0cywgb3V0cHV0cywgX3BhcmFtZXRlcnMpIHtcbiAgICAgICAgaWYgKCF0aGlzLnJpbmcpIHtcbiAgICAgICAgICAgIC8vIFBhdXNlZFxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGlucHV0cy5sZW5ndGggIT0gMSAmJiBvdXRwdXRzLmxlbmd0aCAhPSAxKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJvbmx5IGEgc2luZ2xlIHRyYWNrIGlzIHN1cHBvcnRlZFwiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5yaW5nLnNpemUoKSA9PSB0aGlzLnJpbmcuY2FwYWNpdHkpIHtcbiAgICAgICAgICAgIC8vIOebruWJjeWFiOa4heepuumYn+WIl++8jOWQjumdouWGjeS8mOWMllxuICAgICAgICAgICAgY29uc29sZS53YXJuKFwicmVzeW5jaW5nIHJpbmcgYnVmZmVyXCIpO1xuICAgICAgICAgICAgdGhpcy5yaW5nLmNsZWFyKCk7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBvdXRwdXQgPSBvdXRwdXRzWzBdO1xuICAgICAgICBjb25zdCBzaXplID0gdGhpcy5yaW5nLnJlYWQob3V0cHV0KTtcbiAgICAgICAgLy9jb25zb2xlLmxvZyhcIiBcIiArIG91dHB1dC5sZW5ndGggKyBcIiBcIiArIHNpemUpXG4gICAgICAgIGlmIChzaXplIDwgb3V0cHV0Lmxlbmd0aCkge1xuICAgICAgICAgICAgLy9UT0RPIHRyaWdnZXIgcmVidWZmZXJpbmcgZXZlbnRcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG59XG50cnkge1xuICAgIHJlZ2lzdGVyUHJvY2Vzc29yKFwiYXVkaW9fcmVuZGVyZXJcIiwgQXVkaW9SZW5kZXJXb3JrbGV0KTtcbn1cbmNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJmYWlsZWQgdG8gcmVnaXN0ZXIgYXVkaW8gcmVuZGVyZXI6IFwiLCBlcnJvcik7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=`],{type:"text/javascript"}),i=URL.createObjectURL(t);yield X(this,N,"f").audioWorklet.addModule(i),X(this,N,"f").createGain().gain.value=2;const s=new AudioWorkletNode(X(this,N,"f"),"audio_renderer");s.port.addEventListener("message",this.on.bind(this)),s.onprocessorerror=e=>{console.error("Audio worklet error:",e)},s.connect(X(this,N,"f").destination);const r={sampleRate:e.sample_rate,channels:e.channel_count,ring:X(this,W,"f")};return s.port.postMessage({config:r}),K(this,z,s.port,"f"),s}))}on(e){}play(){X(this,N,"f").resume().catch((e=>console.warn("failed to resume audio context: ",e)))}close(){X(this,N,"f").close().catch((e=>console.warn("failed to close audio context: ",e)))}}N=new WeakMap,P=new WeakMap,z=new WeakMap,B=new WeakMap,W=new WeakMap,x=new WeakMap,L=new WeakMap,j=new WeakMap,G=new WeakMap,V=new WeakMap,R=new WeakSet,U=function(e){K(this,j,new AudioDecoder({output:t=>{e.enqueue(t)},error:console.warn}),"f"),X(this,j,"f").configure({codec:X(this,L,"f").codec,sampleRate:X(this,L,"f").sample_rate,numberOfChannels:X(this,L,"f").channel_count})},Y=function(e){const t=new EncodedAudioChunk({type:"key",timestamp:1e3*e.timestamp,data:e.data,duration:10});try{X(this,j,"f").decode(t)}catch(e){console.log(e)}},H=function(){return q(this,void 0,void 0,(function*(){const e=X(this,x,"f").frames.pipeThrough(X(this,G,"f")).getReader();for(;;){const{value:i,done:s}=yield e.read();if(s)break;if(null==X(this,B,"f")){var t=new Float32Array(i.numberOfFrames*i.numberOfChannels);i.copyTo(t,{planeIndex:0,format:"f32-planar"}),X(this,z,"f")&&X(this,z,"f").postMessage({numberOfFrames:i.numberOfFrames,numberOfChannels:i.numberOfChannels,buff:t},[t.buffer]);continue}const r=X(this,B,"f").write(i);r<i.numberOfFrames&&console.warn(`droppped ${i.numberOfFrames-r} audio samples`)}}))};class Q{constructor(e){{this.type=e.type,this.timestamp=e.timestamp,"number"==typeof e.duration?this.duration=e.duration:this.duration=null,this.byteLength=e.data.byteLength;let t=!1;if(e.transfer){let i,s;i=e.data.buffer?e.data.buffer:e.data,s=e.transfer instanceof Array?e.transfer:Array.from(e.transfer);for(const e of s)if(e===i){t=!0;break}}const i=new Uint8Array(e.data.buffer||e.data,e.data.byteOffset||0,e.data.BYTES_PER_ELEMENT?e.data.BYTES_PER_ELEMENT*e.data.length:e.data.byteLength);this._data=t?i:i.slice(0)}}_libavGetData(){return this._data}copyTo(e){new Uint8Array(e.buffer||e,e.byteOffset||0).set(this._data)}}const Z=Q;!function(e){function t(){var t=this||self;t.globalThis=t,delete e.prototype._T_}"object"!=typeof globalThis&&(this?t():(e.defineProperty(e.prototype,"_T_",{configurable:!0,get:t}),_T_))}(Object);class ${constructor(e){$._checkValidAudioDataInit(e);{this.format=e.format,this.sampleRate=e.sampleRate,this.numberOfFrames=e.numberOfFrames,this.numberOfChannels=e.numberOfChannels,this.timestamp=e.timestamp;let t=!1;if(e.transfer){let i,s;i=e.data.buffer?e.data.buffer:e.data,s=e.transfer instanceof Array?e.transfer:Array.from(e.transfer);for(const e of s)if(e===i){t=!0;break}}let i,s=0;t?(i=e.data,s=e.data.byteOffset||0):i=e.data.slice(0);const r=ee(e.format,i.buffer||i,s);this._data=r}this.duration=e.numberOfFrames/e.sampleRate*1e6}toNative(e={}){const t=new globalThis.AudioData({data:this._data,format:this.format,sampleRate:this.sampleRate,numberOfFrames:this.numberOfFrames,numberOfChannels:this.numberOfChannels,timestamp:this.timestamp,transfer:e.transfer?[this._data.buffer]:[]});return e.transfer&&this.close(),t}static fromNative(e){const t=e,i=ie(t.format)?1:t.numberOfChannels,s=t.allocationSize({format:t.format,planeIndex:0}),r=new Uint8Array(s);for(let e=0;e<i;e++)t.copyTo(r.subarray(e*s),{format:t.format,planeIndex:e});return new $({data:r,format:t.format,sampleRate:t.sampleRate,numberOfFrames:t.numberOfFrames,numberOfChannels:t.numberOfChannels,timestamp:t.timestamp,transfer:[r.buffer]})}_libavGetData(){return this._data}static _checkValidAudioDataInit(e){if(e.sampleRate<=0)throw new TypeError(`Invalid sample rate ${e.sampleRate}`);if(e.numberOfFrames<=0)throw new TypeError(`Invalid number of frames ${e.numberOfFrames}`);if(e.numberOfChannels<=0)throw new TypeError(`Invalid number of channels ${e.numberOfChannels}`);{const t=e.numberOfFrames*e.numberOfChannels,i=te(e.format)*t;if(e.data.byteLength<i)throw new TypeError(`This audio data must be at least ${i} bytes`)}}allocationSize(e){if(null===this._data)throw new DOMException("Detached","InvalidStateError");const t=this._computeCopyElementCount(e);let i=this.format;return e.format&&(i=e.format),te(i)*t}_computeCopyElementCount(e){let t=this.format;e.format&&(t=e.format);const i=ie(t);if(i){if(e.planeIndex>0)throw new RangeError("Invalid plane")}else if(e.planeIndex>=this.numberOfChannels)throw new RangeError("Invalid plane");if(this.format!==t&&"f32-planar"!==t)throw new DOMException("Only conversion to f32-planar is supported","NotSupportedError");const s=this.numberOfFrames,r=e.frameOffset||0;if(r>=s)throw new RangeError("Frame offset out of range");let n=s-r;if("number"==typeof e.frameCount){if(e.frameCount>n)throw new RangeError("Frame count out of range");n=e.frameCount}let o=n;return i&&(o*=this.numberOfChannels),o}copyTo(e,t){if(null===this._data)throw new DOMException("Detached","InvalidStateError");const i=this._computeCopyElementCount(t);let s=this.format;if(t.format&&(s=t.format),te(s)*i>e.byteLength)throw new RangeError("Buffer too small");const r=this._data.subarray(t.planeIndex*this.numberOfFrames),n=t.frameOffset||0,o=this.numberOfChannels;if(this.format===s){const t=ee(s,e.buffer||e,e.byteOffset||0);ie(s)?t.set(r.subarray(n*o,n*o+i)):t.set(r.subarray(n,n+i))}else{const a=ee(s,e.buffer||e,e.byteOffset||0);let l=0,c=1;switch(this.format){case"u8":case"u8-planar":l=128,c=128;break;case"s16":case"s16-planar":c=32768;break;case"s32":case"s32-planar":c=2147483648}if(ie(this.format))for(let e=t.planeIndex+n*o,s=0;s<i;e+=o,s++)a[s]=(r[e]-l)/c;else for(let e=n,t=0;t<i;e++,t++)a[t]=(r[e]-l)/c}}clone(){if(null===this._data)throw new DOMException("Detached","InvalidStateError");return new $({format:this.format,sampleRate:this.sampleRate,numberOfFrames:this.numberOfFrames,numberOfChannels:this.numberOfChannels,timestamp:this.timestamp,data:this._data})}close(){this._data=null}}function ee(e,t,i){switch(e){case"u8":case"u8-planar":return new Uint8Array(t,i);case"s16":case"s16-planar":return new Int16Array(t,i);case"s32":case"s32-planar":return new Int32Array(t,i);case"f32":case"f32-planar":return new Float32Array(t,i);default:throw new TypeError("Invalid AudioSampleFormat")}}function te(e){switch(e){case"u8":case"u8-planar":return 1;case"s16":case"s16-planar":return 2;case"s32":case"s32-planar":case"f32":case"f32-planar":return 4;default:throw new TypeError("Invalid AudioSampleFormat")}}function ie(e){switch(e){case"u8":case"s16":case"s32":case"f32":return!0;case"u8-planar":case"s16-planar":case"s32-planar":case"f32-planar":return!1;default:throw new TypeError("Invalid AudioSampleFormat")}}class se{constructor(){const e=this._eventer=new EventTarget;this.addEventListener=e.addEventListener.bind(e),this.removeEventListener=e.removeEventListener.bind(e),this.dispatchEvent=e.dispatchEvent.bind(e)}}class re extends se{constructor(){super(),this.addEventListener("dequeue",(e=>{this.ondequeue&&this.ondequeue(e)}))}}var ne=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};let oe=null;const ae=[];let le={},ce=null;function he(e){le=e}function pe(){return ne(this,void 0,void 0,(function*(){return ae.length?ae.shift():yield oe.LibAV(le)}))}function ue(e){ae.push(e)}function de(e,t){if("string"==typeof e){let i=e=e.replace(/\..*/,"");switch(e){case"flac":if(void 0===t.description)return null;break;case"opus":if(void 0!==t.description)return null;i="opus";break;case"vorbis":if(void 0===t.description)return null;i="libvorbis";break;case"av01":i="libaom-av1";break;case"vp09":i="libvpx-vp9";break;case"vp8":i="libvpx";break;case"avc1":i="h264";break;case"mp3":case"mp4a":case"ulaw":case"alaw":case"avc1":case"avc3":case"hev1":case"hvc1":return null;default:throw new TypeError("Unrecognized codec")}return ce.indexOf(e)>=0?{codec:i}:null}return e.libavjs}function fe(e,t){const i={};for(const s of t)s in e&&(i[s]=e[s]);return i}var ye=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};class me extends re{constructor(e){super(),this._p=Promise.all([]),this._libav=null,this._codec=this._c=this._pkt=this._frame=0,this._output=e.output,this._error=e.error,this.state="unconfigured",this.decodeQueueSize=0}configure(e){if("closed"===this.state)throw new DOMException("Decoder is closed","InvalidStateError");this._libav&&(this._p=this._p.then((()=>this._free()))),this.state="configured",this._p=this._p.then((()=>ye(this,void 0,void 0,(function*(){let t;if(e.description)if(ArrayBuffer.isView(e.description)){const i=e.description;t=new Uint8Array(i.buffer,i.byteOffset,i.byteLength)}else{const i=e.description;t=new Uint8Array(i)}const i=de(e.codec,e);if(!i)return void this._closeAudioDecoder(new DOMException("Unsupported codec","NotSupportedError"));const s=this._libav=yield pe(),r=yield s.avcodec_parameters_alloc(),n=[s.AVCodecParameters_channels_s(r,e.numberOfChannels),s.AVCodecParameters_sample_rate_s(r,e.sampleRate),s.AVCodecParameters_codec_type_s(r,1)];let o=0;t?(n.push(s.AVCodecParameters_extradata_size_s(r,t.byteLength)),o=yield s.calloc(t.byteLength+64,1),n.push(s.copyin_u8(o,t)),n.push(s.AVCodecParameters_extradata_s(r,o))):(n.push(s.AVCodecParameters_extradata_s(r,0)),n.push(s.AVCodecParameters_extradata_size_s(r,0))),yield Promise.all(n),[this._codec,this._c,this._pkt,this._frame]=yield s.ff_init_decoder(i.codec,r);const a=[s.AVCodecContext_time_base_s(this._c,1,1e3),s.avcodec_parameters_free_js(r)];o&&a.push(s.free(o)),yield Promise.all(a)})))).catch(this._error)}_free(){return ye(this,void 0,void 0,(function*(){this._c&&(yield this._libav.ff_free_decoder(this._c,this._pkt,this._frame),this._codec=this._c=this._pkt=this._frame=0),this._libav&&(ue(this._libav),this._libav=null)}))}_closeAudioDecoder(e){this._resetAudioDecoder(e),this.state="closed",this._p=this._p.then((()=>this._free())),"AbortError"!==e.name&&(this._p=this._p.then((()=>{this._error(e)})))}_resetAudioDecoder(e){if("closed"===this.state)throw new DOMException("Decoder closed","InvalidStateError");this.state="unconfigured",this._p=this._p.then((()=>this._free()))}decode(e){if("configured"!==this.state)throw new DOMException("Unconfigured","InvalidStateError");this.decodeQueueSize++,this._p=this._p.then((()=>ye(this,void 0,void 0,(function*(){const t=this._libav,i=this._c,s=this._pkt,r=this._frame;let n=null;this.decodeQueueSize--,this.dispatchEvent(new CustomEvent("dequeue"));try{const o=Math.floor(e.timestamp/1e3),[a,l]=t.f64toi64(o),c={data:e._libavGetData(),pts:a,ptshi:l,dts:a,dtshi:l};e.duration&&(c.duration=Math.floor(e.duration/1e3),c.durationhi=0),n=yield t.ff_decode_multi(i,s,r,[c])}catch(e){return void(this._p=this._p.then((()=>{this._closeAudioDecoder(e)})))}n&&this._outputAudioData(n)})))).catch(this._error)}_outputAudioData(e){const t=this._libav;for(const i of e){let e,s=!1;switch(i.format){case t.AV_SAMPLE_FMT_U8:e="u8";break;case t.AV_SAMPLE_FMT_S16:e="s16";break;case t.AV_SAMPLE_FMT_S32:e="s32";break;case t.AV_SAMPLE_FMT_FLT:e="f32";break;case t.AV_SAMPLE_FMT_U8P:e="u8-planar",s=!0;break;case t.AV_SAMPLE_FMT_S16P:e="s16-planar",s=!0;break;case t.AV_SAMPLE_FMT_S32P:e="s32-planar",s=!0;break;case t.AV_SAMPLE_FMT_FLTP:e="f32-planar",s=!0;break;default:throw new DOMException("Unsupported libav format!","EncodingError")}const r=i.sample_rate,n=i.nb_samples,o=i.channels,a=1e3*t.i64tof64(i.pts,i.ptshi);let l;if(s){let e=0;for(let t=0;t<i.data.length;t++)e+=i.data[t].length;l=new i.data[0].constructor(e),e=0;for(let t=0;t<i.data.length;t++){const s=i.data[t];l.set(s,e),e+=s.length}}else l=i.data;const c=new $({format:e,sampleRate:r,numberOfFrames:n,numberOfChannels:o,timestamp:a,data:l});this._output(c)}}flush(){if("configured"!==this.state)throw new DOMException("Invalid state","InvalidStateError");const e=this._p.then((()=>ye(this,void 0,void 0,(function*(){if(!this._c)return;const e=this._libav,t=this._c,i=this._pkt,s=this._frame;let r=null;try{r=yield e.ff_decode_multi(t,i,s,[],!0)}catch(e){this._p=this._p.then((()=>{this._closeAudioDecoder(e)}))}r&&this._outputAudioData(r)}))));return this._p=e,e}reset(){this._resetAudioDecoder(new DOMException("Reset","AbortError"))}close(){this._closeAudioDecoder(new DOMException("Close","AbortError"))}static isConfigSupported(e){return ye(this,void 0,void 0,(function*(){const t=de(e.codec,e);let i=!1;if(t){const e=yield pe();try{const[,s,r,n]=yield e.ff_init_decoder(t.codec);yield e.ff_free_decoder(s,r,n),i=!0}catch(e){}yield ue(e)}return{supported:i,config:fe(e,["codec","sampleRate","numberOfChannels"])}}))}}let ge=null;class _e{constructor(e,t){if(this.format="I420",this.codedWidth=0,this.codedHeight=0,this.codedRect=null,this.visibleRect=null,this.displayWidth=0,this.displayHeight=0,this.timestamp=0,this._layout=null,this._data=null,this._nonSquarePixels=!1,this._sar_num=1,this._sar_den=1,e instanceof ArrayBuffer||e.buffer instanceof ArrayBuffer)this._constructBuffer(e,t);else if(e instanceof _e||globalThis.VideoFrame&&e instanceof globalThis.VideoFrame){const i=new Uint8Array(e.allocationSize());e.copyTo(i),this._constructBuffer(i,{transfer:[i.buffer],format:e.format,codedHeight:e.codedHeight,codedWidth:e.codedWidth,colorSpace:e.colorSpace,visibleRect:(null==t?void 0:t.visibleRect)||e.visibleRect,displayHeight:(null==t?void 0:t.displayHeight)||e.displayHeight,displayWidth:(null==t?void 0:t.displayWidth)||e.displayWidth,duration:(null==t?void 0:t.duration)||e.duration,timestamp:(null==t?void 0:t.timestamp)||e.timestamp,metadata:JSON.parse(JSON.stringify(null==t?void 0:t.metadata))})}else if(e instanceof HTMLVideoElement){if(e.readyState===HTMLVideoElement.prototype.HAVE_NOTHING||e.readyState===HTMLVideoElement.prototype.HAVE_METADATA)throw new DOMException("Video is not ready for reading frames","InvalidStateError");if(e.networkState===e.NETWORK_EMPTY)throw new DOMException("Video network state is empty","InvalidStateError");this._constructCanvas(e,Object.assign(Object.assign({},t),{timestamp:(null==t?void 0:t.timestamp)||1e6*e.currentTime}))}else this._constructCanvas(e,t)}_constructCanvas(e,t){let i=0,s=0;if(e.naturalWidth?(i=e.naturalWidth,s=e.naturalHeight):e.videoWidth?(i=e.videoWidth,s=e.videoHeight):e.width&&(i=e.width,s=e.height),!i||!s)throw new DOMException("Could not determine dimensions","InvalidStateError");null===ge&&("undefined"!=typeof OffscreenCanvas?ge=new OffscreenCanvas(i,s):(ge=document.createElement("canvas"),ge.style.display="none",document.body.appendChild(ge))),ge.width=i,ge.height=s;const r=ge.getContext("2d",{desynchronized:!0,willReadFrequently:!0});r.clearRect(0,0,i,s),r.drawImage(e,0,0),this._constructBuffer(r.getImageData(0,0,i,s).data,{format:"RGBA",codedWidth:i,codedHeight:s,timestamp:(null==t?void 0:t.timestamp)||0,duration:(null==t?void 0:t.duration)||0,layout:[{offset:0,stride:4*i}],displayWidth:(null==t?void 0:t.displayWidth)||i,displayHeight:(null==t?void 0:t.displayHeight)||s})}_constructBuffer(e,t){_e._checkValidVideoFrameBufferInit(t);const i=new DOMRect(0,0,t.codedWidth,t.codedHeight);let s;t.visibleRect&&(s=DOMRect.fromRect(t.visibleRect)),this.codedWidth=t.codedWidth,this.codedHeight=t.codedHeight;const r=this._parseVisibleRect(i,s||null);let n;t.layout&&(n=t.layout instanceof Array?t.layout:Array.from(t.layout)),this.format=t.format;const o=this._computeLayoutAndAllocationSize(r,n||null);if(e.byteLength<o.allocationSize)throw new TypeError("data is too small for layout");let a=!1;if(t.transfer){let i,s;i=e.buffer?e.buffer:e,s=t.transfer instanceof Array?t.transfer:Array.from(t.transfer);for(const e of s)if(e===i){a=!0;break}}const l=t.format;if(t.layout)t.layout instanceof Array?this._layout=t.layout:this._layout=Array.from(t.layout);else{const e=Ee(l),t=[];let i=0;for(let s=0;s<e;s++){const e=be(l,s),r=Me(l,s),n=~~(this.codedWidth/e);t.push({offset:i,stride:n}),i+=n*~~(this.codedHeight/r)}this._layout=t}if(this._data=new Uint8Array(e.buffer||e,e.byteOffset||0),!a){const e=Ee(l);let t=this._layout,i=1/0,s=0;for(let r=0;r<e;r++){const e=t[r];let n=e.offset;n<i&&(i=n);const o=Me(l,r);n+=e.stride*~~(this.codedHeight/o),n>s&&(s=n)}0!==i&&(t=this._layout=t.map((e=>({offset:e.offset-i,stride:e.stride})))),this._data=this._data.slice(i,s)}const c=t.codedWidth,h=t.codedHeight;r.left,r.top,this.codedRect=new DOMRect(0,0,c,h),this.visibleRect=r,t.visibleRect?this.visibleRect=DOMRect.fromRect(t.visibleRect):this.visibleRect=new DOMRect(0,0,c,h),"number"==typeof t.displayWidth?this.displayWidth=t.displayWidth:this.displayWidth=this.visibleRect.width,"number"==typeof t.displayHeight?this.displayHeight=t.displayHeight:this.displayHeight=this.visibleRect.height,this.displayWidth!==this.visibleRect.width||this.displayHeight!==this.visibleRect.height?(this._nonSquarePixels=!0,this._sar_num=this.displayWidth*this.visibleRect.width,this._sar_den=this.displayHeight*this.visibleRect.height):(this._nonSquarePixels=!1,this._sar_num=this._sar_den=1),this.timestamp=t.timestamp,this.duration=t.duration}toNative(e={}){const t=new globalThis.VideoFrame(this._data,{layout:this._layout,format:this.format,codedWidth:this.codedWidth,codedHeight:this.codedHeight,visibleRect:this.visibleRect,displayWidth:this.displayWidth,displayHeight:this.displayHeight,duration:this.duration,timestamp:this.timestamp,transfer:e.transfer?[this._data.buffer]:[]});return e.transfer&&this.close(),t}static fromNative(e){const t=e,i=new Uint8Array(t.allocationSize());return t.copyTo(i),new _e(i,{format:t.format,codedWidth:t.codedWidth,codedHeight:t.codedHeight,visibleRect:t.visibleRect,displayWidth:t.displayWidth,displayHeight:t.displayHeight,duration:t.duration,timestamp:t.timestamp})}_libavGetData(){return this._data}_libavGetLayout(){return this._layout}static _checkValidVideoFrameBufferInit(e){if(!e.codedWidth||!e.codedHeight)throw new TypeError("Invalid coded dimensions");if(e.visibleRect){const t=DOMRect.fromRect(e.visibleRect);if(t.x<0||!Number.isFinite(t.x)||t.y<0||!Number.isFinite(t.y)||t.width<0||!Number.isFinite(t.width)||t.height<0||!Number.isFinite(t.height))throw new TypeError("Invalid visible rectangle");if(t.y+t.height>e.codedHeight)throw new TypeError("Visible rectangle outside of coded height");if(t.x+t.width>e.codedWidth)throw new TypeError("Visible rectangle outside of coded width");if(e.displayWidth&&!e.displayHeight||!e.displayWidth&&!e.displayHeight||0===e.displayWidth||0===e.displayHeight)throw new TypeError("Invalid display dimensions")}}metadata(){if(null===this._data)throw new DOMException("Detached","InvalidStateError");return null}allocationSize(e={}){if(null===this._data)throw new DOMException("Detached","InvalidStateError");if(null===this.format)throw new DOMException("Not supported","NotSupportedError");return this._parseVideoFrameCopyToOptions(e).allocationSize}_parseVideoFrameCopyToOptions(e){const t=this.visibleRect;let i=e.rect?new DOMRect(e.rect.x,e.rect.y,e.rect.width,e.rect.height):null;const s=this._parseVisibleRect(t,i);let r=null;return e.layout&&(r=e.layout instanceof Array?e.layout:Array.from(e.layout)),this._computeLayoutAndAllocationSize(s,r)}_parseVisibleRect(e,t){let i=e;if(t){if(0===t.width||0===t.height)throw new TypeError("Invalid rectangle");if(t.x+t.width>this.codedWidth)throw new TypeError("Invalid rectangle");if(t.y+t.height>this.codedHeight)throw new TypeError("Invalid rectangle");i=t}if(!this._verifyRectOffsetAlignment(i))throw new TypeError("Invalid alignment");return i}_computeLayoutAndAllocationSize(e,t){let i=Ee(this.format);if(t&&t.length!==i)throw new TypeError("Invalid layout");let s=0,r=[],n=[],o=0;for(;o<i;){const i=ve(this.format,o),a=be(this.format,o),l=Me(this.format,o),c={destinationOffset:0,destinationStride:0,sourceTop:Math.ceil(~~e.y/l),sourceHeight:Math.ceil(~~e.height/l),sourceLeftBytes:~~(e.x/a*i),sourceWidthBytes:~~(e.width/a*i)};if(t){const e=t[o];if(e.stride<c.sourceWidthBytes)throw new TypeError("Invalid stride");c.destinationOffset=e.offset,c.destinationStride=e.stride}else c.destinationOffset=s,c.destinationStride=c.sourceWidthBytes;const h=c.destinationStride*c.sourceHeight,p=h+c.destinationOffset;if(h>=4294967296||p>=4294967296)throw new TypeError("Plane too large");n.push(p),p>s&&(s=p);let u=0;for(;u<o;){if(!(p<=r[u].destinationOffset||n[u]<=c.destinationOffset))throw new TypeError("Invalid plane layout");u++}r.push(c),o++}return{computedLayouts:r,allocationSize:s}}_verifyRectOffsetAlignment(e){if(!this.format)return!0;let t=0;const i=Ee(this.format);for(;t<i;){const i=be(this.format,t),s=Me(this.format,t),r=e.x/i;if(r!==~~r)return!1;const n=e.y/s;if(n!==~~n)return!1;t++}return!0}copyTo(e){return t=this,i=arguments,r=function*(e,t={}){const i=new Uint8Array(e.buffer||e,e.byteOffset||0);if(null===this._data)throw new DOMException("Detached","InvalidStateError");if(!this.format)throw new DOMException("No format","NotSupportedError");const s=this._parseVideoFrameCopyToOptions(t);if(e.byteLength<s.allocationSize)throw new TypeError("Insufficient space");let r=[];{Ee(this.format);let e=0;for(;e<s.computedLayouts.length;){const t=this._layout[e].stride,n=s.computedLayouts[e];let o=n.sourceTop*t;o+=n.sourceLeftBytes;let a=n.destinationOffset;const l=n.sourceWidthBytes,c={offset:n.destinationOffset,stride:n.destinationStride};let h=0;for(;h<n.sourceHeight;)i.set(this._data.subarray(o,o+l),a),o+=t,a+=n.destinationStride,h++;e++,r.push(c)}}return r},new((s=void 0)||(s=Promise))((function(e,n){function o(e){try{l(r.next(e))}catch(e){n(e)}}function a(e){try{l(r.throw(e))}catch(e){n(e)}}function l(t){var i;t.done?e(t.value):(i=t.value,i instanceof s?i:new s((function(e){e(i)}))).then(o,a)}l((r=r.apply(t,i||[])).next())}));var t,i,s,r}clone(){return null}close(){this._data=null}}function we(e,t){let i=e.AV_PIX_FMT_RGBA;switch(t){case"I420":i=e.AV_PIX_FMT_YUV420P;break;case"I420P10":i=62;break;case"I420P12":i=123;break;case"I420A":i=e.AV_PIX_FMT_YUVA420P;break;case"I420AP10":i=87;break;case"I420AP12":throw new TypeError("YUV420P12 is not supported by libav");case"I422":i=e.AV_PIX_FMT_YUV422P;break;case"I422P10":i=64;break;case"I422P12":i=127;break;case"I422A":i=78;break;case"I422AP10":i=89;break;case"I422AP10":i=186;break;case"I444":i=e.AV_PIX_FMT_YUV444P;break;case"I444P10":i=68;break;case"I444P12":i=131;break;case"I444A":i=79;break;case"I444AP10":i=91;break;case"I444AP12":i=188;break;case"NV12":i=e.AV_PIX_FMT_NV12;break;case"RGBA":i=e.AV_PIX_FMT_RGBA;break;case"RGBX":i=119;break;case"BGRA":i=e.AV_PIX_FMT_BGRA;break;case"BGRX":i=121;break;default:throw new TypeError("Invalid VideoPixelFormat")}return i}function Ee(e){switch(e){case"I420":case"I420P10":case"I420P12":case"I422":case"I422P10":case"I422P12":case"I444":case"I444P10":case"I444P12":return 3;case"I420A":case"I420AP10":case"I420AP12":case"I422A":case"I422AP10":case"I422AP12":case"I444A":case"I444AP10":case"I444AP12":return 4;case"NV12":return 2;case"RGBA":case"RGBX":case"BGRA":case"BGRX":return 1;default:throw new DOMException("Unsupported video pixel format","NotSupportedError")}}function ve(e,t){switch(e){case"I420":case"I420A":case"I422":case"I422A":case"I444":case"I444A":return 1;case"I420P10":case"I420AP10":case"I422P10":case"I422AP10":case"I444P10":case"I444AP10":case"I420P12":case"I420AP12":case"I422P12":case"I422AP12":case"I444P12":case"I444AP12":return 2;case"NV12":return 1===t?2:1;case"RGBA":case"RGBX":case"BGRA":case"BGRX":return 4;default:throw new DOMException("Unsupported video pixel format","NotSupportedError")}}function be(e,t){if(0===t)return 1;if(3===t)return 1;switch(e){case"I420":case"I420P10":case"I420P12":case"I420A":case"I420AP10":case"I420AP12":case"I422":case"I422P10":case"I422P12":case"I422A":case"I422AP10":case"I422AP12":case"NV12":return 2;case"I444":case"I444P10":case"I444P12":case"I444A":case"I444AP10":case"I444AP12":case"RGBA":case"RGBX":case"BGRA":case"BGRX":return 1;default:throw new DOMException("Unsupported video pixel format","NotSupportedError")}}function Me(e,t){if(0===t)return 1;if(3===t)return 1;switch(e){case"I420":case"I420P10":case"I420P12":case"I420A":case"I420AP10":case"I420AP12":case"NV12":return 2;case"I422":case"I422P10":case"I422P12":case"I422A":case"I422AP10":case"I422AP12":case"I444":case"I444P10":case"I444P12":case"I444A":case"I444AP10":case"I444AP12":case"RGBA":case"RGBX":case"BGRA":case"BGRX":return 1;default:throw new DOMException("Unsupported video pixel format","NotSupportedError")}}var Te=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};class Ie extends re{constructor(e){super(),this._p=Promise.all([]),this._libav=null,this._codec=this._c=this._pkt=this._frame=0,this._output=e.output,this._error=e.error,this.state="unconfigured",this.decodeQueueSize=0}configure(e){if("closed"===this.state)throw new DOMException("Decoder is closed","InvalidStateError");this._libav&&(this._p=this._p.then((()=>this._free()))),this.state="configured",this._p=this._p.then((()=>Te(this,void 0,void 0,(function*(){const t=de(e.codec,e);if(!t)return void this._closeVideoDecoder(new DOMException("Unsupported codec","NotSupportedError"));const i=this._libav=yield pe();[this._codec,this._c,this._pkt,this._frame]=yield i.ff_init_decoder(t.codec),yield i.AVCodecContext_time_base_s(this._c,1,1e3)})))).catch(this._error)}_free(){return Te(this,void 0,void 0,(function*(){this._c&&(yield this._libav.ff_free_decoder(this._c,this._pkt,this._frame),this._codec=this._c=this._pkt=this._frame=0),this._libav&&(ue(this._libav),this._libav=null)}))}_closeVideoDecoder(e){this._resetVideoDecoder(e),this.state="closed",this._p=this._p.then((()=>this._free())),"AbortError"!==e.name&&(this._p=this._p.then((()=>{this._error(e)})))}_resetVideoDecoder(e){if("closed"===this.state)throw new DOMException("Decoder closed","InvalidStateError");this.state="unconfigured",this._p=this._p.then((()=>this._free()))}decode(e){const t=this;if("configured"!==this.state)throw new DOMException("Unconfigured","InvalidStateError");this.decodeQueueSize++,this._p=this._p.then((function(){return Te(this,void 0,void 0,(function*(){const i=t._libav,s=t._c,r=t._pkt,n=t._frame;let o=null;t.decodeQueueSize--,t.dispatchEvent(new CustomEvent("dequeue"));try{const t=Math.floor(e.timestamp/1e3),[a,l]=i.f64toi64(t),c={data:e._libavGetData(),pts:a,ptshi:l,dts:a,dtshi:l};e.duration&&(c.duration=Math.floor(e.duration/1e3),c.durationhi=0),o=yield i.ff_decode_multi(s,r,n,[c])}catch(e){t._p=t._p.then((()=>{t._closeVideoDecoder(e)}))}o&&t._outputVideoFrames(o)}))})).catch(this._error)}_outputVideoFrames(e){const t=this._libav;for(const i of e){let e;switch(i.format){case t.AV_PIX_FMT_YUV420P:e="I420";break;case 62:e="I420P10";break;case 123:e="I420P12";break;case t.AV_PIX_FMT_YUVA420P:e="I420A";break;case 87:e="I420AP10";break;case t.AV_PIX_FMT_YUV422P:e="I422";break;case 64:e="I422P10";break;case 127:e="I422P12";break;case 78:e="I422A";break;case 89:e="I422AP10";break;case 186:e="I422AP12";break;case t.AV_PIX_FMT_YUV444P:e="I444";break;case 68:e="I444P10";break;case 131:e="I444P12";break;case 79:e="I444A";break;case 91:e="I444AP10";break;case 188:e="I444AP12";break;case t.AV_PIX_FMT_NV12:e="NV12";break;case t.AV_PIX_FMT_RGBA:e="RGBA";break;case 119:e="RGBX";break;case t.AV_PIX_FMT_BGRA:e="BGRA";break;case 121:e="BGRX";break;default:throw new DOMException("Unsupported libav format!","EncodingError")}const s=i.width,r=i.height;let n;n=i.crop?new DOMRect(i.crop.left,i.crop.top,s-i.crop.left-i.crop.right,r-i.crop.top-i.crop.bottom):new DOMRect(0,0,s,r);let o=s,a=r;if(i.sample_aspect_ratio&&i.sample_aspect_ratio[0]){const e=i.sample_aspect_ratio;e[0]>e[1]?o=~~(s*e[0]/e[1]):a=~~(r*e[1]/e[0])}const l=1e3*t.i64tof64(i.pts,i.ptshi),c=new _e(i.data,{layout:i.layout,format:e,codedWidth:s,codedHeight:r,visibleRect:n,displayWidth:o,displayHeight:a,timestamp:l});this._output(c)}}flush(){if("configured"!==this.state)throw new DOMException("Invalid state","InvalidStateError");const e=this._p.then((()=>Te(this,void 0,void 0,(function*(){if(!this._c)return;const e=this._libav,t=this._c,i=this._pkt,s=this._frame;let r=null;try{r=yield e.ff_decode_multi(t,i,s,[],!0)}catch(e){this._p=this._p.then((()=>{this._closeVideoDecoder(e)}))}r&&this._outputVideoFrames(r)}))));return this._p=e,e}reset(){this._resetVideoDecoder(new DOMException("Reset","AbortError"))}close(){this._closeVideoDecoder(new DOMException("Close","AbortError"))}static isConfigSupported(e){return Te(this,void 0,void 0,(function*(){const t=de(e.codec,e);let i=!1;if(t){const e=yield pe();try{const[,s,r,n]=yield e.ff_init_decoder(t.codec);yield e.ff_free_decoder(s,r,n),i=!0}catch(e){}yield ue(e)}return{supported:i,config:fe(e,["codec","codedWidth","codedHeight"])}}))}}var Ae=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};let Oe=null,Se=null,Fe=null,Ce=null,ke=null;function De(e,t,i,s,r,n,o,a,l,c,h){if(!t._data)return Fe.apply(e,Array.prototype.slice.call(arguments,1));let p,u;void 0===r?(a=i,l=s):void 0===a?(a=i,l=s,c=r,h=n,p=void 0,u=void 0,r=void 0,n=void 0):(p=i,u=s),void 0===c&&(c=t.displayWidth,h=t.displayHeight);const d=we(Oe,t.format),f=Oe.sws_getContext_sync(t.visibleRect.width,t.visibleRect.height,d,c,h,Oe.AV_PIX_FMT_RGBA,2,0,0,0),y=Oe.av_frame_alloc_sync(),m=Oe.av_frame_alloc_sync();let g,_;t._libavGetData?(g=t._libavGetData(),_=t._libavGetLayout()):(g=t._data,_=t._layout),Oe.ff_copyin_frame_sync(y,{data:g,layout:_,format:d,width:t.codedWidth,height:t.codedHeight,crop:{left:t.visibleRect.left,right:t.visibleRect.right,top:t.visibleRect.top,bottom:t.visibleRect.bottom}}),Oe.sws_scale_frame_sync(f,m,y);const w=Oe.ff_copyout_frame_video_imagedata_sync(m);switch(o){case 0:e.putImageData(w,a,l);break;case 90:e.putImageData(function(e){const t=e.width,i=e.height,s=i,r=t,n=new Uint8ClampedArray(s*r*4);for(let r=0;r<i;r++)for(let i=0;i<t;i++){const o=4*(r*t+i),a=4*(i*s+(s-r-1));n[a]=e.data[o],n[a+1]=e.data[o+1],n[a+2]=e.data[o+2],n[a+3]=e.data[o+3]}return new ImageData(n,s,r)}(w),a,l);break;case 180:e.putImageData(function(e){const t=e.width,i=e.height,s=t,r=i,n=new Uint8ClampedArray(s*r*4);for(let s=0;s<i;s++)for(let r=0;r<t;r++){const o=4*(s*t+r),a=4*((i-s-1)*t+(t-r-1));n[a]=e.data[o],n[a+1]=e.data[o+1],n[a+2]=e.data[o+2],n[a+3]=e.data[o+3]}return new ImageData(n,s,r)}(w),a,l);break;case 270:e.putImageData(function(e){const t=e.width,i=e.height,s=i,r=t,n=new Uint8ClampedArray(s*r*4);for(let r=0;r<i;r++)for(let i=0;i<t;i++){const o=4*(r*t+i),a=4*((t-i-1)*s+r);n[a]=e.data[o],n[a+1]=e.data[o+1],n[a+2]=e.data[o+2],n[a+3]=e.data[o+3]}return new ImageData(n,s,r)}(w),a,l)}Oe.av_frame_free_js_sync(m),Oe.av_frame_free_js_sync(y),Oe.sws_freeContext_sync(f)}function Re(e,t,i,s,r,n,o,a,l){return e instanceof _e?De(this,e,t,i,s,r,n,o,a,l):Fe.apply(this,arguments)}function Ne(e,t,i,s,r,n,o,a,l){return e instanceof _e?De(this,e,t,i,s,r,n,o,a,l):Ce.apply(this,arguments)}function Pe(e,t={}){if(!e._data)return ke.apply(globalThis,arguments);const i=we(Se,e.format),s="number"==typeof t.resizeWidth?t.resizeWidth:e.displayWidth,r="number"==typeof t.resizeHeight?t.resizeHeight:e.displayHeight;return(()=>Ae(this,void 0,void 0,(function*(){const[t,n,o]=yield Promise.all([Se.sws_getContext(e.visibleRect.width,e.visibleRect.height,i,s,r,Se.AV_PIX_FMT_RGBA,2,0,0,0),Se.av_frame_alloc(),Se.av_frame_alloc()]);let a,l;e._libavGetData?(a=e._libavGetData(),l=e._libavGetLayout()):e._data?(a=e._data,l=e._layout):(a=new Uint8Array(e.allocationSize()),yield e.copyTo(a)),yield Se.ff_copyin_frame(n,{data:a,layout:l,format:i,width:e.codedWidth,height:e.codedHeight,crop:{left:e.visibleRect.left,right:e.visibleRect.right,top:e.visibleRect.top,bottom:e.visibleRect.bottom}}),yield Se.sws_scale_frame(t,o,n);const c=yield Se.ff_copyout_frame_video_imagedata(o);return yield Promise.all([Se.av_frame_free_js(o),Se.av_frame_free_js(n),Se.sws_freeContext(t)]),yield ke(c)})))()}Error;var ze=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};function Be(){return ze(this,arguments,void 0,(function*(e={}){let t={};var i;if(e.libavOptions&&Object.assign(t,e.libavOptions),e.LibAV||void 0!==globalThis.LibAV||(yield new Promise(((e,t)=>{const i="https://cdn.jsdelivr.net/npm/@uwx/<EMAIL>.6.1.1/dist";globalThis.LibAV={base:i};const s="libav-all.min.js";if("undefined"!=typeof importScripts)importScripts(`${i}/${s}`),e(void 0);else{const r=document.createElement("script");r.src=`${i}/${s}`,r.onload=e,r.onerror=t,document.body.appendChild(r)}}))),t.noworker=!0,e.LibAV&&(i=e.LibAV,oe=i),he(t),yield function(){return ne(this,void 0,void 0,(function*(){oe=oe||LibAV,ce=yield function(){return ne(this,void 0,void 0,(function*(){const e=yield pe(),t=[];for(var i=0;i<1e3;i++)yield e.avcodec_find_decoder(i);for(const[i,s]of[["h264","avc1"],["opus","opus"]])(yield e.avcodec_find_decoder_by_name(i))&&t.push(s);return ue(e),t}))}()}))}(),e.polyfill)for(const e of[["EncodedAudioChunk",Q],["AudioData",$],["AudioDecoder",me],["VideoFrame",_e],["VideoDecoder",Ie]])globalThis[e[0]]||(globalThis[e[0]]=e[1]);yield function(e,t){return Ae(this,void 0,void 0,(function*(){"importScripts"in globalThis&&(oe.nolibavworker=!0),Oe=yield oe.LibAV({noworker:!0}),Se=yield oe.LibAV(e),"CanvasRenderingContext2D"in globalThis&&(Fe=CanvasRenderingContext2D.prototype.drawImage,t&&(CanvasRenderingContext2D.prototype.drawImage=Re)),"OffscreenCanvasRenderingContext2D"in globalThis&&(Ce=OffscreenCanvasRenderingContext2D.prototype.drawImage,t&&(OffscreenCanvasRenderingContext2D.prototype.drawImage=Ne)),ke=globalThis.createImageBitmap,t&&(globalThis.createImageBitmap=Pe)}))}(t,!!e.polyfill)}))}const We=Q,xe=me,Le=Ie,je=De,Ge=Z;var Ve,Ue,Ye,He,qe,Ke,Xe,Je,Qe,Ze,$e,et,tt,it,st=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))},rt=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},nt=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class ot{constructor(e,t){Ve.add(this),Ue.set(this,void 0),Ye.set(this,void 0),He.set(this,void 0),qe.set(this,void 0),Ke.set(this,void 0),Xe.set(this,void 0),Je.set(this,void 0),Qe.set(this,void 0),Ze.set(this,void 0),$e.set(this,void 0),rt(this,$e,void 0!==globalThis.SharedArrayBuffer,"f"),nt(this,$e,"f")||console.warn("SharedArrayBuffer is not supported, audio render will be disabled"),rt(this,Je,e,"f"),rt(this,Ue,new AudioContext({latencyHint:"interactive",sampleRate:e.sample_rate}),"f"),rt(this,Ye,this.load(e),"f"),rt(this,Xe,t,"f"),nt(this,$e,"f")&&(rt(this,Ke,new k(2,e.sample_rate),"f"),null!=nt(this,Ke,"f")&&rt(this,qe,new D(nt(this,Ke,"f")),"f")),rt(this,Ze,new TransformStream({start:nt(this,Ve,"m",et).bind(this),transform:nt(this,Ve,"m",tt).bind(this)}),"f"),nt(this,Ve,"m",it).call(this).catch((e=>console.error("failed to run audio renderer: ",e)))}load(e){return st(this,void 0,void 0,(function*(){const t=new Blob([`/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/render/ring_audio.ts":
/*!**********************************!*\
  !*** ./src/render/ring_audio.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PCMFrame: () => (/* binding */ PCMFrame),
/* harmony export */   Ring: () => (/* binding */ Ring),
/* harmony export */   RingShared: () => (/* binding */ RingShared)
/* harmony export */ });
// Ring buffer with audio samples.
var __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _Ring_instances, _Ring_copyToPlanar;
var STATE;
(function (STATE) {
    STATE[STATE["READ_POS"] = 0] = "READ_POS";
    STATE[STATE["WRITE_POS"] = 1] = "WRITE_POS";
    STATE[STATE["LENGTH"] = 2] = "LENGTH";
})(STATE || (STATE = {}));
class PCMFrame {
    constructor(numberOfFrames, numberOfChannels, buff) {
        this.numberOfFrames = numberOfFrames;
        this.numberOfChannels = numberOfChannels;
        this.format = "float";
        this.buff = buff;
    }
    copyTo(dst, options) {
        const frameCount = options.frameCount || this.numberOfFrames;
        const frameOffset = options.frameOffset || 0;
        const planeIndex = options.planeIndex;
        const startIndex = frameOffset + this.numberOfChannels * planeIndex;
        const endIndex = startIndex + frameCount;
        dst.set(this.buff.slice(startIndex, endIndex));
    }
}
// No prototype to make this easier to send via postMessage
class RingShared {
    constructor(channels, capacity) {
        // Store the current state in a separate ring buffer.
        this.state = new SharedArrayBuffer(STATE.LENGTH * Int32Array.BYTES_PER_ELEMENT);
        // Create a buffer for each audio channel
        this.channels = [];
        for (let i = 0; i < channels; i += 1) {
            const buffer = new SharedArrayBuffer(capacity * Float32Array.BYTES_PER_ELEMENT);
            this.channels.push(buffer);
        }
        this.capacity = capacity;
    }
}
class Ring {
    constructor(shared, channels, capacity) {
        _Ring_instances.add(this);
        this.startCache = 9600; //开始的时候先有这么多数据再播放,减少卡顿
        if (shared) {
            this.state = new Int32Array(shared.state);
            this.channels = [];
            for (const channel of shared.channels) {
                this.channels.push(new Float32Array(channel));
            }
            this.capacity = shared.capacity;
        }
        else {
            this.state = new Int32Array(STATE.LENGTH);
            this.channels = [];
            for (let i = 0; i < channels; i += 1) {
                const buffer = new Float32Array(capacity);
                this.channels.push(buffer);
            }
            this.capacity = capacity;
        }
        for (let i = 0; i < STATE.LENGTH; i += 1) {
            this.state[i] = 0;
        }
        this.start = true;
        this.startCache = 12000; //250ms, 开始的时候先有这么多数据再播放,减少卡顿
        //this.startCache = 480000; //250ms, 开始的时候先有这么多数据再播放,减少卡顿
    }
    write(frame) {
        const readPos = Atomics.load(this.state, STATE.READ_POS);
        const writePos = Atomics.load(this.state, STATE.WRITE_POS);
        //console.log("write " + Date.now() + '---' + readPos + " --- " + writePos)
        const available = this.capacity - (writePos - readPos);
        if (available <= 0)
            return 0;
        const toWrite = Math.min(frame.numberOfFrames, available);
        const startPos = writePos;
        const endPos = writePos + toWrite;
        const startIndex = startPos % this.capacity;
        const endIndex = endPos % this.capacity;
        __classPrivateFieldGet(this, _Ring_instances, "m", _Ring_copyToPlanar).call(this, frame, startIndex, endIndex);
        Atomics.store(this.state, STATE.WRITE_POS, endPos);
        return toWrite;
    }
    read(dst) {
        const readPos = Atomics.load(this.state, STATE.READ_POS);
        const writePos = Atomics.load(this.state, STATE.WRITE_POS);
        if (this.start) {
            //console.log("start " + Date.now() + '---' + (writePos - readPos))
            if (writePos - readPos < this.startCache) { //开始的时候先有这么多数据再播放,减少卡顿
                return 0;
            }
            else {
                this.start = false;
            }
        }
        const startPos = readPos;
        let endPos = startPos + dst[0].length;
        //console.log("read____  " + readPos + " --- " + writePos)
        if (endPos > writePos) {
            endPos = writePos;
            if (endPos <= startPos) {
                this.start = true;
                this.startCache = 2400; //50ms
                return 0;
            }
        }
        const startIndex = startPos % this.capacity;
        const endIndex = endPos % this.capacity;
        // Loop over each channel
        for (let i = 0; i < 1; i += 1) {
            if (i >= this.channels.length) {
                // ignore excess channels
            }
            const input = this.channels[i];
            const output = dst[i];
            if (startIndex < endIndex) {
                const full = input.subarray(startIndex, endIndex);
                output.set(full);
            }
            else {
                const first = input.subarray(startIndex);
                const second = input.subarray(0, endIndex);
                output.set(first);
                output.set(second, first.length);
            }
        }
        Atomics.store(this.state, STATE.READ_POS, endPos);
        return endPos - startPos;
    }
    clear() {
        const pos = Atomics.load(this.state, STATE.WRITE_POS);
        Atomics.store(this.state, STATE.READ_POS, pos);
    }
    size() {
        // TODO is this thread safe?
        const readPos = Atomics.load(this.state, STATE.READ_POS);
        const writePos = Atomics.load(this.state, STATE.WRITE_POS);
        return writePos - readPos;
    }
}
_Ring_instances = new WeakSet(), _Ring_copyToPlanar = function _Ring_copyToPlanar(frame, startIndex, endIndex) {
    // Loop over each channel
    for (let i = 0; i < this.channels.length; i += 1) {
        const channel = this.channels[i];
        // If the AudioData doesn't have enough channels, duplicate it.
        const planeIndex = Math.min(i, frame.numberOfChannels - 1);
        if (startIndex < endIndex) {
            const full = channel.subarray(startIndex, endIndex);
            frame.copyTo(full, {
                planeIndex,
                frameCount: endIndex - startIndex,
                format: "f32-planar"
            });
        }
        else {
            const first = channel.subarray(startIndex);
            const second = channel.subarray(0, endIndex);
            frame.copyTo(first, {
                planeIndex,
                frameCount: first.length,
                format: "f32-planar"
            });
            if (second.length) {
                frame.copyTo(second, {
                    planeIndex,
                    frameOffset: first.length,
                    frameCount: second.length,
                    format: "f32-planar"
                });
            }
        }
    }
};


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!********************************************************************************!*\
  !*** ./node_modules/ts-loader/index.js!./src/render/audio_render_workerlet.ts ***!
  \********************************************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ring_audio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ring_audio */ "./src/render/ring_audio.ts");

class AudioRenderWorklet extends AudioWorkletProcessor {
    constructor() {
        super();
        this.base = 0;
        this.port.onmessage = this.onMessage.bind(this);
    }
    onMessage(e) {
        if (!("config" in e.data)) {
            const chunk = e.data;
            const frame = new _ring_audio__WEBPACK_IMPORTED_MODULE_0__.PCMFrame(chunk.numberOfFrames, chunk.numberOfChannels, chunk.buff);
            this.ring.write(frame);
        }
        else {
            const msg = e.data;
            if (msg.config) {
                this.onConfig(msg.config);
            }
        }
    }
    onConfig(config) {
        this.ring = new _ring_audio__WEBPACK_IMPORTED_MODULE_0__.Ring(config.ring, config.channels, config.sampleRate); //1s
        //this.ring = new Ring(config.ring, config.channels, config.sampleRate * 100) //100s
    }
    // Inputs and outputs in groups of 128 samples.
    process(inputs, outputs, _parameters) {
        if (!this.ring) {
            // Paused
            return true;
        }
        if (inputs.length != 1 && outputs.length != 1) {
            throw new Error("only a single track is supported");
        }
        if (this.ring.size() == this.ring.capacity) {
            // 目前先清空队列，后面再优化
            console.warn("resyncing ring buffer");
            this.ring.clear();
            return true;
        }
        const output = outputs[0];
        const size = this.ring.read(output);
        //console.log(" " + output.length + " " + size)
        if (size < output.length) {
            //TODO trigger rebuffering event
        }
        return true;
    }
}
try {
    registerProcessor("audio_renderer", AudioRenderWorklet);
}
catch (error) {
    console.error("failed to register audio renderer: ", error);
}

})();

/******/ })()
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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`],{type:"text/javascript"}),i=URL.createObjectURL(t);yield nt(this,Ue,"f").audioWorklet.addModule(i),nt(this,Ue,"f").createGain().gain.value=2;const s=new AudioWorkletNode(nt(this,Ue,"f"),"audio_renderer");s.port.addEventListener("message",this.on.bind(this)),s.onprocessorerror=e=>{console.error("Audio worklet error:",e)},s.connect(nt(this,Ue,"f").destination);const r={sampleRate:e.sample_rate,channels:e.channel_count,ring:nt(this,Ke,"f")};return s.port.postMessage({config:r}),rt(this,He,s.port,"f"),s}))}on(e){}play(){nt(this,Ue,"f").resume().catch((e=>console.warn("failed to resume audio context: ",e)))}close(){nt(this,Ue,"f").close().catch((e=>console.warn("failed to close audio context: ",e)))}}Ue=new WeakMap,Ye=new WeakMap,He=new WeakMap,qe=new WeakMap,Ke=new WeakMap,Xe=new WeakMap,Je=new WeakMap,Qe=new WeakMap,Ze=new WeakMap,$e=new WeakMap,Ve=new WeakSet,et=function(e){rt(this,Qe,new xe({output:t=>{e.enqueue(t)},error:console.warn}),"f"),nt(this,Qe,"f").configure({codec:nt(this,Je,"f").codec,sampleRate:nt(this,Je,"f").sample_rate,numberOfChannels:nt(this,Je,"f").channel_count})},tt=function(e){const t=new We({type:e.type,timestamp:1e3*e.timestamp,data:e.data});nt(this,Qe,"f").decode(t)},it=function(){return st(this,void 0,void 0,(function*(){const e=nt(this,Xe,"f").frames.pipeThrough(nt(this,Ze,"f")).getReader();for(;;){const{value:i,done:s}=yield e.read();if(s)break;if(null!=nt(this,qe,"f")){const e=nt(this,qe,"f").write(i);e<i.numberOfFrames&&console.warn(`droppped ${i.numberOfFrames-e} audio samples`)}else{var t=new Float32Array(i.numberOfFrames*i.numberOfChannels);i.copyTo(t,{planeIndex:0,format:"f32-planar"}),nt(this,He,"f")&&nt(this,He,"f").postMessage({numberOfFrames:i.numberOfFrames,numberOfChannels:i.numberOfChannels,buff:t},[t.buffer])}}}))};var at,lt=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};function ct(e){let t="VideoDecoder"in window;return!t&&e&&console.log("HW VideoDecoder not supported"),t}function ht(e){let t="AudioDecoder"in window;return!t&&e&&console.log("HW AudioDecoder not supported"),t}let pt;!function(e){e.h264BaseLine="avc1.42001E",e.h264Main="avc1.4D401E",e.h264High="avc1.64001E",e.h265="hvc1.1.1.L123.00"}(at||(at={}));let ut=!1;function dt(e=!0){ut=e,pt||(pt=new Set),ut&&pt.add(at.h264BaseLine)}function ft(){return lt(this,void 0,void 0,(function*(){return new Promise(((e,t)=>lt(this,void 0,void 0,(function*(){pt?e(pt):pt=new Set;const t=Object.values(at).map((e=>lt(this,void 0,void 0,(function*(){const t=yield function(e){return lt(this,arguments,void 0,(function*(e,t=!1,i=1280,s=720){try{return(yield VideoDecoder.isConfigSupported({codec:e})).supported}catch(e){return!1}}))}(e);t&&pt.add(e)}))));yield Promise.all(t),e(pt)}))))}))}function yt(){return pt}function mt(){var e,t;let i,s=self.navigator.userAgent,r={name:"unknow",device:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(s)?"mobile":"h5"};return(i=/edge\/([\d\.]+)/i.exec(s))?(r.name="Edge",r.version=i[1],r):(i=/edg\/([\d\.]+)/i.exec(s))?(r.name="Edge(Chromium)",r.version=i[1],r):/msie/i.test(s)?(r.name="Internet Explorer",r.version=(null===(e=/msie ([\d\.]+)/i.exec(s))||void 0===e?void 0:e[1])||"",r):/Trident/i.test(s)?(r.name="Internet Explorer",r.version=(null===(t=/rv:([\d\.]+)/i.exec(s))||void 0===t?void 0:t[1])||"",r):(i=/chrome\/([\d\.]+)/i.exec(s))?(r.name="Chrome",r.version=i[1],r):(i=/version\/([\d\.]+).*safari/i.exec(s))?(r.name="Safari",r.version=i[1],r):(i=/firefox\/([\d\.]+)/i.exec(s))?(r.name="Firefox",r.version=i[1],r):(i=s.match(/iPhone OS (\d+)_(\d+)_(\d+)/))?(r.name="Safari",r.version=i[1]+"."+i[2]+"."+i[3],r):(console.log(r),r)}function gt(e,t){const i=e.split(".").map(Number),s=t.split(".").map(Number),r=Math.max(i.length,s.length);for(let e=0;e<r;e++){const t=i[e]||0,r=s[e]||0;if(t>r)return 1;if(t<r)return-1}return 0}function _t(){return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}function wt(e){return!((!ht()||!ct())&&(!ct()||!ut)&&(e&&!ut||!(_t&&Et()>0)))}function Et(){if(!_t())return 60;let e=mt();if("Safari"===e.name){if(gt(e.version,"17")>0)return 30;if(gt(e.version,"16")>0)return 20}return 0}function vt(){let e=mt();return"Safari"===e.name&&gt(e.version,"18")<=0}var bt,Mt,Tt,It,At,Ot,St,Ft,Ct,kt,Dt=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Rt=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class Nt{constructor(e,t,i){bt.add(this),Mt.set(this,void 0),Tt.set(this,void 0),It.set(this,void 0),At.set(this,void 0),Ot.set(this,void 0),St.set(this,void 0),Dt(this,Mt,e,"f"),Dt(this,Tt,t,"f"),Dt(this,It,i,"f"),Dt(this,St,0,"f"),Dt(this,Ot,new TransformStream({start:Rt(this,bt,"m",Ct).bind(this),transform:Rt(this,bt,"m",kt).bind(this)}),"f"),Rt(this,bt,"m",Ft).call(this).catch((e=>console.error("failed to run video renderer hw: ",e)))}close(){}}Mt=new WeakMap,Tt=new WeakMap,It=new WeakMap,At=new WeakMap,Ot=new WeakMap,St=new WeakMap,bt=new WeakSet,Ft=function(){return e=this,t=void 0,s=function*(){const e=Rt(this,It,"f").frames.pipeThrough(Rt(this,Ot,"f")).getReader();let t=!1;for(;;){const{value:i,done:s}=yield e.read();if(s)break;if(t){i.close();continue}t=!0;const r=Rt(this,St,"f");Promise.race([self.requestAnimationFrame((()=>{90==r||270==r?(Rt(this,Tt,"f").width=Rt(this,Mt,"f").display_resolution.height,Rt(this,Tt,"f").height=Rt(this,Mt,"f").display_resolution.width):(Rt(this,Tt,"f").width=Rt(this,Mt,"f").display_resolution.width,Rt(this,Tt,"f").height=Rt(this,Mt,"f").display_resolution.height);const e=Rt(this,Tt,"f").getContext("2d");if(vt&&e.clearRect(0,0,Rt(this,Tt,"f").width,Rt(this,Tt,"f").height),!e)throw new Error("failed to get canvas context");if(0==r)e.drawImage(i,0,0,i.displayWidth,i.displayHeight),e.restore();else if(180==r)e.save(),e.translate(Rt(this,Tt,"f").width/2,Rt(this,Tt,"f").height/2),e.rotate(Math.PI),e.drawImage(i,-i.displayWidth/2,-i.displayHeight/2,i.displayWidth,i.displayHeight),e.restore();else if(90==r||270==r){const t=90==r?270:90;e.save(),e.translate(Rt(this,Tt,"f").width/2,Rt(this,Tt,"f").height/2),e.rotate(t*Math.PI/180),e.drawImage(i,-i.displayWidth/2,-i.displayHeight/2,i.displayWidth,i.displayHeight),e.restore()}i.close(),t=!1}))])}},new((i=void 0)||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}));var e,t,i,s},Ct=function(e){Dt(this,At,new VideoDecoder({output:t=>{e.enqueue(t)},error:console.error}),"f"),Rt(this,At,"f").configure({codec:Rt(this,Mt,"f").codec,codedHeight:Rt(this,Mt,"f").resolution.height,codedWidth:Rt(this,Mt,"f").resolution.width,optimizeForLatency:!0})},kt=function(e){const t=new EncodedVideoChunk({type:e.type,data:e.data,timestamp:e.timestamp});Rt(this,St,"f")!=90*e.rotation&&Dt(this,St,90*e.rotation,"f"),Rt(this,At,"f").decode(t)};var Pt,zt,Bt,Wt,xt,Lt,jt,Gt,Vt,Ut,Yt=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Ht=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class qt{constructor(e,t,i){Pt.add(this),zt.set(this,void 0),Bt.set(this,void 0),Wt.set(this,void 0),xt.set(this,void 0),Lt.set(this,void 0),jt.set(this,void 0),Yt(this,zt,e,"f"),Yt(this,Bt,t,"f"),Yt(this,Wt,i,"f"),Yt(this,Lt,new TransformStream({start:Ht(this,Pt,"m",Vt).bind(this),transform:Ht(this,Pt,"m",Ut).bind(this)}),"f"),Ht(this,Pt,"m",Gt).call(this).catch((e=>console.error("failed to run video renderer soft: ",e)))}close(){}}zt=new WeakMap,Bt=new WeakMap,Wt=new WeakMap,xt=new WeakMap,Lt=new WeakMap,jt=new WeakMap,Pt=new WeakSet,Gt=function(){return e=this,t=void 0,s=function*(){const e=Ht(this,Wt,"f").frames.pipeThrough(Ht(this,Lt,"f")).getReader();for(;;)try{const{value:t,done:i}=yield e.read();if(i)break;const s=Ht(this,jt,"f");self.requestAnimationFrame((()=>{90==s||270==s?(Ht(this,Bt,"f").width=t.displayHeight,Ht(this,Bt,"f").height=t.displayWidth):(Ht(this,Bt,"f").width=t.displayWidth,Ht(this,Bt,"f").height=t.displayHeight);const e=Ht(this,Bt,"f").getContext("2d");if(!e)throw new Error("failed to get canvas context");je(e,t,0,0,t.displayWidth,t.displayHeight,s),t.close()}))}catch(e){console.error(e)}},new((i=void 0)||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}));var e,t,i,s},Vt=function(e){Yt(this,xt,new Le({output:t=>{e.enqueue(t)},error:console.error}),"f"),Ht(this,xt,"f").configure({codec:Ht(this,zt,"f").codec,codedHeight:Ht(this,zt,"f").resolution.height,codedWidth:Ht(this,zt,"f").resolution.width,optimizeForLatency:!0})},Ut=function(e){const t=e.data.buffer,i=new Ge({type:e.type,data:e.data,timestamp:1e3*Date.now(),transfer:[t]});Ht(this,jt,"f")!=90*e.rotation&&Yt(this,jt,90*e.rotation,"f"),Ht(this,xt,"f").decode(i)};var Kt,Xt,Jt,Qt,Zt,$t=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))},ei=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},ti=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class ii{constructor(){this.audio=new si,this.video=new si}}class si{constructor(){Kt.add(this),Xt.set(this,void 0),Jt.set(this,void 0),this.frames=new ReadableStream({pull:ei(this,Kt,"m",Qt).bind(this),cancel:ei(this,Kt,"m",Zt).bind(this)}),ti(this,Jt,new TransformStream({},{highWaterMark:100}),"f")}get segments(){return ei(this,Jt,"f").writable}}function ri(e){return void 0!==e.frames}Xt=new WeakMap,Jt=new WeakMap,Kt=new WeakSet,Qt=function(e){return $t(this,void 0,void 0,(function*(){for(;;){const t=ei(this,Jt,"f").readable.getReader();let i;if(ei(this,Xt,"f")){const e=ei(this,Xt,"f").frames.getReader();vt?(i=yield e.read(),e.releaseLock()):(i=yield Promise.race([e.read(),t.read()]),e.releaseLock())}else i=yield t.read();t.releaseLock();const{value:s,done:r}=i;if(r)ti(this,Xt,void 0,"f");else{if(!ri(s))return void e.enqueue(s);if(ei(this,Xt,"f")){if(s.sequence<ei(this,Xt,"f").sequence){yield s.frames.cancel("skipping segment; too old");continue}yield ei(this,Xt,"f").frames.cancel("skipping segment; too slow")}ti(this,Xt,s,"f")}}}))},Zt=function(e){return $t(this,void 0,void 0,(function*(){ei(this,Xt,"f")&&(yield ei(this,Xt,"f").frames.cancel(e));const t=ei(this,Jt,"f").readable.getReader();for(;;){const{value:i,done:s}=yield t.read();if(s)break;yield i.frames.cancel(e)}}))};var ni,oi,ai,li,ci,hi,pi,ui,di,fi=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},yi=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class mi{constructor(e,t){ni.add(this),oi.set(this,void 0),ai.set(this,void 0),li.set(this,void 0),ci.set(this,void 0),hi.set(this,void 0),pi.set(this,!1),ui.set(this,void 0),fi(this,hi,e,"f"),t&&fi(this,pi,!0,"f")}playAudio(){yi(this,ui,"f")&&(yi(this,ui,"f").force_wasm?(console.log("use soft audio render"),fi(this,ai,new ot(yi(this,ui,"f"),yi(this,oi,"f").audio),"f")):fi(this,ai,new J(yi(this,ui,"f"),yi(this,oi,"f").audio),"f")),yi(this,ai,"f")&&yi(this,ai,"f").play()}runVideoAudio(e,t){yi(this,li,"f")&&yi(this,li,"f").close(),yi(this,ai,"f")&&yi(this,ai,"f").close(),fi(this,ci,t,"f"),fi(this,oi,new ii,"f");const i=[],s=e.audio;fi(this,ui,{codec:s.codec,channel_count:s.channels,sample_rate:s.sample_rate},"f"),2==yi(this,ui,"f").channel_count&&48e3==yi(this,ui,"f").sample_rate&&"opus"==yi(this,ui,"f").codec||(console.error("not support audio"),fi(this,ui,void 0,"f")),!yi(this,ui,"f")||!yi(this,pi,"f")&&ht()||(yi(this,ui,"f").force_wasm=!0);const r=e.video;let n={codec:r.codec,frame_rate:r.fps,resolution:{width:r.size.width,height:r.size.height},display_resolution:{width:r.original_size.width,height:r.original_size.height}};!yi(this,pi,"f")&&ct()||(n.force_wasm=!0),n&&(n.force_wasm?(console.log("use soft video render"),fi(this,li,new qt(n,yi(this,hi,"f"),yi(this,oi,"f").video),"f")):(console.log("use hw video render"),fi(this,li,new Nt(n,yi(this,hi,"f"),yi(this,oi,"f").video),"f"))),i.push(yi(this,ni,"m",di).call(this)),Promise.race(i)}close(){var e,t;null===(e=yi(this,ai,"f"))||void 0===e||e.close(),null===(t=yi(this,li,"f"))||void 0===t||t.close()}}oi=new WeakMap,ai=new WeakMap,li=new WeakMap,ci=new WeakMap,hi=new WeakMap,pi=new WeakMap,ui=new WeakMap,ni=new WeakSet,di=function(){return e=this,t=void 0,s=function*(){const e=yi(this,oi,"f").video,t=yi(this,oi,"f").audio,i=yi(this,ci,"f").getReader(),s=new TransformStream({}),r=s.writable.getWriter(),n=e.segments.getWriter();yield n.write({sequence:0,frames:s.readable}),n.releaseLock();const o=new TransformStream({}),a=o.writable.getWriter(),l=t.segments.getWriter();yield l.write({sequence:0,frames:o.readable}),l.releaseLock();let c=Date.now(),h=0;for(;;){const{done:e,value:t}=yield i.read();if(e)break;t.video?(c=Date.now(),h=t.timestamp,yield r.write(t)):yield a.write(t)}yield a.close(),yield r.close()},new((i=void 0)||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}));var e,t,i,s};var gi,_i,wi,Ei,vi=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))},bi=function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Mi=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(e){e[e.kVQ360p=0]="kVQ360p",e[e.kVQ480p=1]="kVQ480p",e[e.kVQ720p=2]="kVQ720p",e[e.kVQ1080p=3]="kVQ1080p"}(Ei||(Ei={}));const Ti=new Map([[Ei.kVQ360p,1e3],[Ei.kVQ480p,1750],[Ei.kVQ720p,2500],[Ei.kVQ1080p,4e3]]);var Ii;!function(e){e[e.kH264=0]="kH264",e[e.kH265=1]="kH265"}(Ii||(Ii={}));class Ai{}class Oi{constructor(e){gi.set(this,void 0),_i.set(this,void 0),wi.set(this,void 0),bi(this,wi,e,"f"),bi(this,_i,new ReadableStream({start:e=>{bi(this,gi,e,"f")}}),"f")}enqueue(e){Mi(this,gi,"f").enqueue(e)}close(){Mi(this,gi,"f").close()}getReader(){return Mi(this,_i,"f")}}gi=new WeakMap,_i=new WeakMap,wi=new WeakMap,v.prototype.GetMediaStats=function(){return this.LDMediaStats},v.prototype.SetOnRotation=function(){null==this.notificationHandles.get(I.DeviceNotificationType.DEVICE_NOTIFICATION_ORIENTATION)&&this.notificationHandles.set(I.DeviceNotificationType.DEVICE_NOTIFICATION_ORIENTATION,(e=>{const t=I.RotationChange.deserializeBinary(e.payload);this.ReceiveRotation(90*t.rotation)}))},v.prototype.ReceiveRotation=function(e){this.rotation!=e&&(this.connectInfo.onMediaRotation&&this.connectInfo.onMediaRotation(e),this.rotation=e)},v.prototype.ReceiveMediaSize=function(e,t){!this.connectInfo.onMediaSize||this.last_media_width==e&&this.last_media_height==t||this.connectInfo.onMediaSize(e,t),this.last_media_width=e,this.last_media_height=t},v.prototype.ConnectMediaWebrtc=function(t){return vi(this,void 0,void 0,(function*(){return null!=this.video?_.Error(p.ErrorParams,"video is not null"):(this.SetOnRotation(),this.connectInfo=t,this.started=!1,this.connectInfo.webrtcHost,null==this.connectInfo.quality&&(this.connectInfo.quality=Ei.kVQ720p),null==this.responseHandlers.get(a.MediaType.MEDIA_TYPE_CONNECT)&&this.responseHandlers.set(a.MediaType.MEDIA_TYPE_CONNECT,(t=>{var i,s;const r=e.CommonRes.deserializeBinary(t.payload),n=(new TextDecoder).decode(r.result);0==r.code?(this.LogWebrtc("LDDeviceMedia.ts:220 answer "+n),this.peerConnection.setRemoteDescription(new RTCSessionDescription({sdp:n,type:"answer"})),null===(i=this.GetWaitForResponse(t.sub_type,t.seq))||void 0===i||i.resolve(_.Success)):null===(s=this.GetWaitForResponse(t.sub_type,t.seq))||void 0===s||s.reject(r.result)})),null==this.notificationHandles.get(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_ICECANDIDATE)&&this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_ICECANDIDATE,(e=>{let t=a.WebrtcCandidiate.deserializeBinary(e.payload).candidate;null!=this.webrtcHost&&(t=function(e,t){const i=e.split(" ");if("host"===i[7]){const s=i[4];return e.replace(s,t)}return e}(t,this.webrtcHost)),this.LogWebrtc("LDDeviceMedia.ts:256 candidate "+t),this.peerConnection.addIceCandidate(new RTCIceCandidate({candidate:t,sdpMid:"0",sdpMLineIndex:0}))})),this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_STREAM_INFO,(e=>{const t=a.StreamInfo.deserializeBinary(e.payload);this.streamInfo=t})),this.videoContainer=t.video_container,null==this.videoContainer?Promise.reject(_.Error(p.ErrorParams,"videoContainer is undefined")):null!==this.videoContainer.querySelector("video")?Promise.reject(_.Error(p.ErrorParams,"videoContainer has video tag")):(this.video||(this.video=document.createElement("video"),this.video.autoplay=!0,this.video.muted=!0,this.video.playsInline=!0,this.video.webkitPlaysInline=!0,this.video.disablePictureInPicture=!0,this.video.style.objectFit="fill",this.video.style.width="100%",this.video.style.height="100%",this.video.setAttribute("x5-playsinline","true"),this.video.setAttribute("webkit-playsinline","true"),this.video.setAttribute("webkit-disable-remote-playback",""),this.video.setAttribute("x-webkit-airplay","false"),this.video.setAttribute("x5-video-player-type","h5"),this.video.setAttribute("x5-video-player-fullscreen","true"),this.videoContainer.appendChild(this.video)),new Promise(((i,s)=>{function r(e,t=4){return(100*e).toFixed(t)+"%"}if(t.quality||(t.quality=Ei.kVQ720p),this.video.addEventListener("loadedmetadata",(()=>{this.video.videoWidth<10?(console.log("demo 首帧加载失败"),s(_.Error(p.ErrorMedia,"首帧加载失败"))):this.connectInfo.onMediaFirstFrame&&this.connectInfo.onMediaFirstFrame()})),this.video.addEventListener("resize",(()=>{let e=this.video.videoWidth>this.video.videoHeight,t=this.streamInfo.video.original_size.width,i=this.streamInfo.video.original_size.height;if(this.streamInfo.video.original_size.width!=this.streamInfo.video.size.width||this.streamInfo.video.original_size.height!=this.streamInfo.video.size.height){let e=this.rotation;if(this.connectInfo.auto_rotate||(e=h.ROTATION_0),e==h.ROTATION_90){this.video.style.width=r(this.video.videoWidth/i),this.video.style.height=r(this.video.videoHeight/t);const e=r((this.video.videoHeight-t)/t),s=r(this.video.videoHeight/t);this.video.style.clipPath="polygon(0 "+e+", 100% "+e+", 100% "+s+", 0 "+s+")",this.video.style.transform="translateY(-"+e+")"}else if(e==h.ROTATION_270){this.video.style.width=r(this.video.videoWidth/i),this.video.style.height=r(this.video.videoHeight/t);const e=0,s=r(this.media_width/this.video.videoHeight);this.video.style.clipPath="polygon(0 "+e+", 100% "+e+", 100% "+s+", 0 "+s+")"}else if(this.video.style.width=r(this.video.videoWidth/t),this.video.style.height=r(this.video.videoHeight/i),console.log(e),e==h.ROTATION_180){const e=r((this.video.videoWidth-this.media_width)/this.media_width),t=r(this.video.videoWidth/this.media_width);this.video.style.clipPath="polygon("+e+" 0 , "+t+" 0, "+t+" 100%, "+e+" 100% )",this.video.style.transform="translateX(-"+e+")"}else this.video.style.width="100%",this.video.style.height="100%",this.video.style.clipPath=""}else this.video.style.width="100%",this.video.style.height="100%",this.video.style.clipPath="";this.connectInfo.onMediaSize&&this.ReceiveMediaSize(e?i:t,e?t:i)})),function(){const e=navigator.userAgent.match(/Firefox\/([0-9.]+)/);return e&&e[1]}()&&(n=this.connectInfo.webrtcHost,!/^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(n)&&!/^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/.test(n)))return void s(_.Error(p.ErrorParams,"when firefox webrtcHost must be ip"));var n;if(!this.ws||this.ws.readyState!==WebSocket.OPEN)return void s(_.Error(p.NotConnected,"connected error"));this.LDMediaStats=new Ai,this.LDMediaStats.connected=!1,this.peerConnection=new RTCPeerConnection({iceServers:[]}),this.peerConnection.addTransceiver("video",{direction:"recvonly"}),this.peerConnection.addTransceiver("audio",{direction:"recvonly"}),this.dataChannel=this.peerConnection.createDataChannel("MessageChannel",{ordered:!0}),this.dataChannel.onmessage=e=>{this.OnPbCommonMessage(e.data)},this.peerConnection.addEventListener("iceconnectionstatechange",(e=>{var i,s;if("connected"===(null===(i=this.peerConnection)||void 0===i?void 0:i.iceConnectionState)){this.LogWebrtc("LDDeviceMedia.ts:407 webrtc connected"),this.LDMediaStats.connected=!0,setTimeout((()=>{this.peerConnection&&this.peerConnection.getStats().then((e=>{e.forEach((e=>{"inbound-rtp"==e.type&&"video"==e.kind&&0==e.framesDecoded&&this.LogWebrtc("409 No video data received")}))}))}),2e3);let e=0,t=0;this.LDMediaStatsTimer=setInterval((()=>{this.peerConnection&&(this.peerConnection.getStats().then((i=>{i.forEach((i=>{if("inbound-rtp"==i.type&&"video"==i.kind){this.LDMediaStats.jitter=i.jitter,this.LDMediaStats.lost=i.packetsLost/i.packetsReceived,this.LDMediaStats.bytesReceived=i.bytesReceived,this.LDMediaStats.jitterBufferDelay=i.jitterBufferDelay,this.LDMediaStats.jitterBufferTargetDelay=i.jitterBufferTargetDelay;const s=i.timestamp,r=i.bytesReceived;if(e&&t){const i=8*(r-t)/((s-e)/1e3);this.LDMediaStats.bitrate=i}e=s,t=r,this.LDMediaStats.framesDecoded=i.framesDecoded,this.LDMediaStats.freezeCount=i.freezeCount}else"candidate-pair"==i.type&&(this.LDMediaStats.rtt=1e3*i.currentRoundTripTime)}))})),this.LogMediaStatus(),this.uploadLDMediaStats||(this.uploadLDMediaStats=[]),this.uploadLDMediaStats=this.uploadLDMediaStats.concat(this.LDMediaStats),this.uploadLDMediaStats.length>30&&(this.LogReport(JSON.stringify(this.uploadLDMediaStats)),this.uploadLDMediaStats=[]))}),2e3)}else"disconnected"===(null===(s=this.peerConnection)||void 0===s?void 0:s.iceConnectionState)&&(this.peerConnection.close(),clearInterval(this.LDMediaStatsTimer),t.onMediaClose&&t.onMediaClose(_.Error(p.NotConnected,"webrtc connect is close")))})),this.peerConnection.ontrack=e=>{if(this.LogWebrtc("LDDeviceMedia.ts:485  webrtc video track"),e.streams[0]&&this.mediaStream!=e.streams[0]){this.mediaStream=e.streams[0],this.video.srcObject=this.mediaStream,this.video.controls=!1;try{this.video.play()}catch(e){e instanceof DOMException&&"NotAllowedError"===e.name?console.error("由于自动播放策略限制，无法自动播放，请用户交互后重试"):console.error("播放视频时出错:",e)}}};const o=a.MediaType.MEDIA_TYPE_CONNECT;this.quality=t.quality,this.peerConnection.createOffer({iceRestart:!0,offerToReceiveAudio:!0,offerToReceiveVideo:!0}).then((t=>vi(this,void 0,void 0,(function*(){var r;if(!this.peerConnection)return void s("Peer connection is null");const n=new a.ConnectInfo;n.media_info=new a.MediaInfo,n.media_info.video_type=a.VideoType.H264,n.media_info.bitrate=Ti.get(this.quality),n.media_info.audio_type=a.AudioType.Opus,n.media_info.fps=30,n.media_info.capture_size=new a.Size,n.media_info.capture_size.width=this.quality==Ei.kVQ1080p?1080:720,n.media_info.capture_size.height=this.quality==Ei.kVQ1080p?1920:1280,n.media_info.client_size=n.media_info.capture_size,n.media_info.video_support.push(at.h264BaseLine),this.connectInfo.width&&this.connectInfo.height&&(n.media_info.capture_size.width=this.connectInfo.width,n.media_info.capture_size.height=this.connectInfo.height),this.connectInfo.fps&&(n.media_info.fps=this.connectInfo.fps),this.connectInfo.bitrate&&(n.media_info.bitrate=this.connectInfo.bitrate),n.webrtc_info=new a.WebrtcInfo,n.webrtc_info.sdp=t.sdp,n.webrtc_info.auto_rotate=this.connectInfo.auto_rotate,this.LogWebrtc("LDDeviceMedia.ts:534 webrtc offer: "+t.sdp);const l=this.GetSeq(),c=new e.CommonMessage({name:"join",seq:l,type:e.CommonMessage.Type.Request,sub_type:o,payload:n.serializeBinary()});yield this.peerConnection.setLocalDescription(t),null===(r=this.ws)||void 0===r||r.send(c.serializeBinary()),this.AddWaitForResponse(o,l,i,s),setTimeout((()=>{var e;null===(e=this.GetWaitForResponse(o,l))||void 0===e||e.reject(_.Timeout)}),5e3)})))).catch((e=>{s(e)}))}))))}))},v.prototype.DisconnectMedia=function(){return 0!=this.started?Promise.resolve(_.Success):(this.CloseMedia(),new Promise(((t,i)=>{var s;if(!this.hasLogined())return i(_.NotLogin);const r=a.MediaType.MEDIA_TYPE_DISCONNECT,n=this.GetSeq(),o=new e.CommonMessage({name:"disconnect_media",type:e.CommonMessage.Type.Request,seq:n,sub_type:r});null===(s=this.ws)||void 0===s||s.send(o.serializeBinary())})))},v.prototype.SwitchVideoQuality=function(t){const i=new a.SwitchVideoQuality;return i.bitrate=Ti.get(t),this.LogMedia("SwitchVideoQuality "+Ti.get(t)),new Promise(((t,s)=>{if(!this.hasLogined())return s(_.NotLogin);const r=new e.CommonMessage({name:"switch_video_quality",type:e.CommonMessage.Type.Request,sub_type:a.MediaType.MEDIA_TYPE_SWITCH_VIDEO_QUALITY,payload:i.serializeBinary()});this.SendCommonMessage(r.serializeBinary()),t(_.Success)}))},v.prototype.ForceIDR=function(){return new Promise(((t,i)=>{if(!this.hasLogined())return i(_.NotLogin);const s=new e.CommonMessage({name:"force_idr",type:e.CommonMessage.Type.Request,sub_type:a.MediaType.MEDIA_TYPE_IDR});this.SendCommonMessage(s.serializeBinary()),t(_.Success)}))},v.prototype.ConnectMediaNative=function(t){return vi(this,void 0,void 0,(function*(){return wt(!0===t.force_wasm)?(this.started=!1,this.mediaNativeTimer=void 0,this.connectInfo=t,this.muted=!0,this.responseHandlers.set(a.MediaType.MEDIA_TYPE_CONNECT,(t=>{var i,s;const r=e.CommonRes.deserializeBinary(t.payload);0==r.code?(this.LogWebrtc("LDDeviceMedia.ts:611 connected media!"),null===(i=this.GetWaitForResponse(t.sub_type,t.seq))||void 0===i||i.resolve(_.Success)):null===(s=this.GetWaitForResponse(t.sub_type,t.seq))||void 0===s||s.reject(_.Error(r.code,new TextDecoder("utf-8").decode(r.result)))})),this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_STREAM_INFO,(e=>{const t=a.StreamInfo.deserializeBinary(e.payload);this.LogMedia(JSON.stringify(t.toObject())),this.frameReader=new Oi(this),this.mediaNative.runVideoAudio(t,this.frameReader.getReader()),this.streamInfo=t})),this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_VIDEO_STREAM,((t,i)=>{const s=a.StreamVideoHead.deserializeBinary(t.payload);if(this.lastVideoInfo=s,null!=this.connectInfo){if(90*s.rotation!=this.rotation||null==this.last_media_width){let e=this.connectInfo.auto_rotate&&(90*s.rotation==h.ROTATION_90||90*s.rotation==h.ROTATION_270),t=e?this.streamInfo.video.original_size.height:this.streamInfo.video.original_size.width,i=e?this.streamInfo.video.original_size.width:this.streamInfo.video.original_size.height;this.ReceiveMediaSize(t,i)}this.ReceiveRotation(90*s.rotation),this.gotFirstVideo||(this.LogMedia(JSON.stringify(s.toObject())),this.gotFirstVideo=!0,this.connectInfo.onMediaFirstFrame&&this.connectInfo.onMediaFirstFrame()),void 0===this.mediaNativeTimer&&(this.mediaNativeTimer=setInterval((()=>{var t;let i=new a.StreamVideoReport;i.timestamp=Date.now(),i.video_timestamp=this.lastVideoInfo.timestamp;const s=a.MediaType.MEDIA_TYPE_VIDEO_STREAM_REPORT,r=new e.CommonMessage({name:"media_report",type:e.CommonMessage.Type.Request,sub_type:s,payload:i.serializeBinary()});null===(t=this.ws)||void 0===t||t.send(r.serializeBinary())}),300))}})),this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_STREAM_REPORT,((e,t)=>{let i=a.StreamVideoReportNotify.deserialize(e.payload),s=Date.now()-i.report.timestamp,r=(i.video_timestamp-i.report.video_timestamp)/1e3;this.LDMediaStats&&this.LDMediaStats.rtt?(this.LDMediaStats.rtt=Math.round(.8*this.LDMediaStats.rtt+.2*s),this.LDMediaStats.jitter=Math.round(.8*this.LDMediaStats.jitter+.2*r)):(this.LDMediaStats=new Ai,this.LDMediaStats.connected=!0,this.LDMediaStats.rtt=s,this.LDMediaStats.jitter=r),(null==this.lastReportNativeStatsTime||Date.now()-this.lastReportNativeStatsTime>1e3)&&(this.lastReportNativeStatsTime=Date.now(),this.LogMediaStatus(JSON.stringify(this.LDMediaStats)),this.uploadLDMediaStats||(this.uploadLDMediaStats=[]),this.uploadLDMediaStats=this.uploadLDMediaStats.concat(this.LDMediaStats),this.uploadLDMediaStats.length>30&&(this.LogReport(JSON.stringify(this.uploadLDMediaStats)),this.uploadLDMediaStats=[]))})),this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_AUDIO_STREAM,((e,t)=>{const i=a.StreamAudioHead.deserializeBinary(e.payload);this.lastAudioInfo=i,this.gotFirstAudio||(this.LogMedia(JSON.stringify(i.toObject())),this.gotFirstAudio=!0)})),this.meidaHandle=(e,t)=>{if(null!=this.connectInfo)switch(e){case a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_VIDEO_STREAM:if(null==this.lastVideoInfo)return;let e=new O(this.lastVideoInfo.idr?"key":"delta",this.lastVideoInfo.timestamp,t);e.rotation=this.connectInfo.auto_rotate?this.lastVideoInfo.rotation:0,e.video=!0,this.frameReader&&this.frameReader.enqueue(e);break;case a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_AUDIO_STREAM:if(null==this.lastAudioInfo)return;let i=new O("delta",this.lastAudioInfo.timestamp,t);!this.muted&&this.frameReader&&this.frameReader.enqueue(i)}},new Promise(((i,s)=>{var r,n;if(this.videoContainer=this.connectInfo.video_container,null==this.videoContainer)return void s(_.Error(p.ErrorParams,"videoContainer is undefined"));if(null!==this.videoContainer.querySelector("canvas"))return void s(_.Error(p.ErrorParams,"videoContainer has video tag"));this.canvas||(this.canvas=document.createElement("canvas"),this.canvas.autoplay=!0,this.canvas.style.objectFit="fill",this.canvas.style.width="100%",this.canvas.style.height="100%",this.videoContainer.appendChild(this.canvas)),null==t.quality&&(t.quality=Ei.kVQ720p),this.mediaNative=new mi(this.canvas,t.force_wasm);const o=new a.ConnectInfo;o.media_info=new a.MediaInfo,o.media_info.video_type=a.VideoType.H264,o.media_info.audio_type=a.AudioType.Opus,o.media_info.fps=30,o.media_info.capture_size=new a.Size,o.media_info.bitrate=Ti.get(t.quality),o.media_info.capture_size.width=this.quality==Ei.kVQ1080p?1080:720,o.media_info.capture_size.height=this.quality==Ei.kVQ1080p?1920:1280,t.width&&t.height&&(o.media_info.capture_size.width=t.width,o.media_info.capture_size.height=t.height),o.media_info.client_size=o.media_info.capture_size,yt().forEach((e=>{o.media_info.video_support.push(e)})),this.connectInfo.fps&&(o.media_info.fps=this.connectInfo.fps),this.connectInfo.bitrate&&(o.media_info.bitrate=this.connectInfo.bitrate);const l=1==t.force_wasm?Et()>0?Et():20:ct(!0)?60:Et();console.log("limit fps "+l),l<o.media_info.fps&&(o.media_info.fps=l),console.log("connectInfo: "+JSON.stringify(o.media_info.toObject())),null===(r=yt())||void 0===r||r.forEach((e=>{o.media_info.video_support.push(e)}));const c=a.MediaType.MEDIA_TYPE_CONNECT,h=this.GetSeq(),u=new e.CommonMessage({name:"join",type:e.CommonMessage.Type.Request,sub_type:c,seq:h,payload:o.serializeBinary()});null===(n=this.ws)||void 0===n||n.send(u.serializeBinary()),this.AddWaitForResponse(c,h,i,s),setTimeout((()=>{var e;null===(e=this.GetWaitForResponse(c,h))||void 0===e||e.resolve(_.Timeout)}),5e3)}))):(console.log("Native Not Enable, use webrtc"),this.ConnectMediaWebrtc(t))}))},v.prototype.SetMute=function(t){var i;return this.muted=t,this.video&&(this.video.muted=t),t||null===(i=this.mediaNative)||void 0===i||i.playAudio(),new Promise(((i,s)=>{if(!this.hasLogined())return s(_.NotLogin);const r=new a.StreamMute({mute:t}),n=new e.CommonMessage({name:"force_idr",type:e.CommonMessage.Type.Request,sub_type:a.MediaType.MEDIA_TYPE_MUTE,payload:r.serializeBinary()});this.SendCommonMessage(n.serializeBinary()),i(_.Success)}))},v.prototype.CloseMedia=function(){if(this.gotFirstVideo=!1,this.connectInfo=void 0,this.frameReader=void 0,this.mediaNative&&(this.mediaNative.close(),this.mediaNative=void 0),this.notificationHandles.set(a.MediaNotificationType.MEDIA_NOTIFICATION_TYPE_ICECANDIDATE,void 0),this.responseHandlers.set(a.MediaType.MEDIA_TYPE_CONNECT,void 0),this.mediaNativeTimer&&(clearInterval(this.mediaNativeTimer),this.mediaNativeTimer=void 0),this.peerConnection&&this.peerConnection.close(),this.videoContainer){try{this.videoContainer&&this.canvas&&this.videoContainer.contains(this.canvas)&&this.videoContainer.removeChild(this.canvas),this.videoContainer&&this.video&&this.videoContainer.contains(this.video)&&this.videoContainer.removeChild(this.video)}catch(e){console.log(e)}this.canvas=void 0,this.video=void 0}};var Si,Fi,Ci,ki,Di,Ri,Ni,Pi,zi=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(e){var i,s,r,n,o,a,l,c;let h,p,u,d,f,y,m;!function(e){e[e.NONE=0]="NONE",e[e.INPUT_TYPE_MIN=1e4]="INPUT_TYPE_MIN",e[e.INPUT_TYPE_TOUCH=10001]="INPUT_TYPE_TOUCH",e[e.INPUT_TYPE_TOUCH_MOVE=10002]="INPUT_TYPE_TOUCH_MOVE",e[e.INPUT_TYPE_FUNCTION_KEY=10003]="INPUT_TYPE_FUNCTION_KEY",e[e.INPUT_TYPE_TEXT=10004]="INPUT_TYPE_TEXT",e[e.INPUT_TYPE_CLIP_DATA=10005]="INPUT_TYPE_CLIP_DATA",e[e.INPUT_TYPE_SWITCH_IME=10007]="INPUT_TYPE_SWITCH_IME",e[e.INPUT_TYPE_MAX=2e4]="INPUT_TYPE_MAX"}(h=e.InputType||(e.InputType={})),function(e){e[e.INPUT_NOTIFICATION_TYPE_MIN=0]="INPUT_NOTIFICATION_TYPE_MIN",e[e.INPUT_NOTIFICATION_TYPE_CLIP_DATA=10001]="INPUT_NOTIFICATION_TYPE_CLIP_DATA",e[e.INPUT_NOTIFICATION_IME_STATUS=10002]="INPUT_NOTIFICATION_IME_STATUS",e[e.INPUT_NOTIFICATION_TYPE_MAX=2e4]="INPUT_NOTIFICATION_TYPE_MAX"}(p=e.InputNotificationType||(e.InputNotificationType={})),function(e){e[e.INPUT_KEY_CODE_OLD_DEF=0]="INPUT_KEY_CODE_OLD_DEF",e[e.INPUT_KEY_CODE_BACK=1]="INPUT_KEY_CODE_BACK",e[e.INPUT_KEY_CODE_HOME=2]="INPUT_KEY_CODE_HOME",e[e.INPUT_KEY_CODE_APPSELECT=3]="INPUT_KEY_CODE_APPSELECT",e[e.INPUT_KEY_CODE_VOLUMEDOWN=4]="INPUT_KEY_CODE_VOLUMEDOWN",e[e.INPUT_KEY_CODE_VOLUMEUP=5]="INPUT_KEY_CODE_VOLUMEUP",e[e.INPUT_KEY_CODE_POWER=6]="INPUT_KEY_CODE_POWER",e[e.INPUT_KEY_CODE_SILENCE=7]="INPUT_KEY_CODE_SILENCE",e[e.INPUT_KEY_CODE_UP=8]="INPUT_KEY_CODE_UP",e[e.INPUT_KEY_CODE_DOWN=9]="INPUT_KEY_CODE_DOWN",e[e.INPUT_KEY_CODE_LEFT=10]="INPUT_KEY_CODE_LEFT",e[e.INPUT_KEY_CODE_RIGHT=11]="INPUT_KEY_CODE_RIGHT",e[e.INPUT_KEY_CODE_BACKSPACE=12]="INPUT_KEY_CODE_BACKSPACE",e[e.INPUT_KEY_CODE_ENTER=13]="INPUT_KEY_CODE_ENTER",e[e.INPUT_KEY_CODE_DELETE=14]="INPUT_KEY_CODE_DELETE"}(u=e.InputKeyCode||(e.InputKeyCode={})),function(e){e[e.TOUCH_UP=0]="TOUCH_UP",e[e.TOUCH_DOWN=1]="TOUCH_DOWN",e[e.TOUCH_ALL=2]="TOUCH_ALL"}(d=e.Operation||(e.Operation={})),function(e){e[e.ADB=0]="ADB",e[e.Soft=1]="Soft",e[e.Customize=3]="Customize"}(f=e.IMEType||(e.IMEType={})),function(e){e[e.IMEInputTypeUnknown=0]="IMEInputTypeUnknown",e[e.IMEInputTypeNumber=1]="IMEInputTypeNumber",e[e.IMEInputTypeNumberSign=2]="IMEInputTypeNumberSign",e[e.IMEInputTypeNumberDecimal=3]="IMEInputTypeNumberDecimal",e[e.IMEInputTypePhone=4]="IMEInputTypePhone",e[e.IMEInputTypePassword=5]="IMEInputTypePassword",e[e.IMEInputTypeText=6]="IMEInputTypeText"}(y=e.IMEInputType||(e.IMEInputType={})),function(e){e[e.IMEActonUnknown=0]="IMEActonUnknown",e[e.IMEActionGo=1]="IMEActionGo",e[e.IMEActionDone=2]="IMEActionDone",e[e.IMEActionNext=3]="IMEActionNext",e[e.IMEActionPrevious=5]="IMEActionPrevious",e[e.IMEActionSearch=6]="IMEActionSearch",e[e.IMEActionNone=7]="IMEActionNone",e[e.IMEActionSend=8]="IMEActionSend"}(m=e.IMEAction||(e.IMEAction={}));class g extends t.Message{constructor(e){super(),i.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,i,"f")),Array.isArray(e)||"object"!=typeof e||("operation"in e&&null!=e.operation&&(this.operation=e.operation),"timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp))}get operation(){return t.Message.getFieldWithDefault(this,1,d.TOUCH_UP)}set operation(e){t.Message.setField(this,1,e)}get timestamp(){return t.Message.getFieldWithDefault(this,2,0)}set timestamp(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new g({});return null!=e.operation&&(t.operation=e.operation),null!=e.timestamp&&(t.timestamp=e.timestamp),t}toObject(){const e={};return null!=this.operation&&(e.operation=this.operation),null!=this.timestamp&&(e.timestamp=this.timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(this.operation!=d.TOUCH_UP&&i.writeEnum(1,this.operation),0!=this.timestamp&&i.writeSint64(2,this.timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new g;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.operation=i.readEnum();break;case 2:s.timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return g.deserialize(e)}}i=new WeakMap,e.TouchEvent=g;class _ extends t.Message{constructor(e){super(),s.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,s,"f")),Array.isArray(e)||"object"!=typeof e||("index"in e&&null!=e.index&&(this.index=e.index),"x"in e&&null!=e.x&&(this.x=e.x),"y"in e&&null!=e.y&&(this.y=e.y),"timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp),"sync"in e&&null!=e.sync&&(this.sync=e.sync))}get index(){return t.Message.getFieldWithDefault(this,1,0)}set index(e){t.Message.setField(this,1,e)}get x(){return t.Message.getFieldWithDefault(this,2,0)}set x(e){t.Message.setField(this,2,e)}get y(){return t.Message.getFieldWithDefault(this,3,0)}set y(e){t.Message.setField(this,3,e)}get timestamp(){return t.Message.getFieldWithDefault(this,4,0)}set timestamp(e){t.Message.setField(this,4,e)}get sync(){return t.Message.getFieldWithDefault(this,5,!1)}set sync(e){t.Message.setField(this,5,e)}static fromObject(e){const t=new _({});return null!=e.index&&(t.index=e.index),null!=e.x&&(t.x=e.x),null!=e.y&&(t.y=e.y),null!=e.timestamp&&(t.timestamp=e.timestamp),null!=e.sync&&(t.sync=e.sync),t}toObject(){const e={};return null!=this.index&&(e.index=this.index),null!=this.x&&(e.x=this.x),null!=this.y&&(e.y=this.y),null!=this.timestamp&&(e.timestamp=this.timestamp),null!=this.sync&&(e.sync=this.sync),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.index&&i.writeSint32(1,this.index),0!=this.x&&i.writeFloat(2,this.x),0!=this.y&&i.writeFloat(3,this.y),0!=this.timestamp&&i.writeSint64(4,this.timestamp),0!=this.sync&&i.writeBool(5,this.sync),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new _;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.index=i.readSint32();break;case 2:s.x=i.readFloat();break;case 3:s.y=i.readFloat();break;case 4:s.timestamp=i.readSint64();break;case 5:s.sync=i.readBool();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return _.deserialize(e)}}s=new WeakMap,e.MoveEvent=_;class w extends t.Message{constructor(e){super(),r.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[1],zi(this,r,"f")),Array.isArray(e)||"object"!=typeof e||("moves"in e&&null!=e.moves&&(this.moves=e.moves),"timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp))}get moves(){return t.Message.getRepeatedWrapperField(this,_,1)}set moves(e){t.Message.setRepeatedWrapperField(this,1,e)}get timestamp(){return t.Message.getFieldWithDefault(this,3,0)}set timestamp(e){t.Message.setField(this,3,e)}static fromObject(e){const t=new w({});return null!=e.moves&&(t.moves=e.moves.map((e=>_.fromObject(e)))),null!=e.timestamp&&(t.timestamp=e.timestamp),t}toObject(){const e={};return null!=this.moves&&(e.moves=this.moves.map((e=>e.toObject()))),null!=this.timestamp&&(e.timestamp=this.timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(this.moves.length&&i.writeRepeatedMessage(1,this.moves,(e=>e.serialize(i))),0!=this.timestamp&&i.writeSint64(3,this.timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new w;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:i.readMessage(s.moves,(()=>t.Message.addToRepeatedWrapperField(s,1,_.deserialize(i),_)));break;case 3:s.timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return w.deserialize(e)}}r=new WeakMap,e.MoveEventS=w;class E extends t.Message{constructor(e){super(),n.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,n,"f")),Array.isArray(e)||"object"!=typeof e||("operation"in e&&null!=e.operation&&(this.operation=e.operation),"code"in e&&null!=e.code&&(this.code=e.code),"sys_code"in e&&null!=e.sys_code&&(this.sys_code=e.sys_code),"timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp))}get operation(){return t.Message.getFieldWithDefault(this,1,d.TOUCH_UP)}set operation(e){t.Message.setField(this,1,e)}get code(){return t.Message.getFieldWithDefault(this,2,u.INPUT_KEY_CODE_OLD_DEF)}set code(e){t.Message.setField(this,2,e)}get sys_code(){return t.Message.getFieldWithDefault(this,3,0)}set sys_code(e){t.Message.setField(this,3,e)}get timestamp(){return t.Message.getFieldWithDefault(this,4,0)}set timestamp(e){t.Message.setField(this,4,e)}static fromObject(e){const t=new E({});return null!=e.operation&&(t.operation=e.operation),null!=e.code&&(t.code=e.code),null!=e.sys_code&&(t.sys_code=e.sys_code),null!=e.timestamp&&(t.timestamp=e.timestamp),t}toObject(){const e={};return null!=this.operation&&(e.operation=this.operation),null!=this.code&&(e.code=this.code),null!=this.sys_code&&(e.sys_code=this.sys_code),null!=this.timestamp&&(e.timestamp=this.timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(this.operation!=d.TOUCH_UP&&i.writeEnum(1,this.operation),this.code!=u.INPUT_KEY_CODE_OLD_DEF&&i.writeEnum(2,this.code),0!=this.sys_code&&i.writeSint32(3,this.sys_code),0!=this.timestamp&&i.writeSint64(4,this.timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new E;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.operation=i.readEnum();break;case 2:s.code=i.readEnum();break;case 3:s.sys_code=i.readSint32();break;case 4:s.timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return E.deserialize(e)}}n=new WeakMap,e.FunctionKey=E;class v extends t.Message{constructor(e){super(),o.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,o,"f")),Array.isArray(e)||"object"!=typeof e||("content"in e&&null!=e.content&&(this.content=e.content),"timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp))}get content(){return t.Message.getFieldWithDefault(this,1,"")}set content(e){t.Message.setField(this,1,e)}get timestamp(){return t.Message.getFieldWithDefault(this,2,0)}set timestamp(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new v({});return null!=e.content&&(t.content=e.content),null!=e.timestamp&&(t.timestamp=e.timestamp),t}toObject(){const e={};return null!=this.content&&(e.content=this.content),null!=this.timestamp&&(e.timestamp=this.timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(this.content.length&&i.writeString(1,this.content),0!=this.timestamp&&i.writeSint64(2,this.timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new v;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.content=i.readString();break;case 2:s.timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return v.deserialize(e)}}o=new WeakMap,e.TextInput=v;class b extends t.Message{constructor(e){super(),a.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,a,"f")),Array.isArray(e)||"object"!=typeof e||("content"in e&&null!=e.content&&(this.content=e.content),"timestamp"in e&&null!=e.timestamp&&(this.timestamp=e.timestamp))}get content(){return t.Message.getFieldWithDefault(this,1,"")}set content(e){t.Message.setField(this,1,e)}get timestamp(){return t.Message.getFieldWithDefault(this,2,0)}set timestamp(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new b({});return null!=e.content&&(t.content=e.content),null!=e.timestamp&&(t.timestamp=e.timestamp),t}toObject(){const e={};return null!=this.content&&(e.content=this.content),null!=this.timestamp&&(e.timestamp=this.timestamp),e}serialize(e){const i=e||new t.BinaryWriter;if(this.content.length&&i.writeString(1,this.content),0!=this.timestamp&&i.writeSint64(2,this.timestamp),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new b;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.content=i.readString();break;case 2:s.timestamp=i.readSint64();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return b.deserialize(e)}}a=new WeakMap,e.ClipData=b;class M extends t.Message{constructor(e){super(),l.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,l,"f")),Array.isArray(e)||"object"!=typeof e||("type"in e&&null!=e.type&&(this.type=e.type),"name"in e&&null!=e.name&&(this.name=e.name))}get type(){return t.Message.getFieldWithDefault(this,1,f.ADB)}set type(e){t.Message.setField(this,1,e)}get name(){return t.Message.getFieldWithDefault(this,2,"")}set name(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new M({});return null!=e.type&&(t.type=e.type),null!=e.name&&(t.name=e.name),t}toObject(){const e={};return null!=this.type&&(e.type=this.type),null!=this.name&&(e.name=this.name),e}serialize(e){const i=e||new t.BinaryWriter;if(this.type!=f.ADB&&i.writeEnum(1,this.type),this.name.length&&i.writeString(2,this.name),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new M;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.type=i.readEnum();break;case 2:s.name=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return M.deserialize(e)}}l=new WeakMap,e.IME=M;class T extends t.Message{constructor(e){super(),c.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],zi(this,c,"f")),Array.isArray(e)||"object"!=typeof e||("content"in e&&null!=e.content&&(this.content=e.content),"show_soft_input"in e&&null!=e.show_soft_input&&(this.show_soft_input=e.show_soft_input),"input_type"in e&&null!=e.input_type&&(this.input_type=e.input_type),"action"in e&&null!=e.action&&(this.action=e.action),"package"in e&&null!=e.package&&(this.package=e.package))}get content(){return t.Message.getFieldWithDefault(this,1,"")}set content(e){t.Message.setField(this,1,e)}get show_soft_input(){return t.Message.getFieldWithDefault(this,2,!1)}set show_soft_input(e){t.Message.setField(this,2,e)}get input_type(){return t.Message.getFieldWithDefault(this,3,y.IMEInputTypeUnknown)}set input_type(e){t.Message.setField(this,3,e)}get action(){return t.Message.getFieldWithDefault(this,4,m.IMEActonUnknown)}set action(e){t.Message.setField(this,4,e)}get package(){return t.Message.getFieldWithDefault(this,5,"")}set package(e){t.Message.setField(this,5,e)}static fromObject(e){const t=new T({});return null!=e.content&&(t.content=e.content),null!=e.show_soft_input&&(t.show_soft_input=e.show_soft_input),null!=e.input_type&&(t.input_type=e.input_type),null!=e.action&&(t.action=e.action),null!=e.package&&(t.package=e.package),t}toObject(){const e={};return null!=this.content&&(e.content=this.content),null!=this.show_soft_input&&(e.show_soft_input=this.show_soft_input),null!=this.input_type&&(e.input_type=this.input_type),null!=this.action&&(e.action=this.action),null!=this.package&&(e.package=this.package),e}serialize(e){const i=e||new t.BinaryWriter;if(this.content.length&&i.writeString(1,this.content),0!=this.show_soft_input&&i.writeBool(2,this.show_soft_input),this.input_type!=y.IMEInputTypeUnknown&&i.writeEnum(3,this.input_type),this.action!=m.IMEActonUnknown&&i.writeEnum(4,this.action),this.package.length&&i.writeString(5,this.package),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new T;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.content=i.readString();break;case 2:s.show_soft_input=i.readBool();break;case 3:s.input_type=i.readEnum();break;case 4:s.action=i.readEnum();break;case 5:s.package=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return T.deserialize(e)}}c=new WeakMap,e.IMEStatus=T}(Si||(Si={})),function(e){e[e.NONE=0]="NONE",e[e.KEY_CODE_BACK=1]="KEY_CODE_BACK",e[e.KEY_CODE_HOME=2]="KEY_CODE_HOME",e[e.KEY_CODE_APPSELECT=3]="KEY_CODE_APPSELECT",e[e.KEY_CODE_VOLUMEDOWN=4]="KEY_CODE_VOLUMEDOWN",e[e.KEY_CODE_VOLUMEUP=5]="KEY_CODE_VOLUMEUP",e[e.KEY_CODE_POWER=6]="KEY_CODE_POWER",e[e.KEY_CODE_SILENCE=7]="KEY_CODE_SILENCE",e[e.KEY_CODE_UP=8]="KEY_CODE_UP",e[e.KEY_CODE_DOWN=9]="KEY_CODE_DOWN",e[e.KEY_CODE_LEFT=10]="KEY_CODE_LEFT",e[e.KEY_CODE_RIGHT=11]="KEY_CODE_RIGHT",e[e.KEY_CODE_BACKSPACE=12]="KEY_CODE_BACKSPACE",e[e.KEY_CODE_ENTER=13]="KEY_CODE_ENTER",e[e.KEY_CODE_DELETE=14]="KEY_CODE_DELETE"}(Fi||(Fi={})),function(e){e[e.TOUCH_UP=0]="TOUCH_UP",e[e.TOUCH_DOWN=1]="TOUCH_DOWN",e[e.TOUCH_ALL=2]="TOUCH_ALL"}(Ci||(Ci={}));class Bi{}!function(e){e[e.ADB=0]="ADB",e[e.SOFT=1]="SOFT",e[e.Customize=3]="Customize"}(ki||(ki={}));class Wi{}v.prototype.Touch=function(t){if(!this.hasLogined())return _.NotLogin;t===Ci.TOUCH_UP&&(this.indexs.clear(),this.user_indexs.clear());const i=Si.InputType.INPUT_TYPE_TOUCH,s=new Si.TouchEvent;s.operation=t,s.timestamp=Date.now();const r=new e.CommonMessage({name:"touch",type:e.CommonMessage.Type.Request,sub_type:i,payload:s.serializeBinary()});return this.LogInput(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),_.Success},v.prototype.TouchMove=function(t){if(!this.hasLogined())return _.NotLogin;const i=Si.InputType.INPUT_TYPE_TOUCH_MOVE,s=new Si.MoveEventS;s.moves=t.map((e=>{let t=0;if(this.indexs.has(e.index))t=this.indexs.get(e.index);else for(t=0;t<10;t++)if(!this.user_indexs.has(t)){this.indexs.set(e.index,t),this.user_indexs.add(t);break}return new Si.MoveEvent({index:t,x:Math.min(Math.max(e.targetX/e.width,0),1),y:Math.min(Math.max(e.targetY/e.height,0),1),sync:!1})})),s.moves.length>0&&(s.moves[s.moves.length-1].sync=!0),s.timestamp=Date.now();const r=new e.CommonMessage({name:"touch",type:e.CommonMessage.Type.Request,sub_type:i,payload:s.serializeBinary()});return this.LogInput(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),_.Success},v.prototype.SendFunctionKey=function(t,i){if(!this.hasLogined())return _.NotLogin;const s=Si.InputType.INPUT_TYPE_FUNCTION_KEY,r=new Si.FunctionKey;r.code=t,r.timestamp=Date.now(),r.operation=i;var n=new e.CommonMessage({name:"function_key",seq:this.GetSeq(),type:e.CommonMessage.Type.Request,sub_type:s,payload:r.serializeBinary()});return this.LogInput(JSON.stringify(n.toObject())),this.SendCommonMessage(n.serializeBinary()),_.Success},v.prototype.SendText=function(t){if(!this.hasLogined())return _.NotLogin;const i=Si.InputType.INPUT_TYPE_TEXT;var s=new Si.TextInput;s.content=t,s.timestamp=Date.now();var r=new e.CommonMessage({name:"send_text",seq:this.GetSeq(),type:e.CommonMessage.Type.Request,sub_type:i,payload:s.serializeBinary()});return this.LogInput(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),_.Success},v.prototype.SendClipData=function(t){if(!this.hasLogined())return _.NotLogin;const i=Si.InputType.INPUT_TYPE_CLIP_DATA;var s=new Si.ClipData;s.content=t,s.timestamp=Date.now();var r=new e.CommonMessage({name:"send_text",seq:this.GetSeq(),type:e.CommonMessage.Type.Request,sub_type:i,payload:s.serializeBinary()});return this.LogInput(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),_.Success},v.prototype.SwitchIME=function(t,i){if(!this.hasLogined())return _.NotLogin;const s=Si.InputType.INPUT_TYPE_SWITCH_IME,r=new Si.IME;r.type=t,r.name=i;var n=new e.CommonMessage({name:"switch_ime",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:s,payload:r.serializeBinary()});return this.LogInput(JSON.stringify(n.toObject())),this.SendCommonMessage(n.serializeBinary()),_.Success},v.prototype.InitInput=function(){this.notificationHandles.set(Si.InputNotificationType.INPUT_NOTIFICATION_TYPE_CLIP_DATA,(e=>{var t;const i=Si.ClipData.deserializeBinary(e.payload);(null===(t=this.notify)||void 0===t?void 0:t.onClipboard)&&this.notify.onClipboard(i.content)})),this.notificationHandles.set(Si.InputNotificationType.INPUT_NOTIFICATION_IME_STATUS,(e=>{var t;const i=Si.IMEStatus.deserializeBinary(e.payload);if(null===(t=this.notify)||void 0===t?void 0:t.onIMEStatus){const e=new w;e.desc=i.content,i.show_soft_input?(e.show_soft_keyboard=!0,e.action=i.action,e.input_type=i.input_type,e.packet=i.package):e.show_soft_keyboard=!1,this.notify.onIMEStatus(e)}}))},function(e){e[e.GRAVITY=0]="GRAVITY",e[e.GYRO=1]="GYRO"}(Di||(Di={}));class xi{}class Li{}!function(e){e[e.ET_NONE=0]="ET_NONE",e[e.ET_INSTALL=1]="ET_INSTALL",e[e.ET_TRANSPORT=2]="ET_TRANSPORT"}(Ri||(Ri={})),function(e){e[e.NULL_INFO=0]="NULL_INFO",e[e.FOR_REMOVE=1]="FOR_REMOVE",e[e.FOR_START=2]="FOR_START"}(Ni||(Ni={})),function(e){e[e.JPEG=0]="JPEG",e[e.WEBP=1]="WEBP",e[e.PNG=2]="PNG"}(Pi||(Pi={}));class ji{constructor(e,t,i){this.rotation=e,this.device_rotation=e,this.url=t}}v.prototype.InitDevice=function(){this.notificationHandles.set(I.DeviceNotificationType.DEVICE_NOTIFICATION_PUSH_MSG,(e=>{var t;const i=I.PushMsg.deserializeBinary(e.payload);(null===(t=this.notify)||void 0===t?void 0:t.onServerPushMsg)&&this.notify.onServerPushMsg(i.str_msg)}))},v.prototype.SetOnScreenSize=function(e){if(this.onScreenSize=e,this.screen&&null!=this.onScreenSize){const e=this.screen;this.onScreenSize(e.screen_width,e.screen_height)}this.notificationHandles.has(I.DeviceNotificationType.DEVICE_NOTIFICATION_SCREEN)||this.notificationHandles.set(I.DeviceNotificationType.DEVICE_NOTIFICATION_SCREEN,(e=>{const t=I.Screen.deserializeBinary(e.payload);this.screen&&this.screen.screen_width===t.screen_width&&this.screen.screen_height===t.screen_height||this.onScreenSize(t.screen_width,t.screen_height),this.screen=t}))},v.prototype.GetPreview=function(t,i,s,r){return null==this.responseHandlers.get(I.DeviceType.DEVICE_TYPE_SCREEN_CAP)&&this.responseHandlers.set(I.DeviceType.DEVICE_TYPE_SCREEN_CAP,(e=>{const t=this.GetWaitForResponse(e.sub_type,e.seq),i=I.ScreenCapRes.deserializeBinary(e.payload);if(t)if(0!=i.common.code)t.reject(_.Error(i.common.code,"server error"));else{let e="image/jpeg";switch(i.format){case I.CaptureFormat.CAP_PNG:e="image/png";break;case I.CaptureFormat.CAP_WEBP:e="image/webp"}this.LogDevice("GetPreview mimeType:"+e);const s=URL.createObjectURL(new Blob([i.content],{type:e}));t.resolve(new ji(90*i.pic_rotation,s,90*i.rotation))}})),new Promise(((n,o)=>{const a=I.DeviceType.DEVICE_TYPE_SCREEN_CAP,l=new I.ScreenCap;l.size=new I.ScreenCap.Size,l.size.width=i,l.size.height=s,l.quality=r,l.format=t;const c=this.GetSeq(),h=new e.CommonMessage({name:"screen_cap",seq:c,type:e.CommonMessage.Type.Request,sub_type:I.DeviceType.DEVICE_TYPE_SCREEN_CAP,payload:l.serializeBinary()});this.LogDevice(JSON.stringify(h.toObject())),this.SendCommonMessage(h.serializeBinary()),this.AddWaitForResponse(a,c,n,o),setTimeout((()=>{const e=this.GetWaitForResponse(a,c);e&&e.reject(_.Timeout)}),3e3)}))},v.prototype.SendSensorData=function(t){return new Promise(((i,s)=>{if(!this.hasLogined())return void s(_.Error(p.NotLogin,"Not Login"));const r=I.DeviceType.DEVICE_TYPE_SENSOR_DATA,n=new e.CommonMessage({name:"sensor_data",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:r,payload:new I.SensorData({type:t.type,x:t.x,y:t.y,z:t.z,accuracy:t.accuracy}).serializeBinary()});this.LogDevice(JSON.stringify(n.toObject())),this.SendCommonMessage(n.serializeBinary()),i(_.Success)}))},v.prototype.Transfile=function(t,i){return null==this.responseHandlers.get(I.DeviceType.DEVICE_TYPE_TRANSFER_FILE)&&this.responseHandlers.set(I.DeviceType.DEVICE_TYPE_TRANSFER_FILE,(t=>{const i=this.GetWaitForResponse(t.sub_type,t.seq),s=e.CommonRes.deserializeBinary(t.payload);i&&(0!=s.code?i.reject(_.Error(s.code,s.result.toString())):i.resolve(_.Success))})),new Promise(((s,r)=>{if(!this.hasLogined())return void r(_.Error(p.NotLogin,"Not Login"));const n=I.DeviceType.DEVICE_TYPE_TRANSFER_FILE,o=this.GetSeq(),a=new e.CommonMessage({name:"transfer_file",seq:o,type:e.CommonMessage.Type.Request,sub_type:n,payload:new I.TransferFile({type:t,url:i}).serializeBinary()});this.LogDevice(JSON.stringify(a.toObject())),this.SendCommonMessage(a.serializeBinary()),this.AddWaitForResponse(n,o,s,r),setTimeout((()=>{const e=this.GetWaitForResponse(n,o);e&&e.reject(_.Timeout)}),12e4)}))},v.prototype.SendADBCommand=function(t){return new Promise(((i,s)=>{if(!this.hasLogined())return void s(_.Error(p.NotLogin,"Not Login"));const r=I.DeviceType.DEVICE_TYPE_ADB_COMMAND,n=new e.CommonMessage({name:"adb_command",type:e.CommonMessage.Type.Request,sub_type:r,seq:this.GetSeq(),payload:new I.AdbCommand({command:t}).serializeBinary()});this.LogDevice(JSON.stringify(n.toObject())),this.SendCommonMessage(n.serializeBinary()),i(_.Success)}))},v.prototype.GetInstalledPackage=function(t){return null==this.responseHandlers.get(I.DeviceType.DEVICE_TYPE_GET_INSTALLED_PACKAGE)&&this.responseHandlers.set(I.DeviceType.DEVICE_TYPE_GET_INSTALLED_PACKAGE,(e=>{const t=I.GetInstalledPackageRes.deserializeBinary(e.payload),i=this.GetWaitForResponse(e.sub_type,e.seq);if(i)if(0==t.common.code){const e=t.content;i.resolve(e)}else i.reject(_.Error(t.common.code,t.common.result.toString()))})),new Promise(((i,s)=>{const r=I.DeviceType.DEVICE_TYPE_GET_INSTALLED_PACKAGE,n=new I.GetInstalledPackage;t==Ni.FOR_REMOVE?n.type=I.PacketType.PACKAGE:n.type=I.PacketType.ACTIVITY;const o=this.GetSeq(),a=new e.CommonMessage({name:"get_installed_package",type:e.CommonMessage.Type.Request,seq:o,sub_type:r,payload:n.serializeBinary()});this.LogDevice(JSON.stringify(a.toObject())),this.SendCommonMessage(a.serializeBinary()),this.AddWaitForResponse(r,o,i,s),setTimeout((()=>{const e=this.GetWaitForResponse(r,o);e&&e.reject(_.Timeout)}),2e3)}))},v.prototype.GetResourceDetail=function(){return null==this.responseHandlers.get(I.DeviceType.DEVICE_TYPE_RESOURCE_DETAIL)&&this.responseHandlers.set(I.DeviceType.DEVICE_TYPE_RESOURCE_DETAIL,(e=>{const t=I.ResourceDetailRes.deserializeBinary(e.payload),i=this.GetWaitForResponse(e.sub_type,e.seq);if(i)if(0==t.common.code){const e=new Li;e.memory_total=t.memory_total,e.memory_used=t.memory_used,e.disk_total=t.disk_total,e.disk_used=t.disk_used,i.resolve(e)}else i.reject(_.Error(t.common.code,t.common.result.toString()))})),new Promise(((t,i)=>{const s=I.DeviceType.DEVICE_TYPE_RESOURCE_DETAIL,r=this.GetSeq(),n=new e.CommonMessage({name:"resource_detail",seq:r,type:e.CommonMessage.Type.Request,sub_type:s});this.AddWaitForResponse(s,r,t,i),this.LogDevice(JSON.stringify(n.toObject())),this.SendCommonMessage(n.serializeBinary()),setTimeout((()=>{const e=this.GetWaitForResponse(s,r);e&&e.reject(_.Timeout)}),2e3)}))},v.prototype.QueryScreenSize=function(){return null==this.responseHandlers.get(I.DeviceType.DEVICE_TYPE_QUEUE_SCREEN)&&this.responseHandlers.set(I.DeviceType.DEVICE_TYPE_QUEUE_SCREEN,(e=>{const t=I.QueryScreenRes.deserializeBinary(e.payload);0==t.common.code&&this.onScreenSize(t.screen.screen_width,t.screen.screen_height)})),new Promise(((t,i)=>{if(!this.hasLogined())return void i(_.Error(p.NotLogin,"Not Login"));const s=I.DeviceType.DEVICE_TYPE_QUEUE_SCREEN,r=new e.CommonMessage({name:"query_screen_size",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:s});this.LogDevice(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),t(_.Success)}))},v.prototype.Shake=function(){return new Promise(((t,i)=>{if(!this.hasLogined())return void i(_.Error(p.NotLogin,"Not Login"));const s=I.DeviceType.DEVICE_TYPE_SHAKE,r=new e.CommonMessage({name:"shake",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:s});this.LogDevice(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),t(_.Success)}))},v.prototype.GetDiskInfo=function(){return null==this.responseHandlers.get(I.DeviceType.DEVICE_TYPE_QUERY_DISK)&&this.responseHandlers.set(I.DeviceType.DEVICE_TYPE_QUERY_DISK,(e=>{const t=I.QueryDiskRes.deserializeBinary(e.payload);console.log(JSON.stringify(t.toObject()))})),new Promise(((t,i)=>{const s=new e.CommonMessage({name:"query_disk",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:I.DeviceType.DEVICE_TYPE_QUERY_DISK});this.LogDevice(JSON.stringify(s.toObject())),this.SendCommonMessage(s.serializeBinary()),t(_.Success)}))},v.prototype.SwitchSystemLanguage=function(t){return new Promise(((i,s)=>{const r=new I.SystemLocale({sys_locale:t}),n=new e.CommonMessage({name:"switch_system_language",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:I.DeviceType.DEVICE_TYPE_SWITCH_SYSTEM_LOCALE,payload:r.serializeBinary()});this.LogDevice(JSON.stringify(n.toObject())),this.SendCommonMessage(n.serializeBinary()),i(_.Success)}))},v.prototype.SwitchScreenSize=function(t,i){return new Promise(((s,r)=>{const n=new I.ChangeScreenSize({screen:new I.Screen({screen_width:t,screen_height:i})}),o=new e.CommonMessage({name:"switch_screen_size",type:e.CommonMessage.Type.Request,seq:this.GetSeq(),sub_type:I.DeviceType.DEVICE_TYPE_CHANGE_SCREEN_SIZE,payload:n.serializeBinary()});this.screen=n.screen,this.LogDevice(JSON.stringify(o.toObject())),this.SendCommonMessage(o.serializeBinary()),s(_.Success)}))};var Gi,Vi=function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};!function(i){var s,r,n,o,a;let l,c;!function(e){e[e.GAME_TYPE_UNKNOWN=0]="GAME_TYPE_UNKNOWN",e[e.GAME_TYPE_MIN=5e4]="GAME_TYPE_MIN",e[e.GAME_TYPE_CONFIG=50001]="GAME_TYPE_CONFIG",e[e.GAME_TYPE_RESTART_GAME=50002]="GAME_TYPE_RESTART_GAME",e[e.GAME_TYPE_START_GAME=50003]="GAME_TYPE_START_GAME",e[e.GAME_TYPE_STOP_GAME=50004]="GAME_TYPE_STOP_GAME",e[e.GAME_TYPE_SAVE_GAME_SAVE=50005]="GAME_TYPE_SAVE_GAME_SAVE",e[e.GAME_TYPE_LOAD_GAME_SAVE=50006]="GAME_TYPE_LOAD_GAME_SAVE",e[e.GAME_TYPE_MAX=6e4]="GAME_TYPE_MAX"}(l=i.GameType||(i.GameType={})),function(e){e[e.GAME_SAVE_TYPE_AUTO=0]="GAME_SAVE_TYPE_AUTO",e[e.GAME_SAVE_TYPE_MANUAL=1]="GAME_SAVE_TYPE_MANUAL"}(c=i.GameSaveType||(i.GameSaveType={}));class h extends t.Message{constructor(e){super(),s.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],Vi(this,s,"f")),Array.isArray(e)||"object"!=typeof e||("game_id"in e&&null!=e.game_id&&(this.game_id=e.game_id),"expiry_time"in e&&null!=e.expiry_time&&(this.expiry_time=e.expiry_time),"game_name"in e&&null!=e.game_name&&(this.game_name=e.game_name),"end_time"in e&&null!=e.end_time&&(this.end_time=e.end_time),"game_load"in e&&null!=e.game_load&&(this.game_load=e.game_load))}get game_id(){return t.Message.getFieldWithDefault(this,1,0)}set game_id(e){t.Message.setField(this,1,e)}get expiry_time(){return t.Message.getFieldWithDefault(this,2,0)}set expiry_time(e){t.Message.setField(this,2,e)}get game_name(){return t.Message.getFieldWithDefault(this,3,"")}set game_name(e){t.Message.setField(this,3,e)}get end_time(){return t.Message.getFieldWithDefault(this,4,0)}set end_time(e){t.Message.setField(this,4,e)}get game_load(){return t.Message.getWrapperField(this,p,5)}set game_load(e){t.Message.setWrapperField(this,5,e)}get has_game_load(){return null!=t.Message.getField(this,5)}static fromObject(e){const t=new h({});return null!=e.game_id&&(t.game_id=e.game_id),null!=e.expiry_time&&(t.expiry_time=e.expiry_time),null!=e.game_name&&(t.game_name=e.game_name),null!=e.end_time&&(t.end_time=e.end_time),null!=e.game_load&&(t.game_load=p.fromObject(e.game_load)),t}toObject(){const e={};return null!=this.game_id&&(e.game_id=this.game_id),null!=this.expiry_time&&(e.expiry_time=this.expiry_time),null!=this.game_name&&(e.game_name=this.game_name),null!=this.end_time&&(e.end_time=this.end_time),null!=this.game_load&&(e.game_load=this.game_load.toObject()),e}serialize(e){const i=e||new t.BinaryWriter;if(0!=this.game_id&&i.writeSint64(1,this.game_id),0!=this.expiry_time&&i.writeSint64(2,this.expiry_time),this.game_name.length&&i.writeString(3,this.game_name),0!=this.end_time&&i.writeUint64(4,this.end_time),this.has_game_load&&i.writeMessage(5,this.game_load,(()=>this.game_load.serialize(i))),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new h;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.game_id=i.readSint64();break;case 2:s.expiry_time=i.readSint64();break;case 3:s.game_name=i.readString();break;case 4:s.end_time=i.readUint64();break;case 5:i.readMessage(s.game_load,(()=>s.game_load=p.deserialize(i)));break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return h.deserialize(e)}}s=new WeakMap,i.GameInfo=h;class p extends t.Message{constructor(e){super(),r.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],Vi(this,r,"f")),Array.isArray(e)||"object"!=typeof e||("obs_name"in e&&null!=e.obs_name&&(this.obs_name=e.obs_name),"path"in e&&null!=e.path&&(this.path=e.path))}get obs_name(){return t.Message.getFieldWithDefault(this,1,"")}set obs_name(e){t.Message.setField(this,1,e)}get path(){return t.Message.getFieldWithDefault(this,2,"")}set path(e){t.Message.setField(this,2,e)}static fromObject(e){const t=new p({});return null!=e.obs_name&&(t.obs_name=e.obs_name),null!=e.path&&(t.path=e.path),t}toObject(){const e={};return null!=this.obs_name&&(e.obs_name=this.obs_name),null!=this.path&&(e.path=this.path),e}serialize(e){const i=e||new t.BinaryWriter;if(this.obs_name.length&&i.writeString(1,this.obs_name),this.path.length&&i.writeString(2,this.path),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new p;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.obs_name=i.readString();break;case 2:s.path=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return p.deserialize(e)}}r=new WeakMap,i.GameSaveLoad=p;class u extends t.Message{constructor(e){super(),n.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],Vi(this,n,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"str_error"in e&&null!=e.str_error&&(this.str_error=e.str_error),"str_result"in e&&null!=e.str_result&&(this.str_result=e.str_result))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get str_error(){return t.Message.getFieldWithDefault(this,2,"")}set str_error(e){t.Message.setField(this,2,e)}get str_result(){return t.Message.getFieldWithDefault(this,3,"")}set str_result(e){t.Message.setField(this,3,e)}static fromObject(t){const i=new u({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.str_error&&(i.str_error=t.str_error),null!=t.str_result&&(i.str_result=t.str_result),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.str_error&&(e.str_error=this.str_error),null!=this.str_result&&(e.str_result=this.str_result),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),this.str_error.length&&i.writeString(2,this.str_error),this.str_result.length&&i.writeString(3,this.str_result),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new u;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:r.str_error=s.readString();break;case 3:r.str_result=s.readString();break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return u.deserialize(e)}}n=new WeakMap,i.GameSaveLoadRes=u;class d extends t.Message{constructor(e){super(),o.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],Vi(this,o,"f")),Array.isArray(e)||"object"!=typeof e||("save_type"in e&&null!=e.save_type&&(this.save_type=e.save_type),"name"in e&&null!=e.name&&(this.name=e.name),"obs_name"in e&&null!=e.obs_name&&(this.obs_name=e.obs_name),"path"in e&&null!=e.path&&(this.path=e.path),"cover_save_id"in e&&null!=e.cover_save_id&&(this.cover_save_id=e.cover_save_id),"token"in e&&null!=e.token&&(this.token=e.token))}get save_type(){return t.Message.getFieldWithDefault(this,1,c.GAME_SAVE_TYPE_AUTO)}set save_type(e){t.Message.setField(this,1,e)}get name(){return t.Message.getFieldWithDefault(this,2,"")}set name(e){t.Message.setField(this,2,e)}get obs_name(){return t.Message.getFieldWithDefault(this,3,"")}set obs_name(e){t.Message.setField(this,3,e)}get path(){return t.Message.getFieldWithDefault(this,4,"")}set path(e){t.Message.setField(this,4,e)}get cover_save_id(){return t.Message.getFieldWithDefault(this,5,0)}set cover_save_id(e){t.Message.setField(this,5,e)}get token(){return t.Message.getFieldWithDefault(this,6,"")}set token(e){t.Message.setField(this,6,e)}static fromObject(e){const t=new d({});return null!=e.save_type&&(t.save_type=e.save_type),null!=e.name&&(t.name=e.name),null!=e.obs_name&&(t.obs_name=e.obs_name),null!=e.path&&(t.path=e.path),null!=e.cover_save_id&&(t.cover_save_id=e.cover_save_id),null!=e.token&&(t.token=e.token),t}toObject(){const e={};return null!=this.save_type&&(e.save_type=this.save_type),null!=this.name&&(e.name=this.name),null!=this.obs_name&&(e.obs_name=this.obs_name),null!=this.path&&(e.path=this.path),null!=this.cover_save_id&&(e.cover_save_id=this.cover_save_id),null!=this.token&&(e.token=this.token),e}serialize(e){const i=e||new t.BinaryWriter;if(this.save_type!=c.GAME_SAVE_TYPE_AUTO&&i.writeEnum(1,this.save_type),this.name.length&&i.writeString(2,this.name),this.obs_name.length&&i.writeString(3,this.obs_name),this.path.length&&i.writeString(4,this.path),0!=this.cover_save_id&&i.writeUint32(5,this.cover_save_id),this.token.length&&i.writeString(6,this.token),!e)return i.getResultBuffer()}static deserialize(e){const i=e instanceof t.BinaryReader?e:new t.BinaryReader(e),s=new d;for(;i.nextField()&&!i.isEndGroup();)switch(i.getFieldNumber()){case 1:s.save_type=i.readEnum();break;case 2:s.name=i.readString();break;case 3:s.obs_name=i.readString();break;case 4:s.path=i.readString();break;case 5:s.cover_save_id=i.readUint32();break;case 6:s.token=i.readString();break;default:i.skipField()}return s}serializeBinary(){return this.serialize()}static deserializeBinary(e){return d.deserialize(e)}}o=new WeakMap,i.GameSave=d;class f extends t.Message{constructor(e){super(),a.set(this,[]),t.Message.initialize(this,Array.isArray(e)?e:[],0,-1,[],Vi(this,a,"f")),Array.isArray(e)||"object"!=typeof e||("common"in e&&null!=e.common&&(this.common=e.common),"str_error"in e&&null!=e.str_error&&(this.str_error=e.str_error),"str_result"in e&&null!=e.str_result&&(this.str_result=e.str_result))}get common(){return t.Message.getWrapperField(this,e.CommonRes,1)}set common(e){t.Message.setWrapperField(this,1,e)}get has_common(){return null!=t.Message.getField(this,1)}get str_error(){return t.Message.getFieldWithDefault(this,2,"")}set str_error(e){t.Message.setField(this,2,e)}get str_result(){return t.Message.getFieldWithDefault(this,3,"")}set str_result(e){t.Message.setField(this,3,e)}static fromObject(t){const i=new f({});return null!=t.common&&(i.common=e.CommonRes.fromObject(t.common)),null!=t.str_error&&(i.str_error=t.str_error),null!=t.str_result&&(i.str_result=t.str_result),i}toObject(){const e={};return null!=this.common&&(e.common=this.common.toObject()),null!=this.str_error&&(e.str_error=this.str_error),null!=this.str_result&&(e.str_result=this.str_result),e}serialize(e){const i=e||new t.BinaryWriter;if(this.has_common&&i.writeMessage(1,this.common,(()=>this.common.serialize(i))),this.str_error.length&&i.writeString(2,this.str_error),this.str_result.length&&i.writeString(3,this.str_result),!e)return i.getResultBuffer()}static deserialize(i){const s=i instanceof t.BinaryReader?i:new t.BinaryReader(i),r=new f;for(;s.nextField()&&!s.isEndGroup();)switch(s.getFieldNumber()){case 1:s.readMessage(r.common,(()=>r.common=e.CommonRes.deserialize(s)));break;case 2:r.str_error=s.readString();break;case 3:r.str_result=s.readString();break;default:s.skipField()}return r}serializeBinary(){return this.serialize()}static deserializeBinary(e){return f.deserialize(e)}}a=new WeakMap,i.GameSaveRes=f}(Gi||(Gi={}));var Ui,Yi=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};class Hi{constructor(e,t,i,s){this.gameId=e,this.gameName=t,this.expiryTime=i,this.endTime=s}}class qi{constructor(e,t){this.obs_name=e,this.path=t}}!function(e){e[e.GAME_SAVE_TYPE_AUTO=0]="GAME_SAVE_TYPE_AUTO",e[e.GAME_SAVE_TYPE_MANUAL=1]="GAME_SAVE_TYPE_MANUAL"}(Ui||(Ui={}));class Ki{constructor(e,t,i,s){this.type=e,this.name=t,this.path=i,this.cover_save_id=s}}v.prototype.StartGame=function(t){return Yi(this,void 0,void 0,(function*(){return new Promise(((i,s)=>{if(!this.hasLogined())return void s(_.Error(p.NotLogin,"Not login"));if(!t.gameId||!t.gameName)return void s(_.Error(p.ErrorParams,"Invalid game info"));const r=Gi.GameType.GAME_TYPE_START_GAME,n=new Gi.GameInfo({game_id:t.gameId,expiry_time:t.expiryTime,game_name:t.gameName,end_time:t.endTime}),o=new e.CommonMessage({seq:this.GetSeq(),name:"start_game",sub_type:r,type:e.CommonMessage.Type.Request,payload:n.serializeBinary()});this.LogGame(JSON.stringify(o.toObject())),this.SendCommonMessage(o.serializeBinary()),i(_.Success)}))}))},v.prototype.RestartGame=function(t){return Yi(this,void 0,void 0,(function*(){return new Promise(((i,s)=>{if(!this.hasLogined())return void s(_.Error(p.NotLogin,"Not login"));if(!t.gameId||!t.gameName)return void s("Invalid game info");const r=Gi.GameType.GAME_TYPE_RESTART_GAME,n=new Gi.GameInfo({game_id:t.gameId,expiry_time:t.expiryTime,game_name:t.gameName,end_time:t.endTime}),o=new e.CommonMessage({seq:this.GetSeq(),name:"restart_game",sub_type:r,type:e.CommonMessage.Type.Request,payload:n.serializeBinary()});this.LogGame(JSON.stringify(o.toObject())),this.SendCommonMessage(o.serializeBinary()),i(_.Success)}))}))},v.prototype.StopGame=function(){return Yi(this,void 0,void 0,(function*(){return new Promise(((t,i)=>{if(!this.hasLogined())return void i(_.Error(p.NotLogin,"Not login"));const s=Gi.GameType.GAME_TYPE_STOP_GAME,r=new e.CommonMessage({name:"stop_game",seq:this.GetSeq(),sub_type:s,type:e.CommonMessage.Type.Request});this.LogGame(JSON.stringify(r.toObject())),this.SendCommonMessage(r.serializeBinary()),t(_.Success)}))}))},v.prototype.SaveGameArchive=function(t){return Yi(this,void 0,void 0,(function*(){return null==this.responseHandlers.get(Gi.GameType.GAME_TYPE_SAVE_GAME_SAVE)&&this.responseHandlers.set(Gi.GameType.GAME_TYPE_SAVE_GAME_SAVE,(e=>{const t=this.GetWaitForResponse(e.sub_type,e.seq),i=Gi.GameSaveRes.deserializeBinary(e.payload);t&&(0!=i.common.code?t.resolve(_.Error(i.common.code,i.str_error)):t.resolve(_.Success))})),new Promise(((i,s)=>{if(!this.hasLogined())return void s("Not login");const r=Gi.GameType.GAME_TYPE_SAVE_GAME_SAVE,n=this.GetSeq(),o=new Gi.GameSave({path:t.path,save_type:t.type,name:t.name,cover_save_id:t.cover_save_id}),a=new e.CommonMessage({name:"save_game_save",seq:n,sub_type:r,type:e.CommonMessage.Type.Request,payload:o.serializeBinary()});this.LogGame(JSON.stringify(a.toObject())),this.SendCommonMessage(a.serializeBinary()),setTimeout((()=>{const e=this.GetWaitForResponse(r,n);e&&e.reject(_.Timeout)}),2e3)}))}))},v.prototype.LoadGameArchive=function(t){return Yi(this,void 0,void 0,(function*(){return null==this.responseHandlers.get(Gi.GameType.GAME_TYPE_LOAD_GAME_SAVE)&&this.responseHandlers.set(Gi.GameType.GAME_TYPE_LOAD_GAME_SAVE,(e=>{const t=this.GetWaitForResponse(e.sub_type,e.seq),i=Gi.GameSaveRes.deserializeBinary(e.payload);t&&(0!=i.common.code?t.resolve(_.Error(i.common.code,i.str_error)):t.resolve(_.Success))})),new Promise(((i,s)=>{if(!this.hasLogined())return void s(_.Error(p.NotLogin,"Not login"));const r=Gi.GameType.GAME_TYPE_LOAD_GAME_SAVE,n=this.GetSeq(),o=new Gi.GameSaveLoad({obs_name:t.obs_name,path:t.path}),a=new e.CommonMessage({name:"load_game_save",sub_type:r,type:e.CommonMessage.Type.Request,seq:n,payload:o.serializeBinary()});this.LogGame(JSON.stringify(a.toObject())),this.SendCommonMessage(a.serializeBinary()),setTimeout((()=>{const e=this.GetWaitForResponse(r,n);e&&e.reject(_.Timeout)}),2e3)}))}))};var Xi=function(e,t,i,s){return new(i||(i=Promise))((function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(o,a)}l((s=s.apply(e,t||[])).next())}))};class Ji{constructor(e,t,i){this.priority_native=e,this.native_valid=t,this.webrtc_valid=i}}function Qi(e){return Xi(this,void 0,void 0,(function*(){try{if(yield ft(),ht()&&ht())return Promise.resolve(_.Success);if(!e)return Promise.resolve(_.Success);{console.log("wasm_url load",e);const t=document.createElement("script");t.src=e,yield new Promise(((e,i)=>{t.onload=()=>{Be().then((()=>{dt(),e(_.Success)})).catch((()=>{dt(!1),console.warn("wasm load error")}))},t.onerror=()=>{i(_.Error(p.ErrorLoadResource,"wasm load error"))},document.body.appendChild(t)}))}}catch(e){return e}}))}function Zi(){return Xi(this,void 0,void 0,(function*(){if(!("WebSocket"in window))return new Ji(!1,!1,!1);const e="RTCPeerConnection"in window;let t=!1,i=wt(!1);return i&&(t=(yield ft()).has(at.h264High)),new Ji(t,i,e)}))}function $i(e,t){const i=new v(e,t);return i.InitInput(),i.InitDevice(),i}})(),s})()));