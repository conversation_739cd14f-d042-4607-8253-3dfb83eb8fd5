<!DOCTYPE html>
<html>
<head>
    <title>SDK切换按钮样式预览</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .demo-area {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            margin: 20px 0;
        }
        .sdk-switch {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: 2px solid #07C160;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            user-select: none;
        }
        .sdk-switch:hover {
            background: rgba(7, 193, 96, 0.2);
            transform: scale(1.1);
        }
        .sdk-switch:active {
            transform: scale(0.95);
        }
        .sdk-switch.jh-active {
            border-color: #007bff;
            color: #007bff;
        }
        .sdk-switch.ld-active {
            border-color: #07C160;
            color: #07C160;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>SDK切换按钮样式预览</h1>
    
    <div class="preview-container">
        <h2>新版本按钮样式</h2>
        <div class="demo-area">
            <div id="sdkSwitchBtn" class="sdk-switch ld-active">
                LD
            </div>
        </div>
        
        <div class="controls">
            <button onclick="switchToJH()">切换到 JH SDK</button>
            <button onclick="switchToLD()">切换到 LD SDK</button>
        </div>
        
        <div class="info">
            <strong>当前状态：</strong>
            <span id="currentStatus">LD SDK 激活</span>
        </div>
    </div>

    <div class="preview-container">
        <h2>设计特性</h2>
        <ul class="feature-list">
            <li>小圆点设计，不占用太多屏幕空间</li>
            <li>固定在右上角，方便访问</li>
            <li>显示当前SDK类型（JH/LD）</li>
            <li>不同SDK有不同的颜色标识</li>
            <li>悬停时有放大效果</li>
            <li>点击时有缩小反馈</li>
            <li>平滑的过渡动画</li>
            <li>半透明背景，不遮挡内容</li>
        </ul>
    </div>

    <div class="preview-container">
        <h2>颜色方案</h2>
        <div style="display: flex; gap: 20px; align-items: center;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="sdk-switch jh-active" style="position: static; margin: 0;">JH</div>
                <span>JH SDK - 蓝色主题 (#007bff)</span>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="sdk-switch ld-active" style="position: static; margin: 0;">LD</div>
                <span>LD SDK - 绿色主题 (#07C160)</span>
            </div>
        </div>
    </div>

    <div class="preview-container">
        <h2>使用说明</h2>
        <div class="info">
            <p><strong>在实际页面中：</strong></p>
            <p>1. 按钮位于页面右上角</p>
            <p>2. 默认显示当前激活的SDK类型</p>
            <p>3. 点击按钮即可切换到另一种SDK</p>
            <p>4. 切换时会自动清理当前连接并重新初始化</p>
        </div>
    </div>

    <script>
        const sdkSwitchBtn = document.getElementById('sdkSwitchBtn');
        const currentStatus = document.getElementById('currentStatus');
        let currentSdk = 'LD';

        function switchToJH() {
            currentSdk = 'JH';
            updateButtonState();
        }

        function switchToLD() {
            currentSdk = 'LD';
            updateButtonState();
        }

        function updateButtonState() {
            sdkSwitchBtn.classList.remove('jh-active', 'ld-active');
            
            if (currentSdk === 'JH') {
                sdkSwitchBtn.classList.add('jh-active');
                sdkSwitchBtn.textContent = 'JH';
                currentStatus.textContent = 'JH SDK 激活';
            } else {
                sdkSwitchBtn.classList.add('ld-active');
                sdkSwitchBtn.textContent = 'LD';
                currentStatus.textContent = 'LD SDK 激活';
            }
        }

        // 点击按钮切换
        sdkSwitchBtn.addEventListener('click', function() {
            currentSdk = currentSdk === 'JH' ? 'LD' : 'JH';
            updateButtonState();
        });
    </script>
</body>
</html>
