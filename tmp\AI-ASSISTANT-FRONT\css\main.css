
body {
    font-family: 'Noto Sans SC', system-ui, -apple-system, sans-serif;
    -webkit-tap-highlight-color: transparent;
    position: relative; /* Ensure body is positioned for absolute children */
    overflow: hidden; /* Hide overflow from particles */
}

.wechat-green {
    background: linear-gradient(135deg, #07c160 0%, #36cfc9 100%);
}

.btn-hover {
    transition: all 0.3s ease;
}

.btn-hover:active {
    transform: scale(0.98);
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #07c160;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 确认弹框样式 */
.confirm-dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.confirm-dialog.show {
    display: flex;
}

.confirm-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    width: 80%;
    max-width: 320px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.confirm-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
}

.confirm-buttons {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.confirm-btn {
    flex: 1;
    padding: 10px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.confirm-btn.primary {
    background: #07c160;
    color: white;
    border: none;
}

.confirm-btn.secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.confirm-btn:active {
    transform: scale(0.98);
}

/* Background particles CSS */
.bg-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1; /* Send to background */
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(0, 212, 255, 0.6); /* Cyan */
    border-radius: 50%;
    opacity: 0.6;
    animation: float 8s infinite ease-in-out;
}

.particle:nth-child(2n) {
    background: rgba(139, 92, 246, 0.6); /* Purple */
    animation-delay: -2s;
    animation-duration: 12s;
}

.particle:nth-child(3n) {
    background: rgba(16, 185, 129, 0.6); /* Green */
    animation-delay: -4s;
    animation-duration: 10s;
}

@keyframes float {
    0% {
        transform: translateY(100vh); /* Start from bottom */
        opacity: 0;
    }
    100% {
        transform: translateY(-100vh); /* Move upwards */
        opacity: 1;
    }
}

/* Background geometric decoration */
.bg-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -2; /* Even further back */
    opacity: 0.05;
}

.bg-decoration::before {
    content: '';
    position: absolute;
    top: 10%;
    right: 10%;
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.8), rgba(139, 92, 246, 0.8));
    border-radius: 50%;
    filter: blur(60px);
}

.bg-decoration::after {
    content: '';
    position: absolute;
    bottom: 20%;
    left: 15%;
    width: 150px;
    height: 150px;
    background: linear-gradient(-45deg, rgba(16, 185, 129, 0.8), rgba(0, 212, 255, 0.8));
    border-radius: 50%;
    filter: blur(50px);
}

/* Ensure content is above background */
.flex-col {
    position: relative;
    z-index: 1;
}
