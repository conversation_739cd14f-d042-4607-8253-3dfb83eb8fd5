<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <title>微信快捷登录</title>
        <link href="sdk/font-awesome.6.0.0.css" rel="stylesheet" />
        <link rel="stylesheet" href="sdk/weui.min.2.5.16.css" />
        <link rel="stylesheet" href="css/main.css" />
        <script src="sdk/tailwindcss.min.3.4.16.js"></script>
        <script type="text/javascript" src="sdk/weui.min.1.2.21.js"></script>
        <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
        <script src="sdk/vconsole.min.3.15.1.js"></script>
        <script>
            new VConsole();
        </script>
    </head>
    <body class="bg-gray-50 min-h-screen flex flex-col">
        <div class="bg-particles" id="particles"></div>
        <div class="bg-decoration"></div>


        <div id="newUserConfirmDialog" class="confirm-dialog">
            <div class="confirm-content">
                <div class="confirm-title">AI 个人助理已就位！</div>
                <div class="confirm-buttons">
                    <button class="confirm-btn primary" onclick="closeWindow()">立即开始对话</button>
                </div>
            </div>
        </div>

        <div id="oldUserConfirmDialog" class="confirm-dialog">
            <div class="confirm-content">
                <div class="confirm-title">欢迎回来！已为您连接专属助理</div>
                <div class="confirm-buttons">
                    <button class="confirm-btn primary" onclick="closeWindow()">立即开始对话</button>
                </div>
            </div>
        </div>


        <!-- <div id="confirmDialog" class="confirm-dialog">
            <div class="confirm-content">
                <div class="confirm-title">现在去体验AI个人助理</div>
                <div class="confirm-buttons">
                    <button class="confirm-btn primary" onclick="confirmRedirect()">确认</button>
                </div>
            </div>
        </div> -->

        <div class="flex-1 flex flex-col items-center px-4">
            <div
                class="w-28 h-28 bg-white rounded-full shadow-lg flex items-center justify-center mt-[22.22vh] mb-16 overflow-hidden transform hover:scale-105 transition-transform duration-300"
            >
                <img src="public/static/aia_logo.png" alt="Logo" class="w-full h-full object-contain" />
            </div>

            <p class="text-gray-600 text-base mb-12 font-medium leading-normal text-center">
                AI个人助理，<br />
                让您的生活更便捷
            </p>

            <a
                href="#"
                id="wechatLoginBtn"
                class="inline-flex bg-[#07C160] text-white py-3 px-6 rounded-xl shadow-lg btn-hover items-center justify-center mb-4 transform hover:scale-[1.02] transition-all duration-300 whitespace-nowrap"
            >
                <span class="font-medium text-base">微信一键登录</span>
            </a>

            <div class="text-xs text-gray-400 text-center mb-8">
                登录即表示同意
                <a href="#" class="text-[#07C160] hover:text-[#06ae56] transition-colors duration-300">《用户协议》</a>
                和
                <a href="#" class="text-[#07C160] hover:text-[#06ae56] transition-colors duration-300">《隐私政策》</a>
            </div>
        </div>

        <div class="text-center text-gray-400 text-xs py-4 border-t border-gray-100">
            <div>© 2024 版权所有</div>
        </div>

        <div id="loadingMask" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 hidden">
            <div class="bg-white p-4 rounded-lg shadow-xl flex items-center">
                <div class="loading-spinner mr-3"></div>
                <span class="text-gray-700">正在验证身份...</span>
            </div>
        </div>

        <script src="js/config.20250814_2027.js"></script>
        <script src="js/utils.20250814_2027.js"></script>
        <script src="js/h5login.20250814_2027.js"></script>
    </body>
</html>