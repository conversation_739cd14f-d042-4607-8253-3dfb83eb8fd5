document.addEventListener('DOMContentLoaded', () => {
    console.log('[DOMContentLoaded] 页面加载完成');
    console.log('[DOMContentLoaded] 当前URL:', window.location.href);

    // Create background particles
    createParticles();

    const queryString = window.location.search.substring(1);
    const wechatLoginUrl = generateWechatLoginUrl(queryString);
    console.log('[DOMContentLoaded] 生成微信授权页面地址:', wechatLoginUrl);

    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const senderId = urlParams.get('senderId');
    console.log('[DOMContentLoaded] URL参数检查 - code:', code);
    console.log('[DOMContentLoaded] URL参数检查 - senderId:', senderId);

    if (senderId) {
        sessionStorage.setItem(window.SESSION_STORAGE_CACHE_KEY_SENDER_ID, senderId);
        console.log(
            '[DOMContentLoaded] 保存 senderId 到 sessionStorage:',
            window.SESSION_STORAGE_CACHE_KEY_SENDER_ID,
            senderId,
        );
    } 
    if (code) {//微信授权后的回调
        const cacheSenderId = sessionStorage.getItem(window.SESSION_STORAGE_CACHE_KEY_SENDER_ID);
        console.log('[DOMContentLoaded] 检测到 code 开始处理回调');
        handleWechatCallback(code, cacheSenderId);
    }

    const wechatLoginBtn = document.getElementById('wechatLoginBtn');
    wechatLoginBtn.onclick = function (e) {
        e.preventDefault();
        window.location.replace(wechatLoginUrl);
    };
});

// 创建背景粒子
function createParticles() {
    const particlesContainer = document.getElementById('particles');
    const particleCount = 20; // Number of particles

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = '100%';
        particle.style.animationDuration = Math.random() * 10 + 10 + 's';
        particle.style.animationDelay = Math.random() * 8 + 's';
        particlesContainer.appendChild(particle);
    }
}

// 显示确认弹框
function showConfirmDialog(isNewUser) {
    if (isNewUser) {
        document.getElementById('newUserConfirmDialog').classList.add('show');
    } else {
        document.getElementById('oldUserConfirmDialog').classList.add('show');
    }

}

// 隐藏确认弹框
function hideConfirmDialog() {
    document.getElementById('confirmDialog').classList.remove('show');
}

// 确认跳转
function confirmRedirect(page) {
    if (page) {
        window.location.href = page + '.html';
        return
    }
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        console.log('是微信浏览器');

        document.addEventListener('WeixinJSBridgeReady', function () {
            WeixinJSBridge.call('closeWindow');
        }, false);
        WeixinJSBridge.call('closeWindow');

    } else {
        console.log('不是微信浏览器');
    }
}

// 生成微信登录URL
function generateWechatLoginUrl(queryString) {
    redirect_uri = `${window.API_URL_BASE_VDI}/${window.URL_PATH}/h5login.html`
    if (queryString) {
        redirect_uri = `${window.API_URL_BASE_VDI}/${window.URL_PATH}/h5login.html?${queryString}`
    }
    console.log('redirect_uri:', redirect_uri);

    const wxConfig = {
        appid: `${window.WX_MP_APPID}`,
        redirect_uri: redirect_uri,
        scope: 'snsapi_userinfo',
        state: 'STATE',
    };
    const baseUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize';
    const params = new URLSearchParams({
        appid: wxConfig.appid,
        response_type: 'code',
        scope: wxConfig.scope,
        state: wxConfig.state,
        redirect_uri: wxConfig.redirect_uri,
    }).toString();
    return `${baseUrl}?${params}#wechat_redirect`;
}

// 保存用户ID到localStorage
function saveUserToLocalStorage(data) {
    const { uid, token } = data;
    localStorage.setItem(window.LOCAL_STORAGE_CACHE_KEY_UID, uid);
    localStorage.setItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN, token);
    console.log('保存 uid 到 localStorage:', window.LOCAL_STORAGE_CACHE_KEY_UID, uid);
    console.log('保存 token 到 localStorage:', window.LOCAL_STORAGE_CACHE_KEY_TOKEN, token);
}

// 处理微信登录回调
async function handleWechatCallback(code, senderId) {
    console.log('[handleWechatCallback] 开始处理微信回调...');
    console.log('[handleWechatCallback] 参数 - code:', code);
    console.log('[handleWechatCallback] 参数 - senderId:', senderId);
    console.log('[handleWechatCallback] url:', window.location.href);


    try {
        console.log('[handleWechatCallback] 准备调用后端接口...');
        showLoading();
        const response = await fetch(`${window.API_URL_BASE_VDI}/decs-cvgs/api/aia/wechat-login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                v: '1.0.0',
                arg: {
                    code: code,
                    senderId: senderId,
                }
            }),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const rspData = await response.json();
        console.log('[handleWechatCallback] 后端接口返回数据:', rspData);

        if (rspData.errorCode !== 0) {
            throw new Error(rspData.errorMessage);
        }

        if (rspData.data?.uid && rspData.data?.token) {
            saveUserToLocalStorage(rspData.data);

            const queryString = window.location.search.substring(1);
            const urlParams = new URLSearchParams(queryString);

            const eacRedirect = urlParams.get('eac_redirect');
            console.log('eacRedirect:', eacRedirect);

            urlParams.delete('code');   // 移除 微信返回的code 参数
            urlParams.delete('state');  // 移除 微信返回的state 参数


            // 如果存在 eac_redirect 参数，则重定向到该页面
            if (eacRedirect) {
                urlParams.delete('eac_redirect');  // 返回回去时，移除传过来的 eac_redirect 参数

                if (urlParams.size > 0) {
                    window.location.href = eacRedirect + '.html?' + urlParams.toString();
                } else {
                    window.location.href = eacRedirect + '.html';
                }
            } else {
                showConfirmDialog(rspData.data.isNewUser);
            }

        } else {
            throw new Error('响应成功，未获取到用户信息');
        }
    } catch (error) {
        console.error('[handleWechatCallback] 登录失败:', error);
        weui.alert(`${error?.message || '登录失败, 未知错误'}`);
    } finally {
        hideLoading();
    }
}