
// 显示加载遮罩
function showLoading() {
    const loadingMask = document.getElementById('loadingMask');
    if (loadingMask) {
        loadingMask.classList.remove('hidden');
    }
}

// 隐藏加载遮罩
function hideLoading() {
    const loadingMask = document.getElementById('loadingMask');
    if (loadingMask) {
        loadingMask.classList.add('hidden');
    }
}

// 跳转到登录页
function redirect2Login(page,queryString) {
    const cacheSenderId = sessionStorage.getItem(window.SESSION_STORAGE_CACHE_KEY_SENDER_ID) || '';
    
    let redirectUrl = 'h5login.html';
    let params = [];

    params.push(`eac_redirect=` + page);

    params.push(`state=${encodeURIComponent(window.location.href)}`);
    
    // Handle query string
    if (queryString && typeof queryString === 'string') {
        params.push(queryString);
    }

    // Handle senderId
    if (cacheSenderId !== '') {
        params.push(`senderId=${encodeURIComponent(cacheSenderId)}`);
    }
    
    if (params.length > 0) {
        redirectUrl += '?' + params.join('&');
    }
    console.log(`[redirect2Login] redirectUrl: ${redirectUrl}`);
    window.location.href = redirectUrl;
}


function getBasePath() {
    let path = window.location.origin;
    let pathname = window.location.pathname;
    let demoIdx = pathname.lastIndexOf('/');
    if (demoIdx !== -1) {
        return path + pathname.substr(0, demoIdx);
    } else {
        return path;
    }
}

function isBlank(str) {
    return str === null || str === undefined || str.trim() === '';
}


function closeWindow() {
    // window.location.href = `index.html`;
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        console.log('是微信浏览器');

        document.addEventListener('WeixinJSBridgeReady', function() {
            WeixinJSBridge.call('closeWindow');
        }, false);
        WeixinJSBridge.call('closeWindow');

    } else {
        console.log('不是微信浏览器');
    }

}

// 验证JSON格式是否有效
function isValidJson(str) {
    // 检查是否为空或非字符串
    if (typeof str !== 'string' || str.trim() === '' || !str.startsWith('{')) {
        return false;
    }

    // 尝试解析JSON
    try {
        JSON.parse(str);
        return true;
    } catch (e) {
        return false;
    }
}