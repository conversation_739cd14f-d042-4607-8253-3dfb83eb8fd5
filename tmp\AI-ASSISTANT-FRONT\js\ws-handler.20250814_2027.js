// 核心WebSocket连接管理对象
const socketManager = {
  socket: null,
  url: null,               // 保存连接URL用于重连
  pongReceived: false,     // 是否已收到pong

  connectionReadyCallbacks: [], // 使用数组存储多个回调函数

  pingTimer: null,        // ping超时定时器
  pongTimer: null,        // pong超时定时器
  pingTimeout: 10*1000,   // 10s
  pongTimeout: 30*1000,   // 30s

  reconnectTimer: null,    // 重连定时器
  reconnectLock: false,    // 重连锁

  init(url) {
    // 1. 建立WebSocket连接
    this.socket = new WebSocket(url);
    this.url = url;
    this.pongReceived = false;

    // 2. 连接成功回调
    this.socket.onopen = () => {
      console.log('WS连接成功');
      this.send('ping');

      // 执行所有连接就绪回调
      this.executeConnectionReadyCallbacks();

      // 心跳检测
      this.heartbeat();
    };

    // 3. 接收JSON消息
    this.socket.onmessage = (event) => {
      try {
        console.log('收到消息:', event.data);

        // 处理消息
        this.handleMessage(event.data);

        // 心跳检测
        this.heartbeat();
      } catch(e) {
        console.error('消息解析失败', e);
      }
    };

    // 4. 错误处理
    this.socket.onerror = (error) => {
      console.error('WS错误:', error);
    };

    // 5. 连接关闭处理
    this.socket.onclose = (event) => {
      console.log(`WS连接关闭: ${event.code} - ${event.reason || '未知原因'}`);

      // 重置心跳检测
      this.resetHeartbeat();

      // 如果不是主动关闭则尝试重连
      if (event.code !== 1000) {
        this.reconnect();
      }
    };
  },

  // 发送JSON消息方法
  send(data) {
    console.log('消息发送:', data);
    if (data === null || data === '') {
        return false;
    }
    
    if(this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(data);
      console.log('WS发送成功');
      return true;
    }
    console.warn('消息发送失败：连接未就绪');
    return false;
  },

  // 发送消息，处理连接就绪逻辑
  sendWhenReady(data) {
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.send(data);
    } else {
      this.onConnectionReady(() => {
        console.log('WebSocket连接已就绪, 发送消息');
        this.send(data);
      });
    }
  },

  // 注册连接就绪回调
  onConnectionReady(callback) {
    if (typeof callback === 'function') {
      this.connectionReadyCallbacks.push(callback);
    }
  },

  // 执行所有连接就绪回调
  executeConnectionReadyCallbacks() {
    // 如果连接已就绪，执行所有回调
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.connectionReadyCallbacks.forEach((callback, index) => {
        try {
          callback();
        } catch (error) {
          console.error(`执行连接就绪回调 ${index} 时出错:`, error);
        }
      });
      
      // 清空回调列表
      this.connectionReadyCallbacks = [];
    }
  },

  // 处理响应消息
  handleMessage(data) {
    // 检测pong消息并更新状态
    if (!this.pongReceived && data === 'pong') {
      console.log('WS首次收到pong响应');
      this.pongReceived = true;
    }

    // 判断data是否是JSON格式, 非JSON格式则直接返回
    if (!isValidJson(data)) {
      return;
    }
  
    const jsonData = JSON.parse(data);
    const type = jsonData.type;
    const arg = jsonData.arg;
    console.log('开始处理Message消息类型:', jsonData.type);

    // 执行操作
    executeResponseAction(type, arg);
    console.log('WS响应成功');
  },

  // 安全关闭连接
  close() {
    if(this.socket) {
      this.socket.close(1000, '客户端主动关闭连接');
    }
  },

  // 重连机制
  reconnect() {
    console.log(`WS尝试重连`);

    // 防止重复重连
    if (this.reconnectLock) {
      return;
    }

    // 3S后执行重连
    this.reconnectLock = true;
    this.reconnectTimer = setTimeout(() => {
      this.reconnectLock = false;
      this.init(this.url);
    }, 3*1000);
  },

  // 重置及开启心跳检测
  heartbeat() {
    this.resetHeartbeat();
    this.startHeartbeat();
  },
  resetHeartbeat() {
    this.pingTimer && clearTimeout(this.pingTimer);
    this.pongTimer && clearTimeout(this.pongTimer);
  },
  startHeartbeat() {
    // 若未首次收到pong，则开启定时发送心跳；若已收到pong，则通过超时触发心跳
    if (!this.pongReceived) {
      this.pingTimer = setInterval(() => {
        this.send('ping');
      }, this.pingTimeout);
    } else {
      this.pingTimer = setTimeout(() => {
        this.send('ping');
  
        this.pongTimer = setTimeout(() => {
        //   this.socket.close(1000, '响应超时关闭连接');
        }, this.pongTimeout);
      }, this.pingTimeout);
    }
  }
};