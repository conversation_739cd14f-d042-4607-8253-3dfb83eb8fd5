

// 微信sdk相关处理
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem(window.LOCAL_STORAGE_CACHE_KEY_TOKEN);
    if (!token) {
        console.error('[DOMContentLoaded] 当前页面需要登录后才能访问');
        return;
    }

    // const pageUrl = `${window.API_URL_BASE_VDI}/${window.URL_PATH}/wxDebug.html`;
    const jsApiList = ['checkJsApi', 'closeWindow', 'getLocation'];

    try {
        await initWxConfig(jsApiList);

        wx.ready(function () {
            console.log('[wx.ready]');
            wx.checkJsApi({
                jsApiList: jsApiList,
                success: function (res) {
                    console.log('[wx.checkJsApi] success, res:', res);
                },
                fail: function (res) {
                    console.log('[wx.checkJsApi] fail, res:', res);
                },
                complete: function (res) {
                    console.log('[wx.checkJsApi] complete, res:', res);
                },
            });
        });

        wx.error(function (res) {
            console.log('[wx.error] res:', res);
            weui.toast('微信配置失败', 2000);
        });
    } catch (error) {
        console.error('微信初始化失败:', error);
        weui.toast('微信初始化失败', 2000);
    }

    // 关闭窗口按钮点击事件
    document.getElementById('wxCloseWindow').addEventListener('click', function (e) {
        e.preventDefault();
        wx.closeWindow();
    });

    // 获取地理位置按钮点击事件
    document.getElementById('wxGetLocation').addEventListener('click', async function (e) {
        e.preventDefault();
        await initWxConfig(jsApiList);
        wx.getLocation({
            type: 'wgs84',
            success: function (res) {
                console.log('获取位置成功：', res);
                weui.toast('获取位置成功', 2000);
                alert(
                    `位置信息：\n纬度：${res.latitude}\n经度：${res.longitude}\n速度：${res.speed}\n精确度：${res.accuracy}`,
                );
            },
            fail: function (res) {
                console.error('获取位置失败：', res);
                weui.toast('获取位置失败', 2000);
            },
        });
    });
});

