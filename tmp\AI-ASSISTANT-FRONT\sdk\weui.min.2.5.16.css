/*!
 * WeUI v2.5.16 (https://github.com/weui/weui)
 * Copyright 2023 Tencent, Inc.
 * Licensed under the MIT license
 */
 .wx-root,body {
    --weui-BTN-DISABLED-FONT-COLOR: rgba(0,0,0,.2)
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BTN-DISABLED-FONT-COLOR: hsla(0,0%,100%,.2)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BTN-DISABLED-FONT-COLOR:hsla(0,0%,100%,.2)
    }
}

.wx-root,body {
    --weui-BTN-DEFAULT-BG: #f2f2f2
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BTN-DEFAULT-BG: hsla(0,0%,100%,.08)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BTN-DEFAULT-BG:hsla(0,0%,100%,.08)
    }
}

.wx-root,body {
    --weui-BTN-DEFAULT-COLOR: #06ae56
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BTN-DEFAULT-COLOR: hsla(0,0%,100%,.8)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BTN-DEFAULT-COLOR:hsla(0,0%,100%,.8)
    }
}

.wx-root,body {
    --weui-BTN-DEFAULT-ACTIVE-BG: #e6e6e6
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0,0%,100%,.126)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BTN-DEFAULT-ACTIVE-BG:hsla(0,0%,100%,.126)
    }
}

.wx-root,body {
    --weui-BTN-ACTIVE-MASK: rgba(0,0,0,.1)
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BTN-ACTIVE-MASK: hsla(0,0%,100%,.05)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BTN-ACTIVE-MASK:hsla(0,0%,100%,.05)
    }
}

.wx-root[data-weui-mode=care],body[data-weui-mode=care] {
    --weui-BTN-DEFAULT-COLOR: #018942
}

.wx-root[data-weui-mode=care][data-weui-theme=dark],body[data-weui-mode=care][data-weui-theme=dark] {
    --weui-BTN-DEFAULT-COLOR: hsla(0,0%,100%,.8)
}

@media (prefers-color-scheme: dark) {
    .wx-root[data-weui-mode=care]:not([data-weui-theme=light]),body[data-weui-mode=care]:not([data-weui-theme=light]) {
        --weui-BTN-DEFAULT-COLOR:hsla(0,0%,100%,.8)
    }
}

.wx-root,body {
    --weui-DIALOG-LINE-COLOR: rgba(0,0,0,.1)
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-DIALOG-LINE-COLOR: hsla(0,0%,100%,.1)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-DIALOG-LINE-COLOR:hsla(0,0%,100%,.1)
    }
}

html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    line-height: 1.6;
    font-family: system-ui,-apple-system,Helvetica Neue,sans-serif
}

* {
    margin: 0;
    padding: 0;
    outline: 0
}

a img {
    border: 0
}

a {
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

input,textarea {
    caret-color: #07c160;
    caret-color: var(--weui-BRAND)
}

::-webkit-input-placeholder {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

::placeholder {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.wx-root,body {
    --weui-BG-0: #ededed;
    --weui-BG-1: #f7f7f7;
    --weui-BG-2: #fff;
    --weui-BG-3: #f7f7f7;
    --weui-BG-4: #4c4c4c;
    --weui-BG-5: #fff;
    --weui-FG-0: rgba(0,0,0,.9);
    --weui-FG-HALF: rgba(0,0,0,.9);
    --weui-FG-1: rgba(0,0,0,.5);
    --weui-FG-2: rgba(0,0,0,.3);
    --weui-FG-3: rgba(0,0,0,.1);
    --weui-FG-4: rgba(0,0,0,.15);
    --weui-RED: #fa5151;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #fa9d3b;
    --weui-YELLOW: #ffc300;
    --weui-GREEN: #91d300;
    --weui-LIGHTGREEN: #95ec69;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1485ee;
    --weui-PURPLE: #6467f0;
    --weui-WHITE: #fff;
    --weui-LINK: #576b95;
    --weui-TEXTGREEN: #06ae56;
    --weui-FG: #000;
    --weui-BG: #fff;
    --weui-TAG-TEXT-ORANGE: #fa9d3b;
    --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,.1);
    --weui-TAG-TEXT-GREEN: #06ae56;
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,.1);
    --weui-TAG-TEXT-BLUE: #10aeff;
    --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,.1);
    --weui-TAG-TEXT-BLACK: rgba(0,0,0,.5);
    --weui-TAG-BACKGROUND-BLACK: rgba(0,0,0,.05)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BG-0:#111;
        --weui-BG-1: #1e1e1e;
        --weui-BG-2: #191919;
        --weui-BG-3: #202020;
        --weui-BG-4: #404040;
        --weui-BG-5: #2c2c2c;
        --weui-FG-0: hsla(0,0%,100%,.8);
        --weui-FG-HALF: hsla(0,0%,100%,.6);
        --weui-FG-1: hsla(0,0%,100%,.5);
        --weui-FG-2: hsla(0,0%,100%,.3);
        --weui-FG-3: hsla(0,0%,100%,.1);
        --weui-FG-4: hsla(0,0%,100%,.15);
        --weui-RED: #fa5151;
        --weui-REDORANGE: #ff6146;
        --weui-ORANGE: #c87d2f;
        --weui-YELLOW: #cc9c00;
        --weui-GREEN: #74a800;
        --weui-LIGHTGREEN: #3eb575;
        --weui-BRAND: #07c160;
        --weui-BLUE: #10aeff;
        --weui-INDIGO: #1196ff;
        --weui-PURPLE: #8183ff;
        --weui-WHITE: hsla(0,0%,100%,.8);
        --weui-LINK: #7d90a9;
        --weui-TEXTGREEN: #259c5c;
        --weui-FG: #fff;
        --weui-BG: #000;
        --weui-TAG-TEXT-ORANGE: rgba(250,157,59,.6);
        --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,.1);
        --weui-TAG-TEXT-GREEN: rgba(6,174,86,.6);
        --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,.1);
        --weui-TAG-TEXT-BLUE: rgba(16,174,255,.6);
        --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,.1);
        --weui-TAG-TEXT-BLACK: hsla(0,0%,100%,.5);
        --weui-TAG-BACKGROUND-BLACK: hsla(0,0%,100%,.05)
    }
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-FG-0: hsla(0,0%,100%,.8);
    --weui-FG-HALF: hsla(0,0%,100%,.6);
    --weui-FG-1: hsla(0,0%,100%,.5);
    --weui-FG-2: hsla(0,0%,100%,.3);
    --weui-FG-3: hsla(0,0%,100%,.1);
    --weui-FG-4: hsla(0,0%,100%,.15);
    --weui-RED: #fa5151;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-WHITE: hsla(0,0%,100%,.8);
    --weui-LINK: #7d90a9;
    --weui-TEXTGREEN: #259c5c;
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-TAG-TEXT-ORANGE: rgba(250,157,59,.6);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,.1);
    --weui-TAG-TEXT-GREEN: rgba(6,174,86,.6);
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,.1);
    --weui-TAG-TEXT-BLUE: rgba(16,174,255,.6);
    --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,.1);
    --weui-TAG-TEXT-BLACK: hsla(0,0%,100%,.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0,0%,100%,.05)
}

.wx-root[data-weui-mode=care],body[data-weui-mode=care] {
    --weui-BG-0: #ededed;
    --weui-BG-1: #f7f7f7;
    --weui-BG-2: #fff;
    --weui-BG-3: #f7f7f7;
    --weui-BG-4: #4c4c4c;
    --weui-BG-5: #fff;
    --weui-FG-0: #000;
    --weui-FG-HALF: #000;
    --weui-FG-1: rgba(0,0,0,.6);
    --weui-FG-2: rgba(0,0,0,.42);
    --weui-FG-3: rgba(0,0,0,.1);
    --weui-FG-4: rgba(0,0,0,.15);
    --weui-RED: #dc3636;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #e17719;
    --weui-YELLOW: #bb8e00;
    --weui-GREEN: #4f8400;
    --weui-LIGHTGREEN: #2e8800;
    --weui-BRAND: #018942;
    --weui-BLUE: #007dbb;
    --weui-INDIGO: #0075e2;
    --weui-PURPLE: #6265f1;
    --weui-WHITE: #fff;
    --weui-LINK: #576b95;
    --weui-TEXTGREEN: #06ae56;
    --weui-FG: #000;
    --weui-BG: #fff;
    --weui-TAG-TEXT-ORANGE: #e17719;
    --weui-TAG-BACKGROUND-ORANGE: rgba(225,119,25,.1);
    --weui-TAG-TEXT-GREEN: #06ae56;
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,.1);
    --weui-TAG-TEXT-BLUE: #007dbb;
    --weui-TAG-BACKGROUND-BLUE: rgba(0,125,187,.1);
    --weui-TAG-TEXT-BLACK: rgba(0,0,0,.5);
    --weui-TAG-BACKGROUND-BLACK: rgba(0,0,0,.05)
}

@media (prefers-color-scheme: dark) {
    .wx-root[data-weui-mode=care]:not([data-weui-theme=light]),body[data-weui-mode=care]:not([data-weui-theme=light]) {
        --weui-BG-0:#111;
        --weui-BG-1: #1e1e1e;
        --weui-BG-2: #191919;
        --weui-BG-3: #202020;
        --weui-BG-4: #404040;
        --weui-BG-5: #2c2c2c;
        --weui-FG-0: hsla(0,0%,100%,.85);
        --weui-FG-HALF: hsla(0,0%,100%,.65);
        --weui-FG-1: hsla(0,0%,100%,.55);
        --weui-FG-2: hsla(0,0%,100%,.35);
        --weui-FG-3: hsla(0,0%,100%,.1);
        --weui-FG-4: hsla(0,0%,100%,.15);
        --weui-RED: #fa5151;
        --weui-REDORANGE: #ff6146;
        --weui-ORANGE: #c87d2f;
        --weui-YELLOW: #cc9c00;
        --weui-GREEN: #74a800;
        --weui-LIGHTGREEN: #3eb575;
        --weui-BRAND: #07c160;
        --weui-BLUE: #10aeff;
        --weui-INDIGO: #1196ff;
        --weui-PURPLE: #8183ff;
        --weui-WHITE: hsla(0,0%,100%,.8);
        --weui-LINK: #7d90a9;
        --weui-TEXTGREEN: #259c5c;
        --weui-FG: #fff;
        --weui-BG: #000;
        --weui-TAG-TEXT-ORANGE: rgba(250,157,59,.6);
        --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,.1);
        --weui-TAG-TEXT-GREEN: rgba(6,174,86,.6);
        --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,.1);
        --weui-TAG-TEXT-BLUE: rgba(16,174,255,.6);
        --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,.1);
        --weui-TAG-TEXT-BLACK: hsla(0,0%,100%,.5);
        --weui-TAG-BACKGROUND-BLACK: hsla(0,0%,100%,.05)
    }
}

.wx-root[data-weui-mode=care][data-weui-theme=dark],body[data-weui-mode=care][data-weui-theme=dark] {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-FG-0: hsla(0,0%,100%,.85);
    --weui-FG-HALF: hsla(0,0%,100%,.65);
    --weui-FG-1: hsla(0,0%,100%,.55);
    --weui-FG-2: hsla(0,0%,100%,.35);
    --weui-FG-3: hsla(0,0%,100%,.1);
    --weui-FG-4: hsla(0,0%,100%,.15);
    --weui-RED: #fa5151;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-WHITE: hsla(0,0%,100%,.8);
    --weui-LINK: #7d90a9;
    --weui-TEXTGREEN: #259c5c;
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-TAG-TEXT-ORANGE: rgba(250,157,59,.6);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,.1);
    --weui-TAG-TEXT-GREEN: rgba(6,174,86,.6);
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,.1);
    --weui-TAG-TEXT-BLUE: rgba(16,174,255,.6);
    --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,.1);
    --weui-TAG-TEXT-BLACK: hsla(0,0%,100%,.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0,0%,100%,.05)
}

.wx-root,body {
    --weui-BG-COLOR-ACTIVE: #ececec
}

.wx-root[data-weui-theme=dark],body[data-weui-theme=dark] {
    --weui-BG-COLOR-ACTIVE: #373737
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]),body:not([data-weui-theme=light]) {
        --weui-BG-COLOR-ACTIVE:#373737
    }
}

[class*=" weui-icon-"][class*=" weui-icon-"],[class*=" weui-icon-"][class^=weui-icon-],[class^=weui-icon-][class*=" weui-icon-"],[class^=weui-icon-][class^=weui-icon-] {
    display: inline-block;
    vertical-align: middle;
    font-size: 10px;
    width: 2.4em;
    height: 2.4em;
    -webkit-mask-position: 50% 50%;
    mask-position: 50% 50%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    background-color: currentColor
}

.weui-icon-circle {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-download {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M11.25%2012.04l-1.72-1.72-1.06%201.06%202.828%202.83a1%201%200%20001.414-.001l2.828-2.828-1.06-1.061-1.73%201.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55%200%20.999.446.999.996v13.008a.998.998%200%2001-.996.996H4.996A.998.998%200%20014%2021.004V7.996A1%201%200%20014.999%207h6.251z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M11.25%2012.04l-1.72-1.72-1.06%201.06%202.828%202.83a1%201%200%20001.414-.001l2.828-2.828-1.06-1.061-1.73%201.73V7h-1.5v5.04zm0-5.04V2h1.5v5h6.251c.55%200%20.999.446.999.996v13.008a.998.998%200%2001-.996.996H4.996A.998.998%200%20014%2021.004V7.996A1%201%200%20014.999%207h6.251z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-info {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.75-12v7h1.5v-7h-1.5zM12%209a1%201%200%20100-2%201%201%200%20000%202z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.75-12v7h1.5v-7h-1.5zM12%209a1%201%200%20100-2%201%201%200%20000%202z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-safe-success {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.6C315.5%2046.7%20180.4%2093.1%2057.6%20132c0%20129.3.2%20231.7.2%20339.7%200%20304.2%20248.3%20471.6%20443.1%20523.7C695.7%20943.3%20944%20775.9%20944%20471.7c0-108%20.2-210.4.2-339.7C821.4%2093.1%20686.3%2046.7%20500.9%204.6zm248.3%20349.1l-299.7%20295c-2.1%202-5.3%202-7.4-.1L304.4%20506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3%205-2.8%207.2-1l112.2%2086c2.3%201.8%206%201.7%208.1-.1l274.7-228.9c2.2-1.8%205.7-1.7%207.7.3l17%2016.8c2.2%202.1%202.2%205.3.2%207.4z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23070202%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.6C315.5%2046.7%20180.4%2093.1%2057.6%20132c0%20129.3.2%20231.7.2%20339.7%200%20304.2%20248.3%20471.6%20443.1%20523.7C695.7%20943.3%20944%20775.9%20944%20471.7c0-108%20.2-210.4.2-339.7C821.4%2093.1%20686.3%2046.7%20500.9%204.6zm248.3%20349.1l-299.7%20295c-2.1%202-5.3%202-7.4-.1L304.4%20506.1c-2-2.1-2.3-5.7-.6-8l18.3-24.9c1.7-2.3%205-2.8%207.2-1l112.2%2086c2.3%201.8%206%201.7%208.1-.1l274.7-228.9c2.2-1.8%205.7-1.7%207.7.3l17%2016.8c2.2%202.1%202.2%205.3.2%207.4z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23070202%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-safe-warn {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.5c-185.4%2042-320.4%2088.4-443.2%20127.3%200%20129.3.2%20231.7.2%20339.6%200%20304.1%20248.2%20471.4%20443%20523.6%20194.7-52.2%20443-219.5%20443-523.6%200-107.9.2-210.3.2-339.6C821.3%2092.9%20686.2%2046.5%20500.9%204.5zm-26.1%20271.1h52.1c5.8%200%2010.3%204.7%2010.1%2010.4l-11.6%20313.8c-.1%202.8-2.5%205.2-5.4%205.2h-38.2c-2.9%200-5.3-2.3-5.4-5.2L464.8%20286c-.2-5.8%204.3-10.4%2010-10.4zm26.1%20448.3c-20.2%200-36.5-16.3-36.5-36.5s16.3-36.5%2036.5-36.5%2036.5%2016.3%2036.5%2036.5-16.4%2036.5-36.5%2036.5z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23020202%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%201000%201000%22%3E%3Cpath%20d%3D%22M500.9%204.5c-185.4%2042-320.4%2088.4-443.2%20127.3%200%20129.3.2%20231.7.2%20339.6%200%20304.1%20248.2%20471.4%20443%20523.6%20194.7-52.2%20443-219.5%20443-523.6%200-107.9.2-210.3.2-339.6C821.3%2092.9%20686.2%2046.5%20500.9%204.5zm-26.1%20271.1h52.1c5.8%200%2010.3%204.7%2010.1%2010.4l-11.6%20313.8c-.1%202.8-2.5%205.2-5.4%205.2h-38.2c-2.9%200-5.3-2.3-5.4-5.2L464.8%20286c-.2-5.8%204.3-10.4%2010-10.4zm26.1%20448.3c-20.2%200-36.5-16.3-36.5-36.5s16.3-36.5%2036.5-36.5%2036.5%2016.3%2036.5%2036.5-16.4%2036.5-36.5%2036.5z%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20fill%3D%22%23020202%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success-circle {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm-1.172-6.242l5.809-5.808.848.849-5.95%205.95a1%201%200%2001-1.414%200L7%2012.426l.849-.849%202.98%202.98z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm-1.172-6.242l5.809-5.808.848.849-5.95%205.95a1%201%200%2001-1.414%200L7%2012.426l.849-.849%202.98%202.98z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success-no-circle {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-waiting {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.75%2011.38V6h-1.5v6l4.243%204.243%201.06-1.06-3.803-3.804zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.75%2011.38V6h-1.5v6l4.243%204.243%201.06-1.06-3.803-3.804zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-waiting-circle {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.6%2011.503l3.891%203.891-.848.849L11.4%2012V6h1.2v5.503zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.6%2011.503l3.891%203.891-.848.849L11.4%2012V6h1.2v5.503zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-warn {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.763-15.864l.11%207.596h1.305l.11-7.596h-1.525zm.759%2010.967c.512%200%20.902-.383.902-.882%200-.5-.39-.882-.902-.882a.878.878%200%2000-.896.882c0%20.499.396.882.896.882z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-.763-15.864l.11%207.596h1.305l.11-7.596h-1.525zm.759%2010.967c.512%200%20.902-.383.902-.882%200-.5-.39-.882-.902-.882a.878.878%200%2000-.896.882c0%20.499.396.882.896.882z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-info-circle {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zM11.4%2010h1.2v7h-1.2v-7zm.6-1a1%201%200%20110-2%201%201%200%20010%202z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zM11.4%2010h1.2v7h-1.2v-7zm.6-1a1%201%200%20110-2%201%201%200%20010%202z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-cancel {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill-rule%3D%22evenodd%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%20fill-rule%3D%22nonzero%22%2F%3E%3Cpath%20d%3D%22M12.849%2012l3.11%203.111-.848.849L12%2012.849l-3.111%203.11-.849-.848L11.151%2012l-3.11-3.111.848-.849L12%2011.151l3.111-3.11.849.848L12.849%2012z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20fill-rule%3D%22evenodd%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6z%22%20fill-rule%3D%22nonzero%22%2F%3E%3Cpath%20d%3D%22M12.849%2012l3.11%203.111-.848.849L12%2012.849l-3.111%203.11-.849-.848L11.151%2012l-3.11-3.111.848-.849L12%2011.151l3.111-3.11.849.848L12.849%2012z%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E)
}

.weui-icon-search {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M16.31%2015.561l4.114%204.115-.848.848-4.123-4.123a7%207%200%2011.857-.84zM16.8%2011a5.8%205.8%200%2010-11.6%200%205.8%205.8%200%200011.6%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M16.31%2015.561l4.114%204.115-.848.848-4.123-4.123a7%207%200%2011.857-.84zM16.8%2011a5.8%205.8%200%2010-11.6%200%205.8%205.8%200%200011.6%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-clear {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M13.06%2012l3.006-3.005-1.06-1.06L12%2010.938%208.995%207.934l-1.06%201.06L10.938%2012l-3.005%203.005%201.06%201.06L12%2013.062l3.005%203.005%201.06-1.06L13.062%2012zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M13.06%2012l3.006-3.005-1.06-1.06L12%2010.938%208.995%207.934l-1.06%201.06L10.938%2012l-3.005%203.005%201.06%201.06L12%2013.062l3.005%203.005%201.06-1.06L13.062%2012zM12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm1.999-6.563L10.68%2012%2014%208.562%2012.953%207.5%209.29%2011.277a1.045%201.045%200%20000%201.446l3.663%203.777L14%2015.437z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm1.999-6.563L10.68%2012%2014%208.562%2012.953%207.5%209.29%2011.277a1.045%201.045%200%20000%201.446l3.663%203.777L14%2015.437z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-delete {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M6.774%206.4l.812%2013.648a.8.8%200%2000.798.752h7.232a.8.8%200%2000.798-.752L17.226%206.4H6.774zm11.655%200l-.817%2013.719A2%202%200%200115.616%2022H8.384a2%202%200%2001-1.996-1.881L5.571%206.4H3.5v-.7a.5.5%200%2001.5-.5h16a.5.5%200%2001.5.5v.7h-2.071zM14%203a.5.5%200%2001.5.5v.7h-5v-.7A.5.5%200%200110%203h4zM9.5%209h1.2l.5%209H10l-.5-9zm3.8%200h1.2l-.5%209h-1.2l.5-9z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M6.774%206.4l.812%2013.648a.8.8%200%2000.798.752h7.232a.8.8%200%2000.798-.752L17.226%206.4H6.774zm11.655%200l-.817%2013.719A2%202%200%200115.616%2022H8.384a2%202%200%2001-1.996-1.881L5.571%206.4H3.5v-.7a.5.5%200%2001.5-.5h16a.5.5%200%2001.5.5v.7h-2.071zM14%203a.5.5%200%2001.5.5v.7h-5v-.7A.5.5%200%200110%203h4zM9.5%209h1.2l.5%209H10l-.5-9zm3.8%200h1.2l-.5%209h-1.2l.5-9z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success-no-circle-thin {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.864%2016.617l-5.303-5.303-1.061%201.06%205.657%205.657a1%201%200%20001.414%200L21.238%206.364l-1.06-1.06L8.864%2016.616z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.864%2016.617l-5.303-5.303-1.061%201.06%205.657%205.657a1%201%200%20001.414%200L21.238%206.364l-1.06-1.06L8.864%2016.616z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-arrow {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-arrow-bold {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20height%3D%2224%22%20width%3D%2212%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10.157%2012.711L4.5%2018.368l-1.414-1.414%204.95-4.95-4.95-4.95L4.5%205.64l5.657%205.657a1%201%200%20010%201.414z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20height%3D%2224%22%20width%3D%2212%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10.157%2012.711L4.5%2018.368l-1.414-1.414%204.95-4.95-4.95-4.95L4.5%205.64l5.657%205.657a1%201%200%20010%201.414z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back-arrow {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M3.343%2012l7.071%207.071L9%2020.485l-7.778-7.778a1%201%200%20010-1.414L9%203.515l1.414%201.414L3.344%2012z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M3.343%2012l7.071%207.071L9%2020.485l-7.778-7.778a1%201%200%20010-1.414L9%203.515l1.414%201.414L3.344%2012z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back-arrow-thin {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-close {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2010.586l5.657-5.657%201.414%201.414L13.414%2012l5.657%205.657-1.414%201.414L12%2013.414l-5.657%205.657-1.414-1.414L10.586%2012%204.929%206.343%206.343%204.93%2012%2010.586z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2010.586l5.657-5.657%201.414%201.414L13.414%2012l5.657%205.657-1.414%201.414L12%2013.414l-5.657%205.657-1.414-1.414L10.586%2012%204.929%206.343%206.343%204.93%2012%2010.586z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-close-thin {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-back-circle {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm1.999-5.363L12.953%2016.5%209.29%2012.723a1.045%201.045%200%20010-1.446L12.953%207.5%2014%208.563%2010.68%2012%2014%2015.438z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm0-1.2a8.8%208.8%200%20100-17.6%208.8%208.8%200%20000%2017.6zm1.999-5.363L12.953%2016.5%209.29%2012.723a1.045%201.045%200%20010-1.446L12.953%207.5%2014%208.563%2010.68%2012%2014%2015.438z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-icon-success {
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-icon-waiting {
    color: #10aeff;
    color: var(--weui-BLUE)
}

.weui-icon-warn {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-icon-info {
    color: #10aeff;
    color: var(--weui-BLUE)
}

.weui-icon-success-circle,.weui-icon-success-no-circle,.weui-icon-success-no-circle-thin {
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-icon-waiting-circle {
    color: #10aeff;
    color: var(--weui-BLUE)
}

.weui-icon-circle {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.weui-icon-download {
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-icon-info-circle {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.weui-icon-safe-success {
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-icon-safe-warn {
    color: #ffc300;
    color: var(--weui-YELLOW)
}

.weui-icon-cancel {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-icon-search {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-icon-clear {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.weui-icon-clear:active {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-icon-delete.weui-icon_gallery-delete {
    color: #fff;
    color: var(--weui-WHITE)
}

.weui-icon-arrow-bold.weui-icon-arrow,.weui-icon-arrow-bold.weui-icon-arrow-bold,.weui-icon-arrow-bold.weui-icon-back-arrow,.weui-icon-arrow-bold.weui-icon-back-arrow-thin,.weui-icon-arrow.weui-icon-arrow,.weui-icon-arrow.weui-icon-arrow-bold,.weui-icon-arrow.weui-icon-back-arrow,.weui-icon-arrow.weui-icon-back-arrow-thin,.weui-icon-back-arrow-thin.weui-icon-arrow,.weui-icon-back-arrow-thin.weui-icon-arrow-bold,.weui-icon-back-arrow-thin.weui-icon-back-arrow,.weui-icon-back-arrow-thin.weui-icon-back-arrow-thin,.weui-icon-back-arrow.weui-icon-arrow,.weui-icon-back-arrow.weui-icon-arrow-bold,.weui-icon-back-arrow.weui-icon-back-arrow,.weui-icon-back-arrow.weui-icon-back-arrow-thin {
    width: 1.2em
}

.weui-icon-arrow,.weui-icon-arrow-bold {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.weui-icon-back,.weui-icon-back-arrow,.weui-icon-back-arrow-thin,.weui-icon-back-circle {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-icon_msg.weui-icon_msg {
    width: 6.4em;
    height: 6.4em
}

.weui-icon_msg.weui-icon_msg.weui-icon-warn {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-icon_msg.weui-icon_msg.weui-icon-info-circle {
    color: #10aeff;
    color: var(--weui-BLUE)
}

.weui-icon_msg-primary.weui-icon_msg-primary {
    width: 6.4em;
    height: 6.4em
}

.weui-icon_msg-primary.weui-icon_msg-primary.weui-icon-warn {
    color: #ffc300;
    color: var(--weui-YELLOW)
}

.weui-hidden_abs {
    opacity: 0;
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden
}

.weui-a11y_ref {
    display: none
}

.weui-hidden-space:empty:before {
    content: "\00A0";
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden
}

.weui-a11y-combo {
    position: relative
}

.weui-a11y-combo__helper {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.weui-a11y-combo__content {
    position: relative;
    z-index: 1
}

.weui-wa-hotarea-el {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    min-width: 44px;
    min-height: 44px;
    width: 100%;
    height: 100%
}

.weui-wa-hotarea,.weui-wa-hotarea-el__wrp,.weui-wa-hotarea_before {
    position: relative
}

.weui-wa-hotarea-el__wrp a,.weui-wa-hotarea-el__wrp button,.weui-wa-hotarea-el__wrp navigator,.weui-wa-hotarea_before a,.weui-wa-hotarea_before button,.weui-wa-hotarea_before navigator,.weui-wa-hotarea a,.weui-wa-hotarea button,.weui-wa-hotarea navigator {
    position: relative;
    z-index: 1
}

.weui-wa-hotarea:after,.weui-wa-hotarea_before:before {
    content: "";
    pointer-events: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    min-width: 44px;
    min-height: 44px;
    width: 100%;
    height: 100%
}

.weui-link {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-link,.weui-link:visited {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-link:active {
    opacity: .5
}

.weui-btn {
    position: relative;
    display: block;
    width: 184px;
    margin-left: auto;
    margin-right: auto;
    padding: 8px 24px;
    box-sizing: border-box;
    font-weight: 700;
    font-size: 17px;
    text-align: center;
    text-decoration: none;
    color: #fff;
    line-height: 1.88235294;
    border-radius: 8px;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-user-select: none;
    user-select: none
}

.weui-btn:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,.1);
    background-color: var(--weui-BTN-ACTIVE-MASK);
    border-radius: 8px
}

.weui-btn:not(.weui-btn_disabled):not(.weui-btn_loading):active:before,.weui-btn:not([disabled]):not(.weui-btn_loading):active:before {
    content: ""
}

.weui-btn_block {
    width: auto
}

.weui-btn_inline {
    display: inline-block
}

.weui-btn_default {
    background-color: #f2f2f2;
    background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-btn_default,.weui-btn_default:not(.weui-btn_disabled):visited {
    color: #06ae56;
    color: var(--weui-BTN-DEFAULT-COLOR)
}

.weui-btn_primary {
    background-color: #07c160;
    background-color: var(--weui-BRAND)
}

.weui-btn_primary:not(.weui-btn_disabled):visited {
    color: #fff
}

.weui-btn_warn {
    background-color: #f2f2f2;
    background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-btn_warn,.weui-btn_warn:not(.weui-btn_disabled):visited {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-btn[disabled],.weui-btn_disabled {
    color: rgba(0,0,0,.2);
    color: var(--weui-BTN-DISABLED-FONT-COLOR);
    background-color: #f2f2f2;
    background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-btn_loading .weui-loading {
    margin: -.2em 8px 0 0
}

.weui-btn_loading .weui-mask-loading {
    margin: -.2em 8px 0 0;
    color: currentColor
}

.weui-btn_loading .weui-primary-loading {
    margin: -.2em 8px 0 0;
    vertical-align: middle;
    color: currentColor
}

.weui-btn_loading .weui-primary-loading:before {
    content: ""
}

.weui-btn_loading.weui-btn_primary {
    color: #fff;
    color: var(--weui-WHITE)
}

.weui-btn_cell {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    font-size: 17px;
    text-align: center;
    text-decoration: none;
    color: #fff;
    line-height: 1.41176471;
    padding: 16px;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    overflow: hidden;
    background-color: #fff;
    background-color: var(--weui-BG-5)
}

.weui-btn_cell+.weui-btn_cell {
    margin-top: 16px
}

.weui-btn_cell:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-btn_cell__icon {
    display: inline-block;
    vertical-align: middle;
    width: 24px;
    height: 24px;
    margin: -.2em .34em 0 0
}

.weui-btn_cell-default {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-btn_cell-primary {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-btn_cell-warn {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-bottom-fixed-opr-page {
    height: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column
}

.weui-bottom-fixed-opr-page__content {
    min-height: 0;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding-bottom: 80px;
    box-sizing: border-box;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch
}

.weui-bottom-fixed-opr {
    padding: 16px 32px 24px;
    padding: 16px calc(32px + constant(safe-area-inset-right)) calc(24px + constant(safe-area-inset-bottom)) calc(32px + constant(safe-area-inset-left));
    padding: 16px calc(32px + env(safe-area-inset-right)) calc(24px + env(safe-area-inset-bottom)) calc(32px + env(safe-area-inset-left));
    background: #fff;
    position: relative
}

.weui-bottom-fixed-opr:before {
    content: "";
    height: 80px;
    background: -webkit-linear-gradient(bottom,#fff,hsla(0,0%,100%,0));
    background: linear-gradient(0deg,#fff,hsla(0,0%,100%,0));
    position: absolute;
    bottom: calc(100% - 1px);
    left: 0;
    right: 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.wx-root[data-weui-theme=dark] .weui-bottom-fixed-opr,body[data-weui-theme=dark] .weui-bottom-fixed-opr {
    background: #191919
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-bottom-fixed-opr,body:not([data-weui-theme=light]) .weui-bottom-fixed-opr {
        background:#191919
    }
}

.wx-root[data-weui-theme=dark] .weui-bottom-fixed-opr:before,body[data-weui-theme=dark] .weui-bottom-fixed-opr:before {
    background: -webkit-linear-gradient(bottom,#191919,rgba(25,25,25,0));
    background: linear-gradient(0deg,#191919,rgba(25,25,25,0))
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-bottom-fixed-opr:before,body:not([data-weui-theme=light]) .weui-bottom-fixed-opr:before {
        background:-webkit-linear-gradient(bottom,#191919,rgba(25,25,25,0));
        background: linear-gradient(0deg,#191919,rgba(25,25,25,0))
    }
}

.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn {
    width: 184px;
    padding-left: 16px;
    padding-right: 16px
}

.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2),.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2)+.weui-btn {
    margin: 0 8px;
    width: 136px
}

.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2)+.weui-btn:first-child,.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2):first-child {
    margin-left: 0
}

.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2)+.weui-btn:last-child,.weui-bottom-fixed-opr-page .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2):last-child {
    margin-right: 0
}

.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column
}

.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2),.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2)+.weui-btn {
    width: 184px;
    margin: 16px 0 0
}

.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2)+.weui-btn:first-child,.weui-bottom-fixed-opr-page_btn-wrap .weui-bottom-fixed-opr .weui-btn:nth-last-child(n+2):first-child {
    margin-top: 0
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed {
    padding: 0
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed .weui-half-screen-dialog__hd {
    padding: 0 24px;
    padding: 0 calc(24px + constant(safe-area-inset-right)) 0 calc(24px + constant(safe-area-inset-left));
    padding: 0 calc(24px + env(safe-area-inset-right)) 0 calc(24px + env(safe-area-inset-left))
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed .weui-half-screen-dialog__bd {
    padding-bottom: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed .weui-half-screen-dialog__ft {
    padding: 0
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed .weui-bottom-fixed-opr-page {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-height: 0
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed .weui-bottom-fixed-opr-page__content {
    padding: 0 24px;
    padding: 0 calc(24px + constant(safe-area-inset-right)) 0 calc(24px + constant(safe-area-inset-left));
    padding: 0 calc(24px + env(safe-area-inset-right)) 0 calc(24px + env(safe-area-inset-left))
}

.weui-half-screen-dialog.weui-half-screen-dialog_bottom-fixed .weui-bottom-fixed-opr {
    padding: 16px 0 64px;
    padding: 16px 0 calc(64px + constant(safe-area-inset-bottom));
    padding: 16px 0 calc(64px + env(safe-area-inset-bottom))
}

button.weui-btn,input.weui-btn {
    border-width: 0;
    outline: 0;
    -webkit-appearance: none
}

button.weui-btn:focus,input.weui-btn:focus {
    outline: 0
}

button.weui-btn_inline,button.weui-btn_mini,input.weui-btn_inline,input.weui-btn_mini {
    width: auto
}

.weui-btn_mini {
    line-height: 1.375;
    padding: 5px 12px;
    font-size: 16px;
    border-radius: 6px
}

.weui-btn_mini,.weui-btn_xmini {
    display: inline-block;
    width: auto
}

.weui-btn_xmini {
    padding: 4px 12px;
    line-height: 1.42857;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px
}

.weui-btn+.weui-btn {
    margin-top: 16px
}

.weui-btn.weui-btn_mini+.weui-btn.weui-btn_mini,.weui-btn.weui-btn_xmini+.weui-btn.weui-btn_xmini {
    margin-top: auto
}

.weui-btn.weui-btn_inline+.weui-btn.weui-btn_inline {
    margin-left: 16px
}

.weui-btn-area {
    margin: 48px 16px 8px
}

.weui-btn-area_inline {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-btn-area_inline .weui-btn {
    margin-top: auto;
    margin-right: 16px;
    width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.weui-btn-area_inline .weui-btn:last-child {
    margin-right: 0
}

.weui-btn_reset {
    font-size: inherit
}

.weui-btn_icon,.weui-btn_reset {
    background: transparent;
    border: 0;
    padding: 0;
    outline: 0
}

.weui-btn_icon {
    font-size: 0
}

.weui-btn_icon:active [class*=weui-icon-] {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-cells {
    margin-top: 8px;
    background-color: #fff;
    background-color: var(--weui-BG-2);
    overflow: hidden;
    position: relative
}

.weui-cells:before {
    top: 0;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-cells:after,.weui-cells:before {
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    z-index: 2
}

.weui-cells:after {
    bottom: 0;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-cells__title {
    margin-top: 16px;
    margin-bottom: 3px;
    padding-left: 16px;
    padding-right: 16px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    font-size: 14px;
    line-height: 1.4
}

.weui-cells__title+.weui-cells {
    margin-top: 0
}

.weui-cells__tips {
    margin-top: 8px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    padding-left: 16px;
    padding-right: 16px;
    font-size: 14px;
    line-height: 1.4
}

.weui-cells__tips a,.weui-cells__tips navigator {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-cells__tips navigator {
    display: inline
}

.weui-cell {
    padding: 16px;
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    line-height: 1.41176471;
    font-size: 17px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-cell:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    left: 16px;
    z-index: 2
}

.weui-cell:first-child:before {
    display: none
}

.weui-cell_active:active:after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0,0,0,.1);
    background: var(--weui-FG-3);
    pointer-events: none
}

.weui-cell_primary {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start
}

.weui-cell__bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-width: 0
}

.weui-cell__ft {
    text-align: right;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-cell__ft button {
    vertical-align: bottom
}

.weui-cell__desc {
    font-size: 12px;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    line-height: 1.4;
    padding-top: 4px
}

.weui-cell_swiped {
    display: block;
    padding: 0
}

.weui-cell_swiped>.weui-cell__bd {
    position: relative;
    z-index: 1;
    background-color: #fff;
    background-color: var(--weui-BG-2)
}

.weui-cell_swiped>.weui-cell__ft {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    color: #fff
}

.weui-cell_swiped>.weui-cell__ft,.weui-swiped-btn {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-swiped-btn {
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    padding: 16px 1em;
    line-height: 1.41176471;
    color: inherit
}

.weui-swiped-btn_default {
    background-color: #ededed;
    background-color: var(--weui-BG-0)
}

.weui-swiped-btn_warn {
    background-color: #fa5151;
    background-color: var(--weui-RED)
}

.weui-cell_access {
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    color: inherit
}

.weui-cell_access:active:after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0,0,0,.1);
    background: var(--weui-FG-3);
    pointer-events: none
}

.weui-cell_access .weui-cell__ft {
    padding-right: 24px;
    position: relative
}

.weui-cell_access .weui-cell__ft:after {
    content: " ";
    width: 12px;
    height: 24px;
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    background-color: currentColor;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -12px
}

.weui-cell_link {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-cell_link:first-child:before {
    display: block
}

.weui-check__label {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-check__label.weui-cell_disabled,.weui-check__label.weui-cell_readonly {
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3)
}

.weui-check {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden
}

.weui-check[disabled]+.weui-icon-checked {
    opacity: .1
}

.weui-cells_radio .weui-cell__ft {
    padding-left: 16px;
    font-size: 0
}

.weui-cells_radio .weui-check+.weui-icon-checked {
    min-width: 16px;
    color: transparent
}

.weui-cells_radio .weui-check:checked+.weui-icon-checked,.weui-cells_radio .weui-check[aria-checked=true]+.weui-icon-checked {
    color: #07c160;
    color: var(--weui-BRAND);
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.657%2018.435L3%2012.778l1.414-1.414%204.95%204.95L20.678%205l1.414%201.414-12.02%2012.021a1%201%200%2001-1.415%200z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E)
}

.weui-cells_checkbox .weui-check__label:before {
    left: 55px
}

.weui-cells_checkbox .weui-cell__hd {
    padding-right: 16px;
    font-size: 0
}

.weui-cells_checkbox .weui-icon-checked {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E)
}

.weui-cells_checkbox .weui-check:checked+.weui-icon-checked,.weui-cells_checkbox .weui-check[aria-checked=true]+.weui-icon-checked {
    color: #07c160;
    color: var(--weui-BRAND);
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E)
}

.weui-label {
    display: block;
    width: 105px;
    word-wrap: break-word;
    word-break: break-all
}

.weui-input {
    width: 100%;
    border: 0;
    outline: 0;
    -webkit-appearance: none;
    background-color: transparent;
    font-size: inherit;
    color: inherit;
    height: 1.41176471em;
    line-height: 1.41176471
}

.weui-input::-webkit-inner-spin-button,.weui-input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

.weui-input:focus:not(:placeholder-shown)+.weui-btn_input-clear {
    display: inline
}

.weui-textarea {
    display: block;
    border: 0;
    resize: none;
    background: transparent;
    width: 100%;
    color: inherit;
    font-size: 1em;
    line-height: inherit;
    height: 80px;
    outline: 0
}

.weui-textarea-counter {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    text-align: right;
    font-size: 14px
}

.weui-cell_warn,.weui-cell_warn .weui-textarea-counter {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-cell_warn .weui-icon-warn {
    display: inline-block
}

.weui-cell_disabled .weui-input:disabled,.weui-cell_disabled .weui-textarea:disabled,.weui-cell_readonly .weui-input:disabled,.weui-cell_readonly .weui-textarea:disabled {
    opacity: 1;
    -webkit-text-fill-color: rgba(0,0,0,.5);
    -webkit-text-fill-color: var(--weui-FG-1)
}

.weui-cell_disabled .weui-input[disabled],.weui-cell_disabled .weui-input[readonly],.weui-cell_disabled .weui-textarea[disabled],.weui-cell_disabled .weui-textarea[readonly],.weui-cell_readonly .weui-input[disabled],.weui-cell_readonly .weui-input[readonly],.weui-cell_readonly .weui-textarea[disabled],.weui-cell_readonly .weui-textarea[readonly] {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-btn_input-clear {
    display: none;
    padding-left: 8px
}

.weui-btn_input-clear [class*=weui-icon-] {
    width: 18px
}

.weui-cells_form .weui-cell_disabled:active,.weui-cells_form .weui-cell_readonly:active,.weui-cells_form .weui-cell_switch:active,.weui-cells_form .weui-cell_vcode:active {
    background-color: transparent
}

.weui-cells_form .weui-cell__ft {
    font-size: 0
}

.weui-cells_form .weui-icon-warn {
    display: none
}

.weui-cells_form input,.weui-cells_form label[for],.weui-cells_form textarea {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-form-preview {
    position: relative;
    background-color: #fff;
    background-color: var(--weui-BG-2)
}

.weui-form-preview:before {
    top: 0;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-form-preview:after,.weui-form-preview:before {
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3)
}

.weui-form-preview:after {
    bottom: 0;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-form-preview__hd {
    position: relative;
    padding: 16px;
    text-align: right;
    line-height: 2.5em
}

.weui-form-preview__hd:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    left: 16px
}

.weui-form-preview__hd .weui-form-preview__value {
    font-style: normal;
    font-size: 1.6em
}

.weui-form-preview__bd {
    padding: 16px;
    font-size: .9em;
    text-align: right;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    line-height: 2
}

.weui-form-preview__ft {
    position: relative;
    line-height: 50px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-form-preview__ft:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-DIALOG-LINE-COLOR);
    color: rgba(0,0,0,.1);
    color: var(--weui-DIALOG-LINE-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-form-preview__item {
    overflow: hidden
}

.weui-form-preview__label {
    float: left;
    margin-right: 1em;
    width: 4.2em;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    text-align: left
}

.weui-form-preview__value {
    display: block;
    overflow: hidden;
    word-break: normal;
    word-wrap: break-word;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-form-preview__btn {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    color: #576b95;
    color: var(--weui-LINK);
    text-align: center;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

button.weui-form-preview__btn {
    background-color: transparent;
    border: 0;
    outline: 0;
    line-height: inherit;
    font-size: inherit
}

.weui-form-preview__btn:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-form-preview__btn:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-left: 1px solid rgba(0,0,0,.1);
    border-left: 1px solid var(--weui-DIALOG-LINE-COLOR);
    color: rgba(0,0,0,.1);
    color: var(--weui-DIALOG-LINE-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

.weui-form-preview__btn:first-child:after {
    display: none
}

.weui-form-preview__btn_default {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-HALF)
}

.weui-form-preview__btn_primary {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-form-preview__list {
    padding-top: 24px;
    padding-bottom: 24px;
    line-height: 1.4;
    font-size: 14px;
    position: relative
}

.weui-form-preview__list:before {
    content: "";
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-form-preview__list:last-child {
    padding-bottom: 0
}

.weui-form-preview__list .weui-form-preview__label {
    text-align: left;
    width: 6em
}

.weui-form-preview__list .weui-form-preview__value {
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-form-preview__list .weui-form-preview__item {
    margin-top: 12px
}

.weui-form-preview__list .weui-form-preview__item:first-child,.weui-form-preview__list>.weui-cells__title:first-child {
    margin-top: 0
}

.weui-cell_select {
    padding: 0
}

.weui-cell_select .weui-cell__bd:after {
    content: " ";
    width: 12px;
    height: 24px;
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    background-color: currentColor;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    position: absolute;
    top: 50%;
    right: 16px;
    margin-top: -12px
}

.weui-select {
    -webkit-appearance: none;
    border: 0;
    outline: 0;
    background-color: transparent;
    width: 100%;
    font-size: inherit;
    min-height: 56px;
    line-height: 56px;
    position: relative;
    z-index: 1;
    padding-left: 16px;
    padding-right: 40px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    vertical-align: bottom;
    box-sizing: border-box
}

.weui-cell_select-before .weui-cell__hd {
    padding-left: 0;
    position: relative
}

.weui-cell_select-before .weui-cell__hd:after {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-right: 1px solid rgba(0,0,0,.1);
    border-right: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

.weui-cell_select-before .weui-cell__hd:before {
    content: " ";
    width: 12px;
    height: 24px;
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    background-color: currentColor;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
    position: absolute;
    top: 50%;
    right: 16px;
    margin-top: -12px
}

.weui-cell_select-before .weui-cell__bd {
    padding-left: 16px
}

.weui-cell_select-before .weui-cell__bd:after {
    display: none
}

.weui-cell_select-before .weui-select {
    max-width: 5em;
    width: 105px;
    box-sizing: content-box
}

.weui-cell_select-after .weui-cell__hd {
    padding-left: 16px
}

.weui-cell_select-after .weui-select {
    padding-left: 0
}

.weui-cell_vcode {
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0
}

.weui-vcode-btn,.weui-vcode-img {
    margin-left: 5px;
    height: 56px;
    vertical-align: middle
}

.weui-vcode-btn {
    display: inline-block;
    padding: 0 .6em 0 .7em;
    line-height: 56px;
    font-size: 17px;
    color: #576b95;
    color: var(--weui-LINK);
    position: relative
}

.weui-vcode-btn:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-left: 1px solid rgba(0,0,0,.1);
    border-left: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

button.weui-vcode-btn {
    background-color: transparent;
    border: 0;
    outline: 0
}

.weui-vcode-btn:active {
    color: var(--weui-LINK-ACTIVE)
}

.weui-gallery {
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #000;
    z-index: 1000
}

.weui-gallery__img,.weui-gallery__opr {
    position: absolute;
    left: 0;
    left: constant(safe-area-inset-left);
    left: env(safe-area-inset-left);
    right: 0;
    right: constant(safe-area-inset-right);
    right: env(safe-area-inset-right)
}

.weui-gallery__img {
    top: 0;
    top: constant(safe-area-inset-top);
    top: env(safe-area-inset-top);
    bottom: 60px;
    bottom: calc(60px + constant(safe-area-inset-bottom));
    bottom: calc(60px + env(safe-area-inset-bottom));
    width: 100%;
    background: 50% no-repeat;
    background-size: contain
}

.weui-gallery__opr {
    position: absolute;
    bottom: 0;
    background-color: #0d0d0d;
    color: #fff;
    color: var(--weui-WHITE);
    line-height: 60px;
    text-align: center
}

.weui-gallery__del {
    display: block;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom)
}

.weui-gallery__del:active {
    opacity: .5
}

.weui-cell_switch {
    padding-top: 12px;
    padding-bottom: 12px
}

.weui-cell_switch.weui-cell_disabled,.weui-cell_switch.weui-cell_readonly {
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3)
}

.weui-switch {
    -webkit-appearance: none;
    appearance: none
}

.weui-switch,.weui-switch-cp__box {
    vertical-align: bottom;
    position: relative;
    width: 52px;
    height: 32px;
    background-color: rgba(0,0,0,.1);
    background-color: var(--weui-FG-3);
    border: 0;
    padding: 2px;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    -webkit-transition: background-color .1s,border .1s;
    transition: background-color .1s,border .1s
}

.weui-switch-cp__box:after,.weui-switch:after {
    content: " ";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 28px;
    height: 28px;
    border-radius: 15px;
    background-color: #fff;
    box-shadow: 0 2px 3px 0 rgba(0,0,0,.06);
    -webkit-transition: -webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);
    transition: -webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);
    transition: transform .35s cubic-bezier(.4,.4,.25,1.35);
    transition: transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)
}

.weui-switch-cp__input:checked+.weui-switch-cp__box,.weui-switch-cp__input[aria-checked=true]+.weui-switch-cp__box,.weui-switch:checked {
    background-color: #07c160;
    background-color: var(--weui-BRAND)
}

.weui-switch-cp__input:checked+.weui-switch-cp__box:after,.weui-switch-cp__input[aria-checked=true]+.weui-switch-cp__box:after,.weui-switch:checked:after {
    -webkit-transform: translateX(20px);
    transform: translateX(20px)
}

.weui-switch-cp__input[aria-disabled=true]+.weui-switch-cp__box,.weui-switch-cp__input[disabled]+.weui-switch-cp__box,.weui-switch[disabled] {
    opacity: .1
}

.weui-switch-cp__input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    overflow: hidden
}

.weui-switch-cp__box {
    display: block
}

.weui-cell_uploader {
    padding-bottom: 24px
}

.weui-uploader {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.weui-uploader__hd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    padding-bottom: 16px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-uploader__title {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.weui-uploader__info {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.weui-uploader__bd {
    margin-bottom: -8px;
    margin-right: -8px;
    overflow: hidden
}

.weui-uploader__files {
    list-style: none
}

.weui-uploader__file {
    float: left;
    margin-right: 8px;
    margin-bottom: 8px;
    width: 96px;
    height: 96px;
    background: no-repeat 50%;
    background-size: cover
}

.weui-uploader__file_status {
    position: relative
}

.weui-uploader__file_status:before {
    content: " ";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,.5)
}

.weui-uploader__file_status .weui-uploader__file-content {
    display: block
}

.weui-uploader__file-content {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    color: #fff;
    color: var(--weui-WHITE)
}

.weui-uploader__file-content .weui-icon-warn {
    display: inline-block
}

.weui-uploader__input-box {
    float: left;
    position: relative;
    margin-right: 8px;
    margin-bottom: 8px;
    width: 96px;
    height: 96px;
    box-sizing: border-box;
    background-color: #ededed
}

.wx-root[data-weui-theme=dark] .weui-uploader__input-box,body[data-weui-theme=dark] .weui-uploader__input-box {
    background-color: #2e2e2e
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-uploader__input-box,body:not([data-weui-theme=light]) .weui-uploader__input-box {
        background-color:#2e2e2e
    }
}

.weui-uploader__input-box:after,.weui-uploader__input-box:before {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background-color: #a3a3a3
}

.wx-root[data-weui-theme=dark] .weui-uploader__input-box:after,.wx-root[data-weui-theme=dark] .weui-uploader__input-box:before,body[data-weui-theme=dark] .weui-uploader__input-box:after,body[data-weui-theme=dark] .weui-uploader__input-box:before {
    background-color: #6d6d6d
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-uploader__input-box:after,.wx-root:not([data-weui-theme=light]) .weui-uploader__input-box:before,body:not([data-weui-theme=light]) .weui-uploader__input-box:after,body:not([data-weui-theme=light]) .weui-uploader__input-box:before {
        background-color:#6d6d6d
    }
}

.weui-uploader__input-box:before {
    width: 2px;
    height: 33.33%
}

.weui-uploader__input-box:after {
    width: 33.33%;
    height: 2px
}

.weui-uploader__input-box:active:after,.weui-uploader__input-box:active:before {
    opacity: .7
}

.weui-uploader__input {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-msg__desc-primary a,.weui-msg__desc a,.weui-msg__tips a {
    color: #576b95;
    color: var(--weui-LINK);
    display: inline-block;
    vertical-align: baseline
}

.weui-msg {
    padding-top: 48px;
    padding: calc(48px + constant(safe-area-inset-top)) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left);
    padding: calc(48px + env(safe-area-inset-top)) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
    text-align: center;
    line-height: 1.4;
    min-height: 100%;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    background-color: #fff;
    background-color: var(--weui-BG-2)
}

.weui-msg__icon-area {
    margin-bottom: 32px
}

.weui-msg__text-area {
    margin-bottom: 32px;
    padding: 0 32px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    line-height: 1.6;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-msg__text-area:first-child {
    padding-top: 96px
}

.weui-msg__title {
    font-weight: 500;
    font-size: 22px
}

.weui-msg__desc,.weui-msg__title {
    margin-bottom: 16px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-msg__desc {
    font-size: 17px;
    font-weight: 400
}

.weui-msg__desc-primary {
    font-size: 14px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    margin-bottom: 16px
}

.weui-msg__custom-area {
    text-align: left;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
    margin-bottom: 16px
}

.weui-msg__title+.weui-msg__custom-area {
    margin-top: 48px
}

.weui-msg__desc+.weui-msg__custom-area,.weui-msg__desc-primary+.weui-msg__custom-area {
    margin-top: 40px
}

.weui-msg__custom-area .weui-cells__group_form .weui-cells {
    margin: 0
}

.weui-msg__opr-area {
    margin-bottom: 16px
}

.weui-msg__opr-area .weui-btn-area {
    margin: 0
}

.weui-msg__opr-area .weui-btn+.weui-btn {
    margin-bottom: 16px
}

.weui-msg__opr-area:last-child {
    margin-bottom: 96px
}

.weui-msg__opr-area+.weui-msg__extra-area {
    margin-top: 48px
}

.weui-msg__tips-area {
    margin-bottom: 16px;
    padding: 0 40px;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-msg__opr-area+.weui-msg__tips-area {
    margin-bottom: 48px
}

.weui-msg__tips-area:last-child {
    margin-bottom: 64px
}

.weui-msg__tips {
    font-size: 14px
}

.weui-msg__extra-area,.weui-msg__tips {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-msg__extra-area {
    margin-bottom: 24px;
    padding: 0 32px;
    box-sizing: border-box;
    font-size: 12px
}

.weui-msg__extra-area a,.weui-msg__extra-area navigator {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-msg__extra-area navigator {
    display: inline
}

.weui-msg_align-top .weui-msg__text-area:first-child {
    padding-top: 0
}

body,page {
    --weui-STEPS-DEFAULT-COLOR: var(--weui-FG-3);
    --weui-STEPS-HIGHLIGHT-COLOR: var(--weui-BRAND);
    --weui-STEPS-FONT-SIZE: 17;
    --weui-STEPS-LINEHEIGHT: 1.4;
    --weui-STEPS-DOT-SIZE: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    --weui-STEPS-ICON-SIZE: 40;
    --weui-STEPS-VERTICAL-DOT-GAP: calc((1em - var(--weui-STEPS-DOT-SIZE)) / 2);
    --weui-STEPS-HORIZONAL-DOT-GAP: 4px
}

.weui-steps {
    line-height: 1.4;
    line-height: var(--weui-STEPS-LINEHEIGHT);
    font-size: 17px;
    font-size: calc(1px * var(--weui-STEPS-FONT-SIZE))
}

.weui-steps__item__desc,.weui-steps__item__title {
    display: block
}

.weui-steps__item__title {
    font-weight: 500
}

.weui-steps__item__desc {
    font-size: 14px;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    margin-top: 4px
}

.weui-steps_vertical {
    position: relative
}

.weui-steps_vertical .weui-steps__item {
    position: relative;
    padding-bottom: 32px
}

.weui-steps_vertical .weui-steps__item:before {
    content: "";
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-left: 1px solid var(--weui-FG-3);
    border-left: 1px solid var(--weui-STEPS-DEFAULT-COLOR);
    color: var(--weui-FG-3);
    color: var(--weui-STEPS-DEFAULT-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5);
    top: 1.2em;
    top: calc((var(--weui-STEPS-LINEHEIGHT) - (var(--weui-STEPS-LINEHEIGHT) - 1) / 2) * 1em);
    bottom: -0.2em;
    bottom: calc((var(--weui-STEPS-LINEHEIGHT) - 1) / 2 * -1em)
}

.weui-steps_vertical .weui-steps__item:first-child:not(.weui-steps__item_success) .weui-steps__item__inner:before {
    background-color: var(--weui-BRAND);
    background-color: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_vertical .weui-steps__item:last-child:before {
    display: none
}

.weui-steps_vertical .weui-steps__item__inner {
    position: relative;
    z-index: 1;
    padding-left: 36px
}

.weui-steps_vertical .weui-steps__item__inner:before {
    content: "";
    width: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    width: var(--weui-STEPS-DOT-SIZE);
    height: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    height: var(--weui-STEPS-DOT-SIZE);
    border-radius: 100%;
    background-color: var(--weui-FG-3);
    background-color: var(--weui-STEPS-DEFAULT-COLOR)
}

.weui-steps_vertical .weui-steps__icon,.weui-steps_vertical .weui-steps__item__inner:before {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0.7em;
    top: calc(var(--weui-STEPS-LINEHEIGHT) / 2 * 1em);
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.weui-steps_vertical .weui-steps__icon {
    font-size: 17px;
    font-size: calc(1px * var(--weui-STEPS-FONT-SIZE));
    width: 2.35294em;
    width: calc(var(--weui-STEPS-ICON-SIZE) / var(--weui-STEPS-FONT-SIZE) * 1em);
    height: 2.35294em;
    height: calc(var(--weui-STEPS-ICON-SIZE) / var(--weui-STEPS-FONT-SIZE) * 1em);
    margin-top: 0.39647em;
    margin-top: calc((var(--weui-STEPS-ICON-SIZE) / var(--weui-STEPS-FONT-SIZE) * 1em - 1em) / 2 - .28em)
}

.weui-steps_vertical .weui-steps__item_icon:before {
    top: calc(((1em - var(--weui-STEPS-DOT-SIZE)) / 2) + 40 / 17 * 1em - .14em);
    top: calc(var(--weui-STEPS-VERTICAL-DOT-GAP) + var(--weui-STEPS-ICON-SIZE) / var(--weui-STEPS-FONT-SIZE) * 1em - .14em)
}

.weui-steps_vertical .weui-steps__item_icon .weui-steps__item__inner:before {
    display: none
}

.weui-steps_vertical .weui-steps__item_icon-prev:before {
    bottom: calc(((1em - var(--weui-STEPS-DOT-SIZE)) / 2) - 0.4 / 2 * 1em + .14em);
    bottom: calc(var(--weui-STEPS-VERTICAL-DOT-GAP) - (var(--weui-STEPS-LINEHEIGHT) - 1) / 2 * 1em + .14em)
}

.weui-steps_vertical .weui-steps__item_success:before {
    border-color: var(--weui-BRAND);
    border-color: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_vertical .weui-steps__item_success+.weui-steps__item .weui-steps__item__inner:before,.weui-steps_vertical .weui-steps__item_success .weui-steps__item__inner:before {
    background-color: var(--weui-BRAND);
    background-color: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_horizonal,.weui-steps_horizonal .weui-steps__item {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-steps_horizonal .weui-steps__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-steps_horizonal .weui-steps__item:before {
    content: "";
    display: block;
    width: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    width: var(--weui-STEPS-DOT-SIZE);
    height: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    height: var(--weui-STEPS-DOT-SIZE);
    border-radius: 100%;
    background-color: var(--weui-FG-3);
    background-color: var(--weui-STEPS-DEFAULT-COLOR);
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.weui-steps_horizonal .weui-steps__item:after {
    content: "";
    height: .5px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    margin: 0 4px;
    margin: 0 var(--weui-STEPS-HORIZONAL-DOT-GAP);
    background: var(--weui-FG-3);
    background: var(--weui-STEPS-DEFAULT-COLOR)
}

.weui-steps_horizonal .weui-steps__item:last-child {
    -webkit-box-flex: 0;
    -webkit-flex: none;
    flex: none
}

.weui-steps_horizonal .weui-steps__item:last-child:after {
    display: none
}

.weui-steps_horizonal .weui-steps__item:first-child:not(.weui-steps__item_success):before {
    background: var(--weui-BRAND);
    background: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_horizonal .weui-steps__item__inner {
    margin-left: 8px
}

.weui-steps_horizonal .weui-steps__item_success+.weui-steps__item:before,.weui-steps_horizonal .weui-steps__item_success:after,.weui-steps_horizonal .weui-steps__item_success:before {
    background: var(--weui-BRAND);
    background: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_horizonal-primary {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-steps_horizonal-primary .weui-steps__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    position: relative
}

.weui-steps_horizonal-primary .weui-steps__item:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid var(--weui-FG-3);
    border-top: 1px solid var(--weui-STEPS-DEFAULT-COLOR);
    color: var(--weui-FG-3);
    color: var(--weui-STEPS-DEFAULT-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-steps_horizonal-primary .weui-steps__item:last-child {
    -webkit-box-flex: 0;
    -webkit-flex: none;
    flex: none
}

.weui-steps_horizonal-primary .weui-steps__item:last-child:before {
    display: none
}

.weui-steps_horizonal-primary .weui-steps__item__inner {
    position: relative;
    padding-top: 36px
}

.weui-steps_horizonal-primary .weui-steps__item__inner:before {
    content: "";
    position: absolute;
    z-index: 1;
    width: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    width: var(--weui-STEPS-DOT-SIZE);
    height: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    height: var(--weui-STEPS-DOT-SIZE);
    border-radius: 100%;
    background-color: var(--weui-FG-3);
    background-color: var(--weui-STEPS-DEFAULT-COLOR);
    top: 0;
    left: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.weui-steps_horizonal-primary .weui-steps__item__inner:after {
    content: "";
    background-color: #fff;
    background-color: var(--weui-BG-2);
    width: calc((8 / var(--weui-STEPS-FONT-SIZE) * 1em) + 2 * 4px);
    width: calc(var(--weui-STEPS-DOT-SIZE) + 2 * var(--weui-STEPS-HORIZONAL-DOT-GAP));
    height: calc((8 / var(--weui-STEPS-FONT-SIZE) * 1em) + 2 * 4px);
    height: calc(var(--weui-STEPS-DOT-SIZE) + 2 * var(--weui-STEPS-HORIZONAL-DOT-GAP));
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transform: translate(calc(-50% + (8 / var(--weui-STEPS-FONT-SIZE) * 1em) / 2),-50%);
    -webkit-transform: translate(calc(-50% + var(--weui-STEPS-DOT-SIZE) / 2),-50%);
    transform: translate(calc(-50% + (8 / var(--weui-STEPS-FONT-SIZE) * 1em) / 2),-50%);
    transform: translate(calc(-50% + var(--weui-STEPS-DOT-SIZE) / 2),-50%)
}

.weui-steps_horizonal-primary .weui-steps__item_success:before {
    border-color: var(--weui-BRAND);
    border-color: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_horizonal-primary .weui-steps__item_success+.weui-steps__item .weui-steps__item__inner:before,.weui-steps_horizonal-primary .weui-steps__item_success .weui-steps__item__inner:before {
    background: var(--weui-BRAND);
    background: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

.weui-steps_horizonal-center {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    text-align: center
}

.weui-steps_horizonal-center .weui-steps__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    position: relative
}

.weui-steps_horizonal-center .weui-steps__item:after,.weui-steps_horizonal-center .weui-steps__item:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid var(--weui-FG-3);
    border-top: 1px solid var(--weui-STEPS-DEFAULT-COLOR);
    color: var(--weui-FG-3);
    color: var(--weui-STEPS-DEFAULT-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-steps_horizonal-center .weui-steps__item:before {
    right: 50%
}

.weui-steps_horizonal-center .weui-steps__item:after {
    left: 50%
}

.weui-steps_horizonal-center .weui-steps__item:first-child:before,.weui-steps_horizonal-center .weui-steps__item:last-child:after {
    display: none
}

.weui-steps_horizonal-center .weui-steps__item__inner {
    position: relative;
    z-index: 1;
    padding-top: 36px
}

.weui-steps_horizonal-center .weui-steps__item__inner:before {
    content: "";
    position: absolute;
    z-index: 1;
    width: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    width: var(--weui-STEPS-DOT-SIZE);
    height: calc(8 / var(--weui-STEPS-FONT-SIZE) * 1em);
    height: var(--weui-STEPS-DOT-SIZE);
    border-radius: 100%;
    background-color: var(--weui-FG-3);
    background-color: var(--weui-STEPS-DEFAULT-COLOR);
    top: 0;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.weui-steps_horizonal-center .weui-steps__item__inner:after {
    content: "";
    background-color: #fff;
    background-color: var(--weui-BG-2);
    width: calc((8 / var(--weui-STEPS-FONT-SIZE) * 1em) + 2 * 4px);
    width: calc(var(--weui-STEPS-DOT-SIZE) + 2 * var(--weui-STEPS-HORIZONAL-DOT-GAP));
    height: calc((8 / var(--weui-STEPS-FONT-SIZE) * 1em) + 2 * 4px);
    height: calc(var(--weui-STEPS-DOT-SIZE) + 2 * var(--weui-STEPS-HORIZONAL-DOT-GAP));
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.weui-steps_horizonal-center .weui-steps__item_success+.weui-steps__item .weui-steps__item__inner:before,.weui-steps_horizonal-center .weui-steps__item_success+.weui-steps__item:before,.weui-steps_horizonal-center .weui-steps__item_success .weui-steps__item__inner:before,.weui-steps_horizonal-center .weui-steps__item_success:after,.weui-steps_horizonal-center .weui-steps__item_success:before {
    background: var(--weui-BRAND);
    background: var(--weui-STEPS-HIGHLIGHT-COLOR)
}

body,page {
    --weui-cellMarginLR: 16px;
    --weui-cellPaddingLR: 16px
}

.weui-cells__group {
    border: 0
}

.weui-cells__group:first-child {
    margin-top: 0
}

.weui-cells__group_form {
    margin-top: 24px
}

.weui-cells__group_form .weui-cells {
    margin-left: 16px;
    margin-left: var(--weui-cellMarginLR);
    margin-right: 16px;
    margin-right: var(--weui-cellMarginLR)
}

.weui-cells__group_form .weui-cells:after,.weui-cells__group_form .weui-cells:before {
    left: 16px;
    left: var(--weui-cellPaddingLR);
    right: 16px;
    right: var(--weui-cellPaddingLR)
}

.weui-cells__group_form .weui-cell {
    padding: 16px;
    padding: 16px var(--weui-cellPaddingLR)
}

.weui-cells__group_form .weui-cell:before {
    left: 16px;
    left: var(--weui-cellPaddingLR);
    right: 16px;
    right: var(--weui-cellPaddingLR)
}

.weui-cells__group_form .weui-cell__hd {
    padding-right: 16px
}

.weui-cells__group_form .weui-cell__ft {
    padding-left: 16px
}

.weui-cells__group_form .weui-cells__title {
    margin-top: 24px;
    margin-bottom: 8px;
    padding: 0 32px
}

.weui-cells__group_form:first-child .weui-cells__title {
    margin-top: 0
}

.weui-cells__group_form .weui-cells__tips {
    margin-top: 8px;
    padding: 0 32px;
    padding: 0 calc(var(--weui-cellMarginLR) + var(--weui-cellPaddingLR));
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2)
}

.weui-cells__group_form .weui-cells__tips a {
    font-weight: 700
}

.weui-cells__group_form .weui-cells__tips_warn {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-cells__group_form .weui-label {
    max-width: 5em;
    margin-right: 8px
}

.weui-cells__group_form .weui-cell_access:active:after,.weui-cells__group_form .weui-cell_active:active:after {
    border-radius: 8px
}

.weui-cells__group_form .weui-cell_warn input {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-cells__group_form .weui-cell_disabled:active:after,.weui-cells__group_form .weui-cell_readonly:active:after,.weui-cells__group_form .weui-cell_switch:active:after,.weui-cells__group_form .weui-cell_vcode:active:after,.weui-cells__group_form .weui-icon-warn {
    display: none
}

.weui-cells__group_form input,.weui-cells__group_form label[for],.weui-cells__group_form textarea {
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-cells__group_form .weui-cell_wrap {
    -webkit-box-align: initial;
    -webkit-align-items: initial;
    align-items: initial;
    padding-top: 8px;
    padding-bottom: 8px
}

.weui-cells__group_form .weui-cell_wrap .weui-cell__hd {
    padding-right: 0
}

.weui-cells__group_form .weui-cell_wrap .weui-label {
    margin-top: 8px
}

.weui-cells__group_form .weui-cell_wrap .weui-cell__bd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-cells__group_form .weui-cell__control {
    margin: 8px 0 8px 16px
}

.weui-cells__group_form .weui-cell__control_flex {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-width: 30vw
}

.weui-cells__group_form .weui-vcode-btn {
    font-size: 16px;
    padding: 0 12px;
    height: auto;
    width: auto;
    line-height: 2;
    border-radius: 6px;
    color: #06ae56;
    color: var(--weui-BTN-DEFAULT-COLOR);
    background-color: #f2f2f2;
    background-color: var(--weui-BTN-DEFAULT-BG)
}

.weui-cells__group_form .weui-vcode-btn:before {
    display: none
}

.weui-cells__group_form .weui-cell_vcode.weui-cell_wrap {
    padding-top: 4px;
    padding-bottom: 4px
}

.weui-cells__group_form .weui-cell_vcode.weui-cell_wrap .weui-label {
    margin-top: 12px
}

.weui-cells__group_form .weui-cell_vcode.weui-cell_wrap .weui-input {
    font-size: 17px;
    min-height: 1.88235294em
}

.weui-cells__group_form .weui-cells_checkbox .weui-check__label:before {
    left: 56px;
    left: calc(40px + var(--weui-cellPaddingLR))
}

.weui-cells__group_form .weui-cell_select {
    padding: 0
}

.weui-cells__group_form .weui-cell_select-before .weui-cell__hd {
    padding-right: 0
}

.weui-cells__group_form .weui-cell_switch {
    padding: 12px 16px
}

.weui-cells__group_form-primary {
    margin-top: 32px
}

.weui-cells__group_form-primary .weui-cells {
    background: #f7f7f7;
    background: var(--weui-BG-1);
    border-radius: 8px;
    overflow: hidden
}

.weui-cells__group_form-primary .weui-cells:after,.weui-cells__group_form-primary .weui-cells:before {
    display: none
}

.weui-cells__group_form-primary .weui-cell_access:active:after,.weui-cells__group_form-primary .weui-cell_active:active:after {
    border-radius: 0
}

.weui-form {
    padding: 56px 0 0;
    padding: calc(56px + constant(safe-area-inset-top)) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left);
    padding: calc(56px + env(safe-area-inset-top)) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    line-height: 1.4;
    min-height: 100%;
    box-sizing: border-box;
    background-color: #fff;
    background-color: var(--weui-BG-2)
}

.weui-form .weui-footer,.weui-form .weui-footer__link {
    font-size: 14px
}

.weui-form .weui-agree {
    padding: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    text-align: justify;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    line-height: 1.6;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-form .weui-agree__checkbox {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    margin-top: 0
}

.weui-form .weui-agree__text {
    min-width: 0
}

.weui-form__text-area {
    padding: 0 32px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    text-align: center
}

.weui-form__control-area {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    margin: 48px 0
}

.weui-form__extra-area,.weui-form__tips-area {
    margin-bottom: 24px;
    padding: 0 32px;
    text-align: center
}

.weui-form__extra-area {
    margin-top: 52px
}

.weui-form__opr-area {
    padding: 0 32px
}

.weui-form__opr-area:last-child {
    margin-bottom: 96px
}

.weui-form__opr-area+.weui-form__tips-area {
    margin-top: 16px;
    margin-bottom: 0
}

.weui-form__tips-area+.weui-form__extra-area {
    margin-top: 32px
}

.weui-form__tips-area:last-child {
    margin-bottom: 60px
}

.weui-form__title {
    font-size: 22px;
    font-weight: 700;
    line-height: 1.36
}

.weui-form__desc {
    font-size: 17px;
    margin-top: 16px
}

.weui-form__tips {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    font-size: 14px
}

.weui-form__tips a,.weui-form__tips navigator {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-form__tips navigator {
    display: inline
}

.weui-article {
    padding: 48px 24px;
    padding: 48px calc(24px + constant(safe-area-inset-right)) calc(48px + constant(safe-area-inset-bottom)) calc(24px + constant(safe-area-inset-left));
    padding: 48px calc(24px + env(safe-area-inset-right)) calc(48px + env(safe-area-inset-bottom)) calc(24px + env(safe-area-inset-left));
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    font-size: 17px;
    line-height: 1.6;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-article section {
    margin-bottom: 48px
}

.weui-article section section {
    margin-bottom: 32px
}

.weui-article section section section {
    margin-bottom: 24px
}

.weui-article h1,.weui-article h2,.weui-article h3,.weui-article h4,.weui-article h5,.weui-article h6 {
    line-height: 1.4
}

.weui-article h1 {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 48px;
    text-align: center
}

.weui-article h2 {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 16px
}

.weui-article h3 {
    font-size: 17px;
    font-weight: 500;
    margin-bottom: 8px
}

.weui-article h4 {
    margin-bottom: 4px
}

.weui-article h4,.weui-article h5,.weui-article h6 {
    font-size: 17px;
    font-weight: 400
}

.weui-article * {
    max-width: 100%;
    box-sizing: border-box;
    word-wrap: break-word
}

.weui-article img {
    vertical-align: bottom
}

.weui-article p {
    margin: 0 0 24px
}

.weui-article ol,.weui-article ul {
    margin-left: 1.2em;
    margin-bottom: 24px
}

.weui-article ol ol,.weui-article ol ul,.weui-article ul ol,.weui-article ul ul {
    margin: .5em 0 .5em 1.2em
}

.weui-article ol {
    list-style: decimal
}

.weui-article ul {
    list-style: disc
}

.weui-article li {
    margin: .5em 0
}

.weui-article .weui-article__list_inside {
    margin-left: 0
}

.weui-article .weui-article__list_inside li {
    list-style-position: inside
}

.weui-article .weui-article__list_none {
    margin-left: 0
}

.weui-article .weui-article__list_none li {
    list-style: none
}

.weui-tabbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    position: relative;
    z-index: 500;
    background-color: #f7f7f7;
    background-color: var(--weui-BG-1)
}

.weui-tabbar:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-tabbar__item {
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding: 8px 0;
    padding-bottom: calc(8px + constant(safe-area-inset-bottom));
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
    font-size: 0;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    text-align: center;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-tabbar__item:first-child {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left)
}

.weui-tabbar__item:last-child {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right)
}

.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon,.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon>i,.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label {
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-tabbar__icon {
    display: inline-block;
    font-size: 10px;
    width: 2.8em;
    height: 2.8em;
    margin-bottom: 2px
}

.weui-tabbar__icon>i,i.weui-tabbar__icon {
    font-size: 24px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-tabbar__icon img {
    width: 100%;
    height: 100%
}

.weui-tabbar__label {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    font-size: 10px;
    line-height: 1.4
}

.weui-navbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    position: relative;
    z-index: 500;
    background-color: #fff;
    background-color: var(--weui-BG-2);
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top)
}

.weui-navbar:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-navbar+.weui-tab__panel {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom)
}

.weui-navbar__item {
    position: relative;
    display: block;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding: 16px 0;
    padding-top: calc(16px + constant(safe-area-inset-top));
    padding-top: calc(16px + env(safe-area-inset-top));
    text-align: center;
    font-size: 17px;
    line-height: 1.41176471;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-navbar__item.weui-bar__item_on,.weui-navbar__item:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-navbar__item:after {
    content: " ";
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-right: 1px solid rgba(0,0,0,.1);
    border-right: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

.weui-navbar__item:first-child {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left)
}

.weui-navbar__item:last-child {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right)
}

.weui-navbar__item:last-child:after {
    display: none
}

.weui-tab {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    height: 100%;
    box-sizing: border-box;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column
}

.weui-tab__panel {
    box-sizing: border-box;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.weui-tab__content {
    display: none
}

.weui-progress {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-progress__bar {
    background-color: #ededed;
    background-color: var(--weui-BG-0);
    height: 3px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.weui-progress__inner-bar {
    width: 0;
    height: 100%;
    background-color: #07c160;
    background-color: var(--weui-BRAND)
}

.weui-progress__opr {
    display: block;
    margin-left: 15px;
    font-size: 0
}

.weui-panel {
    background-color: #fff;
    background-color: var(--weui-BG-2);
    margin-top: 10px;
    position: relative;
    overflow: hidden
}

.weui-panel:first-child {
    margin-top: 0
}

.weui-panel:before {
    top: 0;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-panel:after,.weui-panel:before {
    content: " ";
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3)
}

.weui-panel:after {
    bottom: 0;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-panel .weui-cells:after {
    display: none
}

.weui-panel__hd {
    padding: 16px 16px 13px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    font-size: 15px;
    font-weight: 500;
    position: relative
}

.weui-panel__hd:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    left: 15px
}

.weui-media-box {
    padding: 16px;
    position: relative
}

.weui-media-box:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    left: 16px
}

.weui-media-box:first-child:before {
    display: none
}

a.weui-media-box {
    color: #000;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

a.weui-media-box:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-media-box__title {
    display: block;
    font-weight: 400;
    font-size: 17px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    width: auto;
    white-space: nowrap;
    word-wrap: normal
}

.weui-media-box__desc,.weui-media-box__title {
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-media-box__desc {
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    font-size: 14px;
    padding-top: 4px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2
}

.weui-media-box__info {
    display: block;
    margin-top: 16px;
    padding-bottom: 4px;
    font-size: 13px;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    line-height: 1em;
    list-style: none;
    overflow: hidden
}

.weui-media-box__info__meta {
    float: left;
    padding-right: 1em
}

.weui-media-box__info__meta_extra {
    padding-left: 1em;
    border-left: 1px solid rgba(0,0,0,.3);
    border-left: 1px solid var(--weui-FG-2)
}

.weui-media-box_appmsg {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-media-box_appmsg .weui-media-box__hd {
    margin-right: 16px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center
}

.weui-media-box_appmsg .weui-media-box__thumb {
    width: 100%;
    max-height: 100%;
    vertical-align: top
}

.weui-media-box_appmsg .weui-media-box__bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-width: 0
}

.weui-media-box_small-appmsg {
    padding: 0
}

.weui-media-box_small-appmsg .weui-cells {
    margin-top: 0
}

.weui-media-box_small-appmsg .weui-cells:before {
    display: none
}

.weui-grids {
    position: relative;
    overflow: hidden
}

.weui-grids:before {
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-grids:after,.weui-grids:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3)
}

.weui-grids:after {
    width: 1px;
    bottom: 0;
    border-left: 1px solid rgba(0,0,0,.1);
    border-left: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

.weui-grid {
    position: relative;
    float: left;
    padding: 20px 10px;
    width: 33.33333333%;
    box-sizing: border-box
}

.weui-grid:before {
    top: 0;
    width: 1px;
    border-right: 1px solid rgba(0,0,0,.1);
    border-right: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

.weui-grid:after,.weui-grid:before {
    content: " ";
    position: absolute;
    right: 0;
    bottom: 0;
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3)
}

.weui-grid:after {
    left: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-grid:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-grid__icon {
    width: 28px;
    height: 28px;
    margin: 0 auto
}

.weui-grid__icon img {
    display: block;
    width: 100%;
    height: 100%
}

.weui-grid__icon+.weui-grid__label {
    margin-top: 4px
}

.weui-grid__label {
    display: block;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden
}

.weui-footer,.weui-grid__label {
    text-align: center;
    font-size: 14px
}

.weui-footer {
    color: rgba(0,0,0,.2);
    line-height: 1.4
}

.wx-root[data-weui-theme=dark] .weui-footer,body[data-weui-theme=dark] .weui-footer {
    color: hsla(0,0%,100%,.2)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-footer,body:not([data-weui-theme=light]) .weui-footer {
        color:hsla(0,0%,100%,.2)
    }
}

.weui-footer a,.weui-footer navigator {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-footer navigator {
    display: inline
}

.weui-footer_fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding-top: 16px;
    padding-bottom: 16px;
    padding-bottom: calc(16px + constant(safe-area-inset-bottom));
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
    left: constant(safe-area-inset-left);
    left: env(safe-area-inset-left);
    right: constant(safe-area-inset-right);
    right: env(safe-area-inset-right)
}

.weui-footer__links {
    font-size: 0
}

.weui-footer__link {
    display: inline-block;
    vertical-align: top;
    margin: 0 8px;
    position: relative;
    font-size: 14px
}

.weui-footer__link:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-left: 1px solid rgba(0,0,0,.1);
    border-left: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5);
    left: -8px;
    top: .36em;
    bottom: .36em
}

.weui-footer__link:first-child:before {
    display: none
}

.weui-footer__text {
    padding: 0 16px;
    font-size: 12px
}

.weui-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-flex__item {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-width: 0
}

.weui-dialog {
    position: fixed;
    z-index: 5000;
    top: 50%;
    left: 16px;
    right: 16px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background-color: #fff;
    background-color: var(--weui-BG-2);
    text-align: center;
    border-radius: 12px;
    overflow: hidden;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    max-height: 90%;
    outline: 0
}

.weui-dialog__hd {
    padding: 32px 24px 16px
}

.weui-dialog__title {
    font-weight: 700;
    font-size: 17px;
    line-height: 1.4;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-dialog__bd {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 24px;
    margin-bottom: 32px;
    font-size: 17px;
    line-height: 1.4;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-dialog__bd:first-child {
    min-height: 40px;
    padding: 32px 24px 0;
    font-weight: 700;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    -webkit-flex-direction: column;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.weui-dialog__bd:first-child,.weui-dialog__ft {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.weui-dialog__ft {
    position: relative
}

.weui-dialog__ft:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-DIALOG-LINE-COLOR);
    color: rgba(0,0,0,.1);
    color: var(--weui-DIALOG-LINE-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-dialog__btn {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    display: block;
    line-height: 1.41176471;
    padding: 16px 0;
    font-size: 17px;
    color: #576b95;
    color: var(--weui-LINK);
    font-weight: 700;
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-user-select: none;
    user-select: none;
    position: relative;
    overflow: hidden
}

.weui-dialog__btn:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-dialog__btn:after {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 1px;
    bottom: 0;
    border-left: 1px solid rgba(0,0,0,.1);
    border-left: 1px solid var(--weui-DIALOG-LINE-COLOR);
    color: rgba(0,0,0,.1);
    color: var(--weui-DIALOG-LINE-COLOR);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleX(.5);
    transform: scaleX(.5)
}

.weui-dialog__btn:first-child:after {
    display: none
}

.weui-dialog__btn_default {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-HALF)
}

.weui-skin_android .weui-dialog {
    text-align: left;
    box-shadow: 0 6px 30px 0 rgba(0,0,0,.1)
}

.weui-skin_android .weui-dialog__title {
    font-size: 22px;
    line-height: 1.4
}

.weui-skin_android .weui-dialog__hd {
    text-align: left
}

.weui-skin_android .weui-dialog__bd {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    text-align: left
}

.weui-skin_android .weui-dialog__bd:first-child {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-skin_android .weui-dialog__ft {
    display: block;
    text-align: right;
    line-height: 40px;
    min-height: 40px;
    padding: 0 24px 16px
}

.weui-skin_android .weui-dialog__ft:after {
    display: none
}

.weui-skin_android .weui-dialog__btn {
    display: inline-block;
    vertical-align: top;
    padding: 0 .8em
}

.weui-skin_android .weui-dialog__btn:after {
    display: none
}

.weui-skin_android .weui-dialog__btn:last-child {
    margin-right: -.8em
}

.weui-skin_android .weui-dialog__btn_default {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-HALF)
}

@media screen and (min-width: 352px) {
    .weui-dialog {
        width:320px;
        margin: 0 auto
    }
}

.weui-half-screen-dialog {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    min-height: 255px;
    max-height: 75%;
    z-index: 5000;
    line-height: 1.4;
    background-color: #fff;
    background-color: var(--weui-BG-2);
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
    padding: 0 24px;
    padding: 0 calc(24px + constant(safe-area-inset-right)) constant(safe-area-inset-bottom) calc(24px + constant(safe-area-inset-left));
    padding: 0 calc(24px + env(safe-area-inset-right)) env(safe-area-inset-bottom) calc(24px + env(safe-area-inset-left));
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    outline: 0
}

@media only screen and (max-device-height: 558px) {
    .weui-half-screen-dialog {
        max-height:calc(100% - 16px)
    }
}

.weui-half-screen-dialog__hd {
    min-height: 64px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.weui-half-screen-dialog__hd .weui-btn_icon {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: inherit
}

.weui-half-screen-dialog__hd .weui-btn_icon:active {
    opacity: .5
}

.weui-half-screen-dialog__hd__side {
    position: relative;
    left: -8px
}

.weui-half-screen-dialog__hd__main {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.weui-half-screen-dialog__hd__side+.weui-half-screen-dialog__hd__main {
    text-align: center;
    padding: 0 40px
}

.weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side {
    right: -8px;
    left: auto
}

.weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side .weui-btn_icon,.weui-half-screen-dialog__hd__main+.weui-half-screen-dialog__hd__side .weui-icon-btn {
    right: 0
}

.weui-half-screen-dialog__title {
    display: block;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    font-weight: 500;
    font-size: 15px
}

.weui-half-screen-dialog__subtitle {
    display: block;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    font-size: 10px
}

.weui-half-screen-dialog__bd {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
    padding-bottom: 56px;
    font-size: 14px;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-half-screen-dialog__desc {
    font-size: 17px;
    font-weight: 700;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    line-height: 1.4
}

.weui-half-screen-dialog__tips {
    padding-top: 16px;
    font-size: 14px;
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    line-height: 1.4
}

.weui-half-screen-dialog__ft {
    padding: 0 0 64px;
    text-align: center
}

.weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2),.weui-half-screen-dialog__ft .weui-btn:nth-last-child(n+2)+.weui-btn {
    display: inline-block;
    vertical-align: top;
    margin: 0 8px;
    width: 120px
}

.weui-half-screen-dialog__btn-area {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.weui-half-screen-dialog__btn-area .weui-btn {
    width: 184px;
    padding-left: 16px;
    padding-right: 16px
}

.weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2),.weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2)+.weui-btn {
    margin: 0 8px;
    width: 136px
}

.weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2)+.weui-btn:first-child,.weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2):first-child {
    margin-left: 0
}

.weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2)+.weui-btn:last-child,.weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2):last-child {
    margin-right: 0
}

.weui-half-screen-dialog__btn-area+.weui-half-screen-dialog__attachment-area {
    margin-top: 24px;
    margin-bottom: -34px
}

.weui-half-screen-dialog_btn-wrap .weui-half-screen-dialog__btn-area {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column
}

.weui-half-screen-dialog_btn-wrap .weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2),.weui-half-screen-dialog_btn-wrap .weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2)+.weui-btn {
    width: 184px;
    margin: 16px 0 0
}

.weui-half-screen-dialog_btn-wrap .weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2)+.weui-btn:first-child,.weui-half-screen-dialog_btn-wrap .weui-half-screen-dialog__btn-area .weui-btn:nth-last-child(n+2):first-child {
    margin-top: 0
}

.weui-half-screen-dialog_large {
    max-height: none;
    top: 16px
}

.weui-half-screen-dialog_slide .weui-half-screen-dialog__hd {
    min-height: 0;
    padding: 12px 16px 16px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.weui-half-screen-dialog_slide .weui-half-screen-dialog__slide-icon {
    position: absolute;
    top: 12px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    width: 40px;
    height: 4px;
    border-radius: 2px;
    background: #ededed;
    background: var(--weui-BG-0);
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.wx-root[data-weui-theme=dark] .weui-half-screen-dialog_slide .weui-half-screen-dialog__slide-icon,body[data-weui-theme=dark] .weui-half-screen-dialog_slide .weui-half-screen-dialog__slide-icon {
    background: rgba(0,0,0,.1);
    background: var(--weui-FG-3)
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-half-screen-dialog_slide .weui-half-screen-dialog__slide-icon,body:not([data-weui-theme=light]) .weui-half-screen-dialog_slide .weui-half-screen-dialog__slide-icon {
        background:rgba(0,0,0,.1);
        background: var(--weui-FG-3)
    }
}

.weui-half-screen-dialog_slide .weui-half-screen-dialog__slide-icon .weui-icon-arrow {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    width: .8em;
    height: 1.6em;
    opacity: 0
}

.weui-icon-more {
    -webkit-mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M5 10.25a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5z'/%3E%3C/svg%3E") no-repeat 50% 50%;
    mask: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cpath fill-opacity='.9' fill-rule='evenodd' d='M5 10.25a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5zm7 0a1.75 1.75 0 1 1 0 3.5 1.75 1.75 0 0 1 0-3.5z'/%3E%3C/svg%3E") no-repeat 50% 50%
}

.weui-icon-slide-down {
    -webkit-mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cdefs%3E    %3Crect id='dda90263-a290-4594-926f-6aba8cb4779f-a' width='24' height='24' x='0' y='0' rx='12'/%3E  %3C/defs%3E  %3Cg fill='none' fill-rule='evenodd'%3E    %3Cmask id='dda90263-a290-4594-926f-6aba8cb4779f-b' fill='%23fff'%3E      %3Cuse xlink:href='%23dda90263-a290-4594-926f-6aba8cb4779f-a'/%3E    %3C/mask%3E    %3Cuse fill='%23000' fill-opacity='.05' xlink:href='%23dda90263-a290-4594-926f-6aba8cb4779f-a'/%3E    %3Cg fill-opacity='.9' mask='url(%23dda90263-a290-4594-926f-6aba8cb4779f-b)'%3E      %3Cpath fill='%23000' d='M11.407 15.464L6.693 10.75l1.179-1.179 4.125 4.125 4.124-4.125L17.3 10.75l-4.714 4.714a.833.833 0 0 1-1.179 0z'/%3E    %3C/g%3E  %3C/g%3E%3C/svg%3E");
    mask-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='24' height='24' viewBox='0 0 24 24'%3E  %3Cdefs%3E    %3Crect id='dda90263-a290-4594-926f-6aba8cb4779f-a' width='24' height='24' x='0' y='0' rx='12'/%3E  %3C/defs%3E  %3Cg fill='none' fill-rule='evenodd'%3E    %3Cmask id='dda90263-a290-4594-926f-6aba8cb4779f-b' fill='%23fff'%3E      %3Cuse xlink:href='%23dda90263-a290-4594-926f-6aba8cb4779f-a'/%3E    %3C/mask%3E    %3Cuse fill='%23000' fill-opacity='.05' xlink:href='%23dda90263-a290-4594-926f-6aba8cb4779f-a'/%3E    %3Cg fill-opacity='.9' mask='url(%23dda90263-a290-4594-926f-6aba8cb4779f-b)'%3E      %3Cpath fill='%23000' d='M11.407 15.464L6.693 10.75l1.179-1.179 4.125 4.125 4.124-4.125L17.3 10.75l-4.714 4.714a.833.833 0 0 1-1.179 0z'/%3E    %3C/g%3E  %3C/g%3E%3C/svg%3E")
}

.weui-half-screen-dialog__hd .weui-icon-btn {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: inherit
}

.weui-half-screen-dialog__hd .weui-icon-btn:active {
    opacity: .5
}

.weui-half-screen-dialog__hd .weui-icon-btn:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    min-width: 44px;
    min-height: 44px;
    width: 100%;
    height: 100%
}

.weui-icon-btn.weui-icon-btn {
    outline: 0;
    -webkit-appearance: none;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    border-width: 0;
    background-color: transparent;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    font-size: 0;
    width: auto;
    height: auto
}

.weui-icon-btn_goback.weui-icon-btn_goback {
    width: .71rem;
    -webkit-mask: url("data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
    mask: url("data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M10%2019.438L8.955%2020.5l-7.666-7.79a1.02%201.02%200%20010-1.42L8.955%203.5%2010%204.563%202.682%2012%2010%2019.438z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
    -webkit-mask-size: 100%;
    mask-size: 100%
}

.weui-icon-btn_close.weui-icon-btn_close,.weui-icon-btn_goback.weui-icon-btn_goback {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    background-color: currentColor;
    height: 1.42rem
}

.weui-icon-btn_close.weui-icon-btn_close {
    width: 1.42rem;
    -webkit-mask: url("data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
    mask: url("data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12.25%2010.693L6.057%204.5%205%205.557l6.193%206.193L5%2017.943%206.057%2019l6.193-6.193L18.443%2019l1.057-1.057-6.193-6.193L19.5%205.557%2018.443%204.5z%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E") no-repeat 50% 50%;
    -webkit-mask-size: 100%;
    mask-size: 100%
}

.weui-toast {
    position: fixed;
    z-index: 5500;
    font-size: 10px;
    width: 13.6em;
    height: 13.6em;
    top: 40%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    text-align: center;
    border-radius: 12px;
    color: hsla(0,0%,100%,.9);
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    background-color: #4c4c4c;
    background-color: var(--weui-BG-4);
    box-sizing: border-box;
    line-height: 1.4
}

.weui-toast_text {
    width: auto;
    height: auto;
    min-width: 152px;
    max-width: 216px;
    padding: 12px 0;
    border-radius: 8px
}

.weui-toast_text .weui-toast__content {
    font-size: 14px;
    padding: 0 20px
}

.weui-icon_toast {
    display: block;
    margin-bottom: 16px
}

.weui-icon_toast.weui-icon_toast {
    width: 4em;
    height: 4em
}

.weui-icon_toast.weui-icon-success-no-circle,.weui-icon_toast.weui-icon-warn {
    color: hsla(0,0%,100%,.9)
}

.weui-icon_toast.weui-loading {
    width: 1em;
    height: 1em;
    font-size: 40px
}

.weui-icon_toast.weui-primary-loading {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    width: 1em;
    height: 1em;
    font-size: 40px;
    color: #ededed
}

.weui-icon_toast.weui-primary-loading:before {
    border-width: 4px 0 4px 4px
}

.weui-icon_toast.weui-primary-loading:after {
    border-width: 4px 4px 4px 0
}

.weui-icon_toast.weui-primary-loading .weui-primary-loading__dot {
    width: 4px;
    height: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px
}

.weui-toast__content {
    font-size: 17px;
    padding: 0 12px;
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto
}

.weui-toast_text-more .weui-icon_toast {
    margin-bottom: 12px
}

.weui-toast_text-more .weui-toast__content {
    font-size: 14px;
    line-height: 1.6
}

.weui-mask {
    background: rgba(0,0,0,.6)
}

.weui-mask,.weui-mask_transparent {
    position: fixed;
    z-index: 1000;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0
}

.weui-actionsheet {
    position: fixed;
    left: 0;
    bottom: 0;
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 5000;
    width: 100%;
    background-color: #f7f7f7;
    background-color: var(--weui-BG-1);
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s,-webkit-transform .3s;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    overflow: hidden;
    outline: 0
}

.weui-actionsheet__title {
    position: relative;
    height: 56px;
    padding: 8px 24px;
    padding: 8px calc(24px + constant(safe-area-inset-right)) 8px calc(24px + constant(safe-area-inset-left));
    padding: 8px calc(24px + env(safe-area-inset-right)) 8px calc(24px + env(safe-area-inset-left));
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    text-align: center;
    font-size: 12px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    line-height: 1.4;
    background: #fff;
    background: var(--weui-BG-2)
}

.weui-actionsheet__title:before {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-actionsheet__title .weui-actionsheet__title-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2
}

.weui-actionsheet__action,.weui-actionsheet__menu {
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    background-color: #fff;
    background-color: var(--weui-BG-2)
}

.weui-actionsheet__action {
    margin-top: 8px
}

.weui-actionsheet__action .weui-actionsheet__cell:last-child {
    padding-bottom: calc(16px + constant(safe-area-inset-bottom));
    padding-bottom: calc(16px + env(safe-area-inset-bottom))
}

.weui-actionsheet__cell {
    position: relative;
    padding: 16px;
    padding: 16px calc(16px + constant(safe-area-inset-right)) 16px calc(16px + constant(safe-area-inset-left));
    padding: 16px calc(16px + env(safe-area-inset-right)) 16px calc(16px + env(safe-area-inset-left));
    text-align: center;
    font-size: 17px;
    line-height: 1.41176471;
    overflow: hidden
}

.weui-actionsheet__cell:before {
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-actionsheet__cell:active {
    background-color: #ececec;
    background-color: var(--weui-BG-COLOR-ACTIVE)
}

.weui-actionsheet__cell:first-child:before {
    display: none
}

.weui-actionsheet__cell_warn {
    color: #fa5151;
    color: var(--weui-RED)
}

.weui-skin_android .weui-actionsheet {
    position: fixed;
    left: 50%;
    top: 50%;
    bottom: auto;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: 274px;
    box-sizing: border-box;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    background: transparent;
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s,-webkit-transform .3s;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.weui-skin_android .weui-actionsheet__action {
    display: none
}

.weui-skin_android .weui-actionsheet__menu {
    border-radius: 2px;
    box-shadow: 0 6px 30px 0 rgba(0,0,0,.1)
}

.weui-skin_android .weui-actionsheet__cell {
    padding: 16px;
    font-size: 17px;
    line-height: 1.41176471;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    text-align: left
}

.weui-skin_android .weui-actionsheet__cell:first-child {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px
}

.weui-skin_android .weui-actionsheet__cell:last-child {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px
}

.weui-actionsheet_toggle {
    -webkit-transform: translate(0);
    transform: translate(0)
}

.weui-loadmore {
    width: 65%;
    margin: 20px auto;
    text-align: center;
    font-size: 0
}

.weui-loadmore .weui-loading,.weui-loadmore .weui-primary-loading {
    margin-right: 8px
}

.weui-loadmore__tips {
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    line-height: 1.6;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

.weui-loadmore_line {
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    margin-top: 32px
}

.weui-loadmore_line .weui-loadmore__tips {
    position: relative;
    top: -.9em;
    padding: 0 8px;
    background-color: #fff;
    background-color: var(--weui-BG-2)
}

.weui-loadmore_dot .weui-loadmore__tips:before {
    content: " ";
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: rgba(0,0,0,.1);
    background-color: var(--weui-FG-3);
    display: inline-block;
    position: relative;
    vertical-align: 0;
    top: -.16em
}

.weui-badge {
    display: inline-block;
    padding: .15em .4em;
    min-width: .66666667em;
    border-radius: 18px;
    background-color: #fa5151;
    background-color: var(--weui-RED);
    color: #fff;
    line-height: 1.2;
    text-align: center;
    font-size: 12px;
    vertical-align: middle
}

.weui-badge_dot {
    padding: .4em;
    min-width: 0
}

.weui-toptips {
    display: none;
    position: fixed;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    top: 8px;
    left: 8px;
    right: 8px;
    padding: 10px;
    border-radius: 8px;
    font-size: 14px;
    text-align: center;
    color: #fff;
    z-index: 5500;
    word-wrap: break-word;
    word-break: break-all
}

.weui-toptips_warn {
    background-color: #fa5151;
    background-color: var(--weui-RED)
}

.weui-list-tips {
    list-style: none;
    padding-top: 24px;
    padding-bottom: 24px;
    line-height: 1.4;
    font-size: 14px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    position: relative
}

.weui-list-tips:before {
    content: "";
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 1px;
    border-top: 1px solid rgba(0,0,0,.1);
    border-top: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-list-tips:last-child {
    padding-bottom: 0
}

.weui-list-tips__item {
    position: relative;
    padding-left: 15px;
    margin: 16px 0
}

.weui-list-tips__item:before {
    content: "\2022";
    position: absolute;
    left: 0;
    top: -.1em
}

.weui-list-tips__item:first-child {
    margin-top: 0
}

.weui-form-preview__list+.weui-list-tips>.weui-list-tips__item:first-child {
    margin-top: 6px
}

.weui-search-bar {
    position: relative;
    padding: 8px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    box-sizing: border-box;
    background-color: #ededed;
    background-color: var(--weui-BG-0);
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-search-bar.weui-search-bar_focusing .weui-search-bar__cancel-btn {
    display: block
}

.weui-search-bar.weui-search-bar_focusing .weui-search-bar__label {
    display: none
}

.weui-search-bar .weui-icon-search {
    font-size: 10px;
    width: 1.6em;
    height: 1.6em;
    margin-left: 8px;
    margin-right: 4px;
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.weui-search-bar__form {
    position: relative;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    min-width: 0;
    background-color: #fff;
    background-color: var(--weui-BG-2);
    border-radius: 4px
}

.weui-search-bar__box {
    position: relative;
    z-index: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-search-bar__box .weui-search-bar__input {
    padding: 8px 0;
    width: 100%;
    height: 1.14285714em;
    border: 0;
    font-size: 14px;
    line-height: 1.14285714em;
    box-sizing: content-box;
    background: transparent;
    caret-color: #07c160;
    caret-color: var(--weui-BRAND);
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0)
}

.weui-search-bar__box .weui-search-bar__input:focus {
    outline: none
}

.weui-search-bar__box .weui-icon-clear {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    font-size: 10px;
    width: 2em;
    height: 2em;
    margin-left: 8px;
    -webkit-mask-size: 2em;
    mask-size: 2em;
    -webkit-mask-position: calc(100% - 8px) 0;
    mask-position: calc(100% - 8px) 0;
    min-width: 44px
}

.weui-search-bar__box .weui-icon-clear:after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    width: 44px
}

.weui-search-bar__label {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    font-size: 0;
    border-radius: 4px;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    background: #fff;
    background: var(--weui-BG-2)
}

.weui-search-bar__label span {
    display: inline-block;
    font-size: 14px;
    vertical-align: middle
}

.weui-search-bar__cancel-btn {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    display: none;
    margin-left: 8px;
    line-height: 28px;
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-search-bar__input:not(:valid)+.weui-icon-clear {
    display: none
}

input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration,input[type=search]::-webkit-search-results-button,input[type=search]::-webkit-search-results-decoration {
    display: none
}

.weui-picker {
    position: fixed;
    width: 100%;
    box-sizing: border-box;
    left: 0;
    bottom: 0;
    z-index: 5000;
    background-color: #fff;
    background-color: var(--weui-BG-2);
    padding-left: 0;
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
    padding-right: 0;
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s,-webkit-transform .3s;
    outline: 0
}

.weui-picker .weui-half-screen-dialog__hd {
    padding-left: 24px;
    padding-right: 24px
}

.weui-picker .weui-half-screen-dialog__bd {
    overflow: visible
}

.weui-picker__hd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    padding: 16px;
    padding: 16px calc(16px + constant(safe-area-inset-right)) 16px calc(16px + constant(safe-area-inset-left));
    padding: 16px calc(16px + env(safe-area-inset-right)) 16px calc(16px + env(safe-area-inset-left));
    position: relative;
    text-align: center;
    font-size: 17px;
    line-height: 1.4
}

.weui-picker__hd:after {
    content: " ";
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    border-bottom: 1px solid var(--weui-FG-3);
    color: rgba(0,0,0,.1);
    color: var(--weui-FG-3);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.weui-picker__bd {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    position: relative;
    background-color: #fff;
    background-color: var(--weui-BG-2);
    height: 240px;
    overflow: hidden
}

.weui-picker__group {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    position: relative;
    height: 100%;
    font-size: 17px
}

.weui-picker__group:first-child .weui-picker__indicator {
    left: 8px;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px
}

.weui-picker__group:last-child .weui-picker__indicator {
    right: 8px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px
}

.weui-picker__mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    z-index: 3;
    background-image: -webkit-linear-gradient(top,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6)),-webkit-linear-gradient(bottom,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6));
    background-image: linear-gradient(180deg,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6)),linear-gradient(0deg,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6));
    background-position: top,bottom;
    background-size: 100% 112px;
    background-repeat: no-repeat;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.wx-root[data-weui-theme=dark] .weui-picker__mask,body[data-weui-theme=dark] .weui-picker__mask {
    background-image: -webkit-linear-gradient(top,rgba(25,25,25,.95),rgba(25,25,25,.6)),-webkit-linear-gradient(bottom,rgba(25,25,25,.95),rgba(25,25,25,.6));
    background-image: linear-gradient(180deg,rgba(25,25,25,.95),rgba(25,25,25,.6)),linear-gradient(0deg,rgba(25,25,25,.95),rgba(25,25,25,.6))
}

@media (prefers-color-scheme: dark) {
    .wx-root:not([data-weui-theme=light]) .weui-picker__mask,body:not([data-weui-theme=light]) .weui-picker__mask {
        background-image:-webkit-linear-gradient(top,rgba(25,25,25,.95),rgba(25,25,25,.6)),-webkit-linear-gradient(bottom,rgba(25,25,25,.95),rgba(25,25,25,.6));
        background-image: linear-gradient(180deg,rgba(25,25,25,.95),rgba(25,25,25,.6)),linear-gradient(0deg,rgba(25,25,25,.95),rgba(25,25,25,.6))
    }
}

.weui-picker__indicator {
    height: 56px;
    position: absolute;
    top: 112px;
    left: 0;
    right: 0;
    z-index: 1;
    background: #f7f7f7;
    background: var(--weui-BG-3)
}

.weui-picker__content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 2
}

.weui-picker__item {
    height: 56px;
    line-height: 56px;
    text-align: center;
    color: rgba(0,0,0,.9);
    color: var(--weui-FG-0);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}

.weui-picker__item_disabled {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1)
}

@-webkit-keyframes a {
    0% {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes a {
    0% {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.weui-animate-slide-up,.weui-animate_slide-up {
    -webkit-animation: a ease .3s forwards;
    animation: a ease .3s forwards
}

@-webkit-keyframes b {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }
}

@keyframes b {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }
}

.weui-animate-slide-down,.weui-animate_slide-down {
    -webkit-animation: b ease .3s forwards;
    animation: b ease .3s forwards
}

@-webkit-keyframes c {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes c {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.weui-animate-fade-in,.weui-animate_fade-in {
    -webkit-animation: c ease .3s forwards;
    animation: c ease .3s forwards
}

@-webkit-keyframes d {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes d {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.weui-animate-fade-out,.weui-animate_fade-out {
    -webkit-animation: d ease .3s forwards;
    animation: d ease .3s forwards
}

.weui-transition.weui-mask {
    -webkit-transition: opacity .3s,visibility .3s;
    transition: opacity .3s,visibility .3s;
    opacity: 0;
    visibility: hidden
}

.weui-transition.weui-half-screen-dialog {
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s,-webkit-transform .3s;
    -webkit-transform: translateY(100%);
    transform: translateY(100%)
}

.weui-transition_show.weui-mask {
    opacity: 1;
    visibility: visible
}

.weui-transition_show.weui-half-screen-dialog {
    -webkit-transform: translateY(0);
    transform: translateY(0)
}

.weui-agree {
    display: block;
    padding: 8px 15px 0;
    font-size: 14px;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

.weui-agree a,.weui-agree navigator {
    color: #576b95;
    color: var(--weui-LINK)
}

.weui-agree navigator {
    display: inline
}

.weui-agree__text {
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    margin-left: 2px
}

.weui-agree__checkbox {
    -webkit-appearance: none;
    appearance: none;
    display: inline-block;
    border: 0;
    outline: 0;
    vertical-align: middle;
    background-color: currentColor;
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: 100%;
    mask-size: 100%;
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%221000%22%20height%3D%221000%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M500%20916.667C269.881%20916.667%2083.333%20730.119%2083.333%20500%2083.333%20269.881%20269.881%2083.333%20500%2083.333c230.119%200%20416.667%20186.548%20416.667%20416.667%200%20230.119-186.548%20416.667-416.667%20416.667zm0-50c202.504%200%20366.667-164.163%20366.667-366.667%200-202.504-164.163-366.667-366.667-366.667-202.504%200-366.667%20164.163-366.667%20366.667%200%20202.504%20164.163%20366.667%20366.667%20366.667z%22%20fill-rule%3D%22evenodd%22%20fill-opacity%3D%22.9%22%2F%3E%3C%2Fsvg%3E);
    color: rgba(0,0,0,.3);
    color: var(--weui-FG-2);
    width: 1em;
    height: 1em;
    font-size: 17px;
    margin-top: -.2em
}

.weui-agree__checkbox-check {
    opacity: 0;
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden
}

.weui-agree__checkbox-check[aria-checked=true]+.weui-agree__checkbox,.weui-agree__checkbox:checked {
    -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
    mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M12%2022C6.477%2022%202%2017.523%202%2012S6.477%202%2012%202s10%204.477%2010%2010-4.477%2010-10%2010zm-1.177-7.86l-2.765-2.767L7%2012.431l3.119%203.121a1%201%200%20001.414%200l5.952-5.95-1.062-1.062-5.6%205.6z%22%2F%3E%3C%2Fsvg%3E);
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-agree_animate {
    -webkit-animation: e .3s 1;
    animation: e .3s 1
}

@-webkit-keyframes e {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    16% {
        -webkit-transform: translateX(-8px);
        transform: translateX(-8px)
    }

    28% {
        -webkit-transform: translateX(-16px);
        transform: translateX(-16px)
    }

    44% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    59% {
        -webkit-transform: translateX(-16px);
        transform: translateX(-16px)
    }

    73% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    82% {
        -webkit-transform: translateX(16px);
        transform: translateX(16px)
    }

    94% {
        -webkit-transform: translateX(8px);
        transform: translateX(8px)
    }

    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes e {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    16% {
        -webkit-transform: translateX(-8px);
        transform: translateX(-8px)
    }

    28% {
        -webkit-transform: translateX(-16px);
        transform: translateX(-16px)
    }

    44% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    59% {
        -webkit-transform: translateX(-16px);
        transform: translateX(-16px)
    }

    73% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    82% {
        -webkit-transform: translateX(16px);
        transform: translateX(16px)
    }

    94% {
        -webkit-transform: translateX(8px);
        transform: translateX(8px)
    }

    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.weui-primary-loading {
    font-size: 16px;
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    position: relative;
    width: 1em;
    height: 1em;
    vertical-align: middle;
    color: #606060;
    -webkit-animation: f 1s steps(60) infinite;
    animation: f 1s steps(60) infinite
}

.weui-primary-loading:after,.weui-primary-loading:before {
    content: "";
    display: block;
    width: .5em;
    height: 1em;
    box-sizing: border-box;
    border: .0875em solid;
    border-color: currentColor
}

.weui-primary-loading:before {
    border-right-width: 0;
    border-top-left-radius: 1em;
    border-bottom-left-radius: 1em;
    -webkit-mask-image: -webkit-linear-gradient(top,#000 8%,rgba(0,0,0,.3) 95%)
}

.weui-primary-loading:after {
    border-left-width: 0;
    border-top-right-radius: 1em;
    border-bottom-right-radius: 1em;
    -webkit-mask-image: -webkit-linear-gradient(top,transparent 8%,rgba(0,0,0,.3) 95%)
}

.weui-primary-loading__dot {
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -.04375em;
    width: .0875em;
    height: .0875em;
    border-top-right-radius: 100%;
    border-bottom-right-radius: 100%;
    background: currentColor
}

@-webkit-keyframes f {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes f {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.weui-primary-loading_brand {
    color: #07c160;
    color: var(--weui-BRAND)
}

.weui-primary-loading_transparent {
    color: #ededed
}

.weui-loading {
    font-size: 16px;
    width: 1em;
    height: 1em;
    display: inline-block;
    vertical-align: middle;
    background: transparent url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") no-repeat;
    background-size: 100%
}

.weui-btn_loading.weui-btn_primary .weui-loading,.weui-loading.weui-icon_toast,.weui-loading.weui-loading_transparent {
    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23ededed' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23ededed' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23ededed' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23ededed' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23ededed' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A")
}

.weui-mask-loading {
    display: inline-block;
    vertical-align: middle;
    font-size: 16px;
    width: 1em;
    height: 1em;
    -webkit-mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
    mask: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='80px' height='80px' viewBox='0 0 80 80' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eloading%3C/title%3E%3Cdefs%3E%3ClinearGradient x1='94.0869141%25' y1='0%25' x2='94.0869141%25' y2='90.559082%25' id='linearGradient-1'%3E%3Cstop stop-color='%23606060' stop-opacity='0' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3ClinearGradient x1='100%25' y1='8.67370605%25' x2='100%25' y2='90.6286621%25' id='linearGradient-2'%3E%3Cstop stop-color='%23606060' offset='0%25'%3E%3C/stop%3E%3Cstop stop-color='%23606060' stop-opacity='0.3' offset='100%25'%3E%3C/stop%3E%3C/linearGradient%3E%3C/defs%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' opacity='0.9'%3E%3Cg%3E%3Cpath d='M40,0 C62.09139,0 80,17.90861 80,40 C80,62.09139 62.09139,80 40,80 L40,73 C58.2253967,73 73,58.2253967 73,40 C73,21.7746033 58.2253967,7 40,7 L40,0 Z' fill='url(%23linearGradient-1)'%3E%3C/path%3E%3Cpath d='M40,0 L40,7 C21.7746033,7 7,21.7746033 7,40 C7,58.2253967 21.7746033,73 40,73 L40,80 C17.90861,80 0,62.09139 0,40 C0,17.90861 17.90861,0 40,0 Z' fill='url(%23linearGradient-2)'%3E%3C/path%3E%3Ccircle id='Oval' fill='%23606060' cx='40.5' cy='3.5' r='3.5'%3E%3C/circle%3E%3C/g%3E%3CanimateTransform attributeName='transform' begin='0s' dur='1s' type='rotate' values='0 40 40;360 40 40' repeatCount='indefinite'/%3E%3C/g%3E%3C/svg%3E%0A") 0 0 no-repeat;
    -webkit-mask-size: cover;
    mask-size: cover;
    background-color: currentColor;
    color: #606060
}

.weui-slider {
    padding: 15px 18px;
    -webkit-user-select: none;
    user-select: none
}

.weui-slider__inner {
    position: relative;
    height: 2px;
    background-color: rgba(0,0,0,.1);
    background-color: var(--weui-FG-3)
}

.weui-slider__track {
    height: 100%;
    background-color: #07c160;
    background-color: var(--weui-BRAND);
    width: 0
}

.weui-slider__handler {
    position: absolute;
    left: 0;
    top: 50%;
    width: 28px;
    height: 28px;
    margin-left: -14px;
    margin-top: -14px;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 0 4px rgba(0,0,0,.1);
    box-shadow: 0 0 4px var(--weui-FG-3)
}

.weui-slider-box {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center
}

.weui-slider-box .weui-slider {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

.weui-slider-box__value {
    margin-left: .5em;
    min-width: 24px;
    color: rgba(0,0,0,.5);
    color: var(--weui-FG-1);
    text-align: center;
    font-size: 14px
}
