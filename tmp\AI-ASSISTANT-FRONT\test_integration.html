<!DOCTYPE html>
<html>
<head>
    <title>SDK集成测试</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .sdk-status {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .sdk-card {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .sdk-card.active {
            border-color: #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>云手机SDK集成测试</h1>
    
    <div class="test-container">
        <h2>SDK状态检查</h2>
        <div class="sdk-status">
            <div class="sdk-card" id="jhSdkStatus">
                <h3>JH SDK</h3>
                <div id="jhSdkResult" class="test-result info">检查中...</div>
            </div>
            <div class="sdk-card" id="ldSdkStatus">
                <h3>LD SDK</h3>
                <div id="ldSdkResult" class="test-result info">检查中...</div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>功能测试</h2>
        <button onclick="testJHSdk()">测试 JH SDK</button>
        <button onclick="testLDSdk()">测试 LD SDK</button>
        <button onclick="testSwitchFunction()">测试切换功能</button>
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>配置检查</h2>
        <div id="configResults"></div>
        <button onclick="checkConfig()">检查配置</button>
    </div>

    <div class="test-container">
        <h2>集成验证</h2>
        <p>请打开 <a href="index.html" target="_blank">index.html</a> 查看实际集成效果</p>
        <div class="test-result info">
            <strong>验证步骤：</strong><br>
            1. 页面右上角应该显示SDK切换按钮<br>
            2. 默认使用JH SDK<br>
            3. 点击"LD SDK"按钮可以切换到LD SDK<br>
            4. 切换时会清理当前连接并重新初始化
        </div>
    </div>

    <script src="./ld/LDSDK.min.js"></script>
    <script src="sdk/JHSDK.min.3.21.6.js"></script>
    <script src="js/config.20250814_2027.js"></script>
    <script>
        // 检查SDK是否加载成功
        function checkSdkStatus() {
            // 检查JH SDK
            const jhResult = document.getElementById('jhSdkResult');
            const jhCard = document.getElementById('jhSdkStatus');
            if (typeof JHSDK !== 'undefined') {
                jhResult.textContent = '✓ JH SDK 加载成功';
                jhResult.className = 'test-result success';
                jhCard.classList.add('active');
            } else {
                jhResult.textContent = '✗ JH SDK 加载失败';
                jhResult.className = 'test-result error';
            }

            // 检查LD SDK
            const ldResult = document.getElementById('ldSdkResult');
            const ldCard = document.getElementById('ldSdkStatus');
            if (typeof LDSDK !== 'undefined') {
                ldResult.textContent = '✓ LD SDK 加载成功';
                ldResult.className = 'test-result success';
                ldCard.classList.add('active');
            } else {
                ldResult.textContent = '✗ LD SDK 加载失败';
                ldResult.className = 'test-result error';
            }
        }

        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            results.appendChild(div);
        }

        function testJHSdk() {
            try {
                if (typeof JHSDK === 'undefined') {
                    addTestResult('JH SDK 未加载', 'error');
                    return;
                }
                addTestResult('JH SDK 可以正常实例化', 'success');
            } catch (err) {
                addTestResult('JH SDK 测试失败: ' + err.message, 'error');
            }
        }

        function testLDSdk() {
            try {
                if (typeof LDSDK === 'undefined') {
                    addTestResult('LD SDK 未加载', 'error');
                    return;
                }
                addTestResult('LD SDK 可以正常访问', 'success');
            } catch (err) {
                addTestResult('LD SDK 测试失败: ' + err.message, 'error');
            }
        }

        function testSwitchFunction() {
            addTestResult('切换功能需要在 index.html 中测试', 'info');
            addTestResult('请检查 index.html 右上角的SDK切换按钮', 'info');
        }

        function checkConfig() {
            const configResults = document.getElementById('configResults');
            configResults.innerHTML = '';

            function addConfigResult(message, type = 'info') {
                const div = document.createElement('div');
                div.className = `test-result ${type}`;
                div.textContent = message;
                configResults.appendChild(div);
            }

            // 检查配置是否加载
            if (typeof window.LD_SDK_CONFIG !== 'undefined') {
                addConfigResult('✓ LD SDK 配置已加载', 'success');
                addConfigResult(`使用测试地址: ${window.LD_SDK_CONFIG.USE_TEST_URL}`, 'info');
                addConfigResult(`测试地址: ${window.LD_SDK_CONFIG.TEST_URL}`, 'info');
                addConfigResult(`LibAV路径: ${window.LD_SDK_CONFIG.LIBAV_URL}`, 'info');
            } else {
                addConfigResult('✗ LD SDK 配置未加载', 'error');
            }

            if (typeof window.WEBSOCKET_API_URL !== 'undefined') {
                addConfigResult('✓ WebSocket API URL 已配置', 'success');
                addConfigResult(`WebSocket URL: ${window.WEBSOCKET_API_URL}`, 'info');
            } else {
                addConfigResult('✗ WebSocket API URL 未配置', 'error');
            }
        }

        // 页面加载完成后检查SDK状态
        window.onload = function() {
            checkSdkStatus();
            checkConfig();
        };
    </script>
</body>
</html>
